package com.tth.common.constant;

/**
 * Redis Key 常量统一管理类
 * 
 * 设计原则：
 * 1. 统一命名规范：使用冒号(:)分隔层级，小写字母+下划线
 * 2. 业务域分类：按功能模块分组管理
 * 3. 前缀规范：{业务域}:{子模块}:{具体功能}:
 * 4. 便于维护：所有Redis key在此统一定义和管理
 * 
 * 注意：
 * - 此类放在common包下，允许适度的业务变更
 * - framework、modules、thirdparty包都从此处获取key常量
 * - 新增业务模块的key应在对应的业务域下添加
 * 
 * <AUTHOR>
 */
public class RedisKeyConstants {

    // ========== 认证授权模块 (framework层使用) ==========
    public static class Auth {
        /** 认证模块前缀 */
        private static final String PREFIX = "auth:";
        
        /** 刷新token前缀 */
        public static final String REFRESH_TOKEN = PREFIX + "refresh_token:";
        
        /** 用户ID前缀 */
        public static final String USER_ID = PREFIX + "user_id:";
        
        /** 角色缓存前缀 */
        public static final String ROLE = PREFIX + "role:";
        
        /** 权限缓存前缀 */
        public static final String PERMISSION = PREFIX + "permission:";
        
        /** 缓存过期时间（分钟）*/
        public static final long CACHE_EXPIRE_MINUTES = 30;
    }

    // ========== 系统配置模块 (framework层使用) ==========
    public static class SystemConfig {
        /** 系统配置模块前缀 */
        private static final String PREFIX = "system:config:";
        
        /** 系统配置缓存前缀 */
        public static final String CONFIG = PREFIX + "data:";
        
        /** 系统配置单个值缓存前缀 */
        public static final String CONFIG_VALUE = PREFIX + "value:";
        
        /** 系统配置提供商所有配置缓存前缀 */
        public static final String CONFIG_PROVIDER = PREFIX + "provider:";
        
        /** 缓存过期时间（秒）- 30分钟 */
        public static final long CACHE_EXPIRE_SECONDS = 30 * 60;
    }

    // ========== 限流防护模块 (framework层使用) ==========
    public static class RateLimit {
        /** 限流模块前缀 */
        private static final String PREFIX = "rate_limit:";
        
        /** 限流前缀 */
        public static final String LIMIT = PREFIX + "limit:";
        
        /** 防重复提交前缀 */
        public static final String REPEAT_SUBMIT = PREFIX + "repeat_submit:";
    }

    // ========== 第三方服务模块 (thirdparty层使用) ==========
    public static class ThirdParty {
        /** 第三方服务模块前缀 */
        private static final String PREFIX = "third_party:";
        
        // 微信相关
        public static class Wechat {
            /** 微信模块前缀 */
            private static final String WECHAT_PREFIX = PREFIX + "wechat:";
            
            /** 微信小程序接口调用凭据前缀 */
            public static final String MINI_PROGRAM_ACCESS_TOKEN = WECHAT_PREFIX + "miniprogram:access_token:";
            
            /** 微信开放平台接口调用凭据前缀 */
            public static final String OPEN_ACCESS_TOKEN = WECHAT_PREFIX + "open:access_token:";
            
            /** 缓存过期时间（秒）- 7000秒 */
            public static final long ACCESS_TOKEN_EXPIRE_SECONDS = 7000;
        }
        
        // 支付宝相关
        public static class Alipay {
            /** 支付宝模块前缀 */
            private static final String ALIPAY_PREFIX = PREFIX + "alipay:";
            
            /** 支付宝接口调用凭据前缀 */
            public static final String ACCESS_TOKEN = ALIPAY_PREFIX + "access_token:";
        }
        
        // 火山引擎相关
        public static class Volcengine {
            /** 火山引擎模块前缀 */
            private static final String VOLCENGINE_PREFIX = PREFIX + "volcengine:";
            
            /** TTS合成任务前缀 */
            public static final String TTS_TASK = VOLCENGINE_PREFIX + "tts:task:";
            
            /** TTS访问令牌前缀 */
            public static final String TTS_ACCESS_TOKEN = VOLCENGINE_PREFIX + "tts:access_token:";
        }
    }

    // ========== 业务模块 (modules层使用) ==========
    public static class Business {
        /** 业务模块前缀 */
        private static final String PREFIX = "business:";
        
        // 童话故事相关
        public static class FairyTale {
            /** 童话故事模块前缀 */
            private static final String FAIRY_TALE_PREFIX = PREFIX + "fairy_tale:";
            
            /** 故事缓存前缀 */
            public static final String STORY = FAIRY_TALE_PREFIX + "story:";
            
            /** 故事列表缓存前缀 */
            public static final String STORY_LIST = FAIRY_TALE_PREFIX + "story_list:";
        }
        
        // 衣橱管理相关
        public static class Wardrobe {
            /** 衣橱模块前缀 */
            private static final String WARDROBE_PREFIX = PREFIX + "wardrobe:";
            
            /** 衣物缓存前缀 */
            public static final String CLOTHING = WARDROBE_PREFIX + "clothing:";
        }
        
        // 用户相关
        public static class User {
            /** 用户模块前缀 */
            private static final String USER_PREFIX = PREFIX + "user:";

            /** 用户信息缓存前缀 */
            public static final String USER_INFO = USER_PREFIX + "info:";

            /** 用户会话前缀 */
            public static final String USER_SESSION = USER_PREFIX + "session:";
        }

        // OSS文件相关
        public static class OssFile {
            /** OSS文件模块前缀 */
            private static final String OSS_FILE_PREFIX = PREFIX + "oss:file:";

            /** 文件信息缓存前缀 */
            public static final String FILE_INFO = OSS_FILE_PREFIX + "info:";

            /** 文件URL缓存前缀 */
            public static final String FILE_URL = OSS_FILE_PREFIX + "url:";

            /** 文件信息缓存过期时间（秒）- 7天 */
            public static final long FILE_INFO_CACHE_EXPIRE_SECONDS = 7 * 24 * 60 * 60;
        }
    }

    // ========== 工具方法 ==========
    
    /**
     * 构建完整的Redis key
     * 
     * @param prefix key前缀
     * @param suffix key后缀
     * @return 完整的Redis key
     */
    public static String buildKey(String prefix, String suffix) {
        return prefix + suffix;
    }
    
    /**
     * 构建带多个参数的Redis key
     * 
     * @param prefix key前缀
     * @param params 参数列表
     * @return 完整的Redis key
     */
    public static String buildKey(String prefix, Object... params) {
        StringBuilder sb = new StringBuilder(prefix);
        for (Object param : params) {
            sb.append(param).append(":");
        }
        // 移除最后一个冒号
        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == ':') {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }
}
