package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@MarkDictType("激活状态")
@Schema(description = "激活状态")
@Getter
@AllArgsConstructor
public enum ActiveStatusEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "未激活")
    UN_ACTIVE(0, "未激活"),

    @Schema(description = "激活")
    ACTIVE(1, "激活"),
    ;

    private final int value;

    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
