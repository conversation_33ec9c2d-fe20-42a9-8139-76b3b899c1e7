package com.tth.common.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "公共比较类型")
public enum CommonCompareType {
    @Schema(description = "等于")
    EQ,
    @Schema(description = "不等于")
    NE,
    @Schema(description = "大于")
    GT,
    @Schema(description = "大于等于")
    GE,
    @Schema(description = "小于")
    LT,
    @Schema(description = "小于等于")
    LE,
    @Schema(description = "模糊")
    LIKE,
    @Schema(description = "非模糊")
    NOT_LIKE,
    @Schema(description = "包含")
    IN,
    @Schema(description = "不包含")
    NOT_IN,
}
