package com.tth.common.enums;

import lombok.Getter;

@Getter
public enum CommonOperateEnum {
    EQ("="),
    NE("!="),
    GT(">"),
    GE(">="),
    LT("<"),
    LE("<="),
    LIKE("LIKE"),
    NOT_LIKE("NOT LIKE"),
    IN("IN"),
    NOT_IN("NOT IN"),
    ;

    private final String operate;

    CommonOperateEnum(String operate) {
        this.operate = operate;
    }


    public static CommonOperateEnum GetOperateEnum(String operate) {
        for (CommonOperateEnum item : values()) {
            if (item.operate.equalsIgnoreCase(operate)) {
                return item;
            }
        }
        return null;
    }
}
