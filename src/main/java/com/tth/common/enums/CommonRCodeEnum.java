package com.tth.common.enums;


import lombok.Getter;

@Getter
public enum CommonRCodeEnum {
    // 请求成功
    SUCCESS(2000, "请求成功!"),
    // 系统错误
    SYS_ERR(4001, "系统异常"),
    // 三方异常
    THREE_ERR(4100, "三方异常"),
    // Token异常 4300~4399
    TOKEN_ERR(4300, "Token异常"),
    // 业务异常
    BUS_LOGIC_ERR(4200, "业务异常"),
    // 请求错误
    REQ_ERR(4400, "请求错误"),
    // 权限异常
    AUTH_ERR(4500, "权限异常");

    // 编码
    private final int CODE;
    // 消息
    private final String MESSAGE;

    CommonRCodeEnum(int code, String message) {
        this.CODE = code;
        this.MESSAGE = message;
    }
}