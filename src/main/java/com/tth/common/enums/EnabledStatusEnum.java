package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "启用状态")
@Getter
@AllArgsConstructor
public enum EnabledStatusEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "禁用")
    DISABLED(0, "禁用"),

    @Schema(description = "启用")
    ENABLED(1, "启用"),
    ;

    private final int value;

    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
