package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("性别")
@Schema(description = "性别")
public enum GenderEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "未知")
    UNKNOWN(0, "未知"),

    @Schema(description = "男")
    MALE(1, "男"),

    @Schema(description = "女")
    FEMALE(2, "女"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
