package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台来源枚举
 * 用于标识用户操作或数据来自哪个平台或渠道
 * 可用于注册来源、登录来源、订单来源等多种场景
 */
@Schema(description = "平台来源")
@Getter
@AllArgsConstructor
public enum PlatformSourceEnum implements IEnum<Integer> {

    @Schema(description = "未知")
    UNKNOWN(0, "未知"),

    @Schema(description = "PC网站")
    PC(1, "PC网站"),

    @Schema(description = "iOS应用")
    IOS_APP(2, "iOS应用"),

    @Schema(description = "Android应用")
    ANDROID_APP(3, "Android应用"),

    @Schema(description = "H5页面")
    H5(4, "H5页面"),

    @Schema(description = "微信小程序")
    WECHAT_MINI_PROGRAM(5, "微信小程序"),

    @Schema(description = "支付宝小程序")
    ALIPAY_MINI_PROGRAM(6, "支付宝小程序"),

    @Schema(description = "微信公众号")
    WECHAT_OFFICIAL_ACCOUNT(7, "微信公众号"),

    @Schema(description = "QQ小程序")
    QQ_MINI_PROGRAM(8, "QQ小程序"),

    @Schema(description = "字节跳动小程序")
    BYTEDANCE_MINI_PROGRAM(9, "字节跳动小程序"),

    @Schema(description = "百度小程序")
    BAIDU_MINI_PROGRAM(10, "百度小程序"),

    @Schema(description = "QQ")
    QQ(11, "QQ"),

    @Schema(description = "微信")
    WECHAT(12, "微信"),

    @Schema(description = "微博")
    WEIBO(13, "微博"),

    @Schema(description = "后台管理系统")
    ADMIN(14, "后台管理系统"),

    @Schema(description = "API接口")
    API(15, "API接口"),

    @Schema(description = "其他")
    OTHER(99, "其他");

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    @JsonValue
    private final String desc;

    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值查找对应的枚举
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回UNKNOWN
     */
    public static PlatformSourceEnum fromValue(Integer value) {
        if (value == null) {
            return UNKNOWN;
        }
        for (PlatformSourceEnum source : values()) {
            if (source.getValue().equals(value)) {
                return source;
            }
        }
        return UNKNOWN;
    }

    /**
     * 根据描述查找对应的枚举
     * @param desc 枚举描述
     * @return 对应的枚举实例，如果未找到则返回UNKNOWN
     */
    public static PlatformSourceEnum fromDesc(String desc) {
        if (desc == null) {
            return UNKNOWN;
        }
        for (PlatformSourceEnum source : values()) {
            if (source.getDesc().equals(desc)) {
                return source;
            }
        }
        return UNKNOWN;
    }

    /**
     * 判断是否为移动端来源
     * @return 如果是移动端来源返回true，否则返回false
     */
    public boolean isMobile() {
        return this == IOS_APP || this == ANDROID_APP || this == H5 ||
               this == WECHAT_MINI_PROGRAM || this == ALIPAY_MINI_PROGRAM ||
               this == QQ_MINI_PROGRAM || this == BYTEDANCE_MINI_PROGRAM ||
               this == BAIDU_MINI_PROGRAM || this == WECHAT_OFFICIAL_ACCOUNT;
    }

    /**
     * 判断是否为原生应用来源
     * @return 如果是原生应用来源返回true，否则返回false
     */
    public boolean isNativeApp() {
        return this == IOS_APP || this == ANDROID_APP;
    }

    /**
     * 判断是否为小程序来源
     * @return 如果是小程序来源返回true，否则返回false
     */
    public boolean isMiniProgram() {
        return this == WECHAT_MINI_PROGRAM || this == ALIPAY_MINI_PROGRAM ||
               this == QQ_MINI_PROGRAM || this == BYTEDANCE_MINI_PROGRAM ||
               this == BAIDU_MINI_PROGRAM;
    }

    /**
     * 判断是否为社交媒体来源
     * @return 如果是社交媒体来源返回true，否则返回false
     */
    public boolean isSocialMedia() {
        return this == WECHAT_OFFICIAL_ACCOUNT || this == QQ ||
               this == WECHAT || this == WEIBO;
    }

    /**
     * 判断是否为后台操作
     * @return 如果是后台操作返回true，否则返回false
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * 判断是否为API调用
     * @return 如果是API调用返回true，否则返回false
     */
    public boolean isApi() {
        return this == API;
    }
}
