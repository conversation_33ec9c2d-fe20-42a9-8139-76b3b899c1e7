package com.tth.common.enums;

import lombok.Getter;

@Getter
public enum RedisEnum {

    REFRESH_TOKEN("Authorization:login:refresh-token:", "刷新token的前缀"),
    USER_ID("Authorization:login:user-id:", "userId的前缀"),
    WECHAT_MP_ACCESS_TOKEN("wechat:miniprogram:access-token:", "微信小程序接口调用凭据前缀");

    private final String key;

    private final String desc;

    RedisEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
