package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "激活状态")
@Getter
@AllArgsConstructor
public enum SuccessStatusEnum implements IEnum<Integer> {

    @Schema(description = "成功")
    SUCCESS(0, "成功"),

    @Schema(description = "失败")
    FAILURE(1, "失败"),
    ;

    private final int value;

    @JsonValue
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
