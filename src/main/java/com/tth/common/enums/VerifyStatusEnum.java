package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "性别")
@Getter
@AllArgsConstructor
public enum VerifyStatusEnum implements IEnum<Integer> {

    @Schema(description = "未认证")
    UNVERIFIED(0, "未认证"),

    @Schema(description = "认证中")
    VERIFYING(1, "认证中"),

    @Schema(description = "已认证")
    VERIFIED(2, "已认证"),

    @Schema(description = "认证失败")
    FAILED(3, "认证失败"),

    @Schema(description = "认证过期")
    EXPIRED(4, "认证过期"),
    ;

    @EnumValue
    private final int value;

    @JsonValue
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
