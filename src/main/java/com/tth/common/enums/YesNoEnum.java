package com.tth.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否枚举
 */
@Getter
@AllArgsConstructor
@MarkDictType("是否")
@Schema(description = "是否枚举")
public enum YesNoEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "否")
    NO(0, "否"),

    @Schema(description = "是")
    YES(1, "是"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
