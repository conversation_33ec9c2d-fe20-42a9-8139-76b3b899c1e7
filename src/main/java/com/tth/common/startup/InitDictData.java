package com.tth.common.startup;

import cn.hutool.core.lang.ClassScanner;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import com.tth.modules.system.entity.DictData;
import com.tth.modules.system.entity.DictType;
import com.tth.modules.system.service.DictDataService;
import com.tth.modules.system.service.DictTypeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Slf4j
@Component
public class InitDictData {

    @Resource(name = "commonExecutor")
    private Executor executor;

    @Resource
    private DictTypeService dictTypeService;

    @Resource
    private DictDataService dictDataService;

    @EventListener(ApplicationReadyEvent.class)
    public void initDictData() {
        CompletableFuture.runAsync(() -> {
            log.info("开始初始化字典数据");
            try {
                // 获取指定包下的所有枚举类
                Set<Class<?>> classes = ClassScanner.scanPackage("com.tth");
                List<Class<?>> result = new ArrayList<>();
                for (Class<?> clazz : classes) {
                    if (clazz.isEnum()) {
                        result.add(clazz);
                        log.debug("找到枚举类: {}", clazz.getName());
                    }
                }
                // 处理枚举类
                processEnums(result);
            } catch (Exception e) {
                log.error("初始化字典数据失败", e);
            }

            log.info("初始化字典数据结束");
        }, executor);
    }

    /**
     * 处理枚举类，将有 @MarkDictType 注解并且实现了 IEnum 或 IEnumDesc 接口的枚举保存到数据字典表中
     * 主要依赖MarkDictType、IEnum和IEnumDesc接口
     *
     * @param enumClasses 枚举类列表
     */
    private void processEnums(List<Class<?>> enumClasses) {
        log.info("开始处理枚举类，共{}个", enumClasses.size());
        int successCount = 0;

        for (Class<?> enumClass : enumClasses) {
            try {
                // 检查是否实现了IEnum或IEnumDesc接口
                boolean isIEnum = IEnum.class.isAssignableFrom(enumClass);
                boolean isIEnumDesc = IEnumDesc.class.isAssignableFrom(enumClass);
                if (!isIEnum && !isIEnumDesc) {
                    log.debug("枚举类 {} 没有实现IEnum或IEnumDesc接口，跳过", enumClass.getName());
                    continue;
                }

                // 检查是否有@DictType注解
                MarkDictType markDictType = enumClass.getAnnotation(MarkDictType.class);
                if (markDictType == null) {
                    continue; // 没有@DictType注解，跳过
                }

                // 处理字典类型
                String dictType = StrUtil.toSymbolCase(enumClass.getSimpleName(), '_').toUpperCase(); // 使用枚举类名作为字典类型编码
                // 获取字典类型名称
                String dictTypeName = markDictType.value();

                // 保存字典类型
                saveDictType(dictType, dictTypeName);

                // 处理字典数据
                saveEnumAsDictData(enumClass, dictType);

                successCount++;
                log.info("处理枚举类 {} 成功", enumClass.getName());
            } catch (Exception e) {
                log.error("处理枚举类 {} 失败: {}", enumClass.getName(), e.getMessage(), e);
            }
        }

        log.info("枚举类处理完成，成功处理 {}/{} 个枚举类", successCount, enumClasses.size());
    }

    /**
     * 保存字典类型，如果已存在则更新
     *
     * @param dictTypeCode 字典类型编码
     * @param dictTypeName 字典类型名称
     */
    private void saveDictType(String dictTypeCode, String dictTypeName) {
        // 查询是否已存在该字典类型
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DictType::getDictType, dictTypeCode);
        DictType dictType = dictTypeService.getOne(queryWrapper);

        if (dictType == null) {
            // 不存在，创建新的字典类型
            dictType = new DictType();
            dictType.setDictType(dictTypeCode);
            dictType.setDictName(dictTypeName);
            dictType.setStatus(EnabledStatusEnum.ENABLED); // 默认启用
            dictType.setSort(1); // 默认排序
            dictTypeService.save(dictType);
            log.info("创建字典类型: {}", dictTypeCode);
        } else {
            // 已存在，更新字典类型
            dictType.setDictName(dictTypeName);
            dictTypeService.updateById(dictType);
            log.info("更新字典类型: {}", dictTypeCode);
        }
    }

    /**
     * 将枚举类的每个枚举值保存为字典数据
     * 全面处理枚举类与数据库字典表的同步，包括新增、删除、修改、排序变化等
     *
     * @param enumClass 枚举类
     * @param dictTypeCode 字典类型编码
     */
    private void saveEnumAsDictData(Class<?> enumClass, String dictTypeCode) {
        try {
            log.info("开始同步枚举类 {} 到字典表 {}", enumClass.getName(), dictTypeCode);

            // 获取枚举类的所有枚举常量
            Object[] enumConstants = enumClass.getEnumConstants();
            if (enumConstants == null || enumConstants.length == 0) {
                log.warn("枚举类 {} 没有枚举常量", enumClass.getName());
                return;
            }

            // 获取当前数据库中该字典类型的所有数据
            LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DictData::getDictType, dictTypeCode);
            List<DictData> existingDictDataList = dictDataService.list(queryWrapper);
            log.info("数据库中已有 {} 条字典数据", existingDictDataList.size());

            // 创建一个映射，存储已存在的字典数据，以便快速查找
            Map<String, DictData> existingDictDataMap = new HashMap<>();
            for (DictData dictData : existingDictDataList) {
                existingDictDataMap.put(dictData.getDictValue(), dictData);
            }

            // 创建一个集合来跟踪当前枚举中的值
            Set<String> currentEnumValues = new HashSet<>();

            // 处理每个枚举常量
            int newCount = 0;
            int updateCount = 0;

            for (int i = 0; i < enumConstants.length; i++) {
                try {
                    Enum<?> enumValue = (Enum<?>) enumConstants[i];

                    // 获取枚举值和描述
                    String dictValue = enumValue.name(); // 使用枚举常量名作为字典值
                    String dictLabel = getEnumLabel(enumValue); // 获取枚举的标签
                    String storeValue = getEnumStoreValue(enumValue); // 获取枚举的存储值

                    // 添加到当前值集合
                    currentEnumValues.add(dictValue);

                    // 检查是否已存在该字典数据
                    DictData existingDictData = existingDictDataMap.get(dictValue);

                    if (existingDictData == null) {
                        // 不存在，创建新的字典数据
                        DictData dictData = new DictData();
                        dictData.setDictType(dictTypeCode);
                        dictData.setDictLabel(dictLabel);
                        dictData.setDictValue(dictValue);
                        dictData.setStoreValue(storeValue);
                        dictData.setDictSort(i + 1);
                        dictData.setStatus(EnabledStatusEnum.ENABLED); // 默认启用
                        dictData.setIsDefault(YesNoEnum.NO); // 默认非默认值
                        dictDataService.save(dictData);
                        newCount++;
                        log.debug("创建字典数据: {}.{} = {}", dictTypeCode, dictValue, dictLabel);
                    } else {
                        // 已存在，检查是否需要更新
                        boolean needUpdate = false;

                        // 检查标签是否变化
                        if (!dictLabel.equals(existingDictData.getDictLabel())) {
                            existingDictData.setDictLabel(dictLabel);
                            needUpdate = true;
                        }

                        // 检查存储值是否变化
                        if (existingDictData.getStoreValue() == null || !storeValue.equals(existingDictData.getStoreValue())) {
                            existingDictData.setStoreValue(storeValue);
                            needUpdate = true;
                        }

                        // 检查排序是否变化
                        if (existingDictData.getDictSort() == null || existingDictData.getDictSort() != i + 1) {
                            existingDictData.setDictSort(i + 1);
                            needUpdate = true;
                        }

                        // 检查状态是否需要启用
                        if (existingDictData.getStatus() != EnabledStatusEnum.ENABLED) {
                            existingDictData.setStatus(EnabledStatusEnum.ENABLED);
                            needUpdate = true;
                        }

                        // 如果需要更新，则执行更新操作
                        if (needUpdate) {
                            dictDataService.updateById(existingDictData);
                            updateCount++;
                            log.debug("更新字典数据: {}.{} = {}", dictTypeCode, dictValue, dictLabel);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理枚举值失败: {}", e.getMessage(), e);
                }
            }

            // 处理不再存在的枚举值
            int deleteCount = handleRemovedEnumValues(dictTypeCode, existingDictDataList, currentEnumValues);

            // 记录同步结果
            log.info("枚举类 {} 同步完成: 新增 {} 条, 更新 {} 条, 删除 {} 条",
                    enumClass.getSimpleName(), newCount, updateCount, deleteCount);
        } catch (Exception e) {
            log.error("同步枚举类 {} 到字典表失败: {}", enumClass.getName(), e.getMessage(), e);
        }
    }

    /**
     * 处理不再存在的枚举值
     * 将不再存在于枚举中的值进行逻辑删除
     *
     * @param dictTypeCode 字典类型编码
     * @param existingDictDataList 当前数据库中的字典数据
     * @param currentEnumValues 当前枚举中的值
     * @return 删除的记录数
     */
    private int handleRemovedEnumValues(String dictTypeCode, List<DictData> existingDictDataList, Set<String> currentEnumValues) {
        int deleteCount = 0;

        for (DictData existingData : existingDictDataList) {
            if (!currentEnumValues.contains(existingData.getDictValue())) {
                // 该字典数据不再存在于当前枚举中，执行逻辑删除
                dictDataService.removeById(existingData.getId()); // MyBatis-Plus的逻辑删除
                deleteCount++;
                log.debug("删除不再存在的字典数据: {}.{}", dictTypeCode, existingData.getDictValue());
            }
        }

        return deleteCount;
    }

    /**
     * 获取枚举的标签（显示值）
     * 优先级：
     * 1. 如果实现了IEnumDesc接口，使用getDesc方法
     * 2. 如果有desc字段，使用该字段的值
     * 3. 最后使用枚举的name
     *
     * @param enumValue 枚举值
     * @return 枚举的标签
     */
    private String getEnumLabel(Enum<?> enumValue) {
        try {
            // 1. 如果实现了IEnumDesc接口，使用getDesc方法
            if (enumValue instanceof IEnumDesc) {
                String desc = ((IEnumDesc) enumValue).getDesc();
                if (desc != null && !desc.isEmpty()) {
                    return desc;
                }
            }

            // 2. 如果有desc字段，使用该字段的值
            try {
                Field descField = enumValue.getClass().getDeclaredField("desc");
                descField.setAccessible(true);
                Object desc = descField.get(enumValue);
                if (desc != null) {
                    return desc.toString();
                }
            } catch (NoSuchFieldException e) {
                // 忽略异常，继续尝试其他方法
                log.warn("枚举类 {} 没有 desc 字段", enumValue.getClass().getName());
            }

            // 3. 最后使用枚举的name
            return enumValue.name();
        } catch (Exception e) {
            log.warn("获取枚举标签失败，使用枚举名称: {}", e.getMessage());
            return enumValue.name();
        }
    }

    /**
     * 获取枚举的存储值（数据库存储值）
     * 优先级：
     * 1. 如果实现了IEnum接口，使用getValue方法
     * 2. 如果有value字段，使用该字段的值
     * 3. 最后使用枚举的name
     *
     * @param enumValue 枚举值
     * @return 枚举的存储值
     */
    private String getEnumStoreValue(Enum<?> enumValue) {
        try {
            // 1. 如果实现了IEnum接口，使用getValue方法
            if (enumValue instanceof IEnum) {
                Object value = ((IEnum<?>) enumValue).getValue();
                if (value != null) {
                    return value.toString();
                }
            }

            // 2. 如果有value字段，使用该字段的值
            try {
                Field valueField = enumValue.getClass().getDeclaredField("value");
                valueField.setAccessible(true);
                Object value = valueField.get(enumValue);
                if (value != null) {
                    return value.toString();
                }
            } catch (NoSuchFieldException e) {
                // 忽略异常，继续尝试其他方法
                log.debug("枚举类 {} 没有 value 字段", enumValue.getClass().getName());
            }

            // 3. 最后使用枚举的name
            return enumValue.name();
        } catch (Exception e) {
            log.warn("获取枚举存储值失败，使用枚举名称: {}", e.getMessage());
            return enumValue.name();
        }
    }

}
