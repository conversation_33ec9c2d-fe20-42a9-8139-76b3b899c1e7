package com.tth.common.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.tth.framework.base.*;

import java.util.HashMap;
import java.util.Map;


/**
 * 代码生成器
 */
public class CodeGenerator {
    // 数据库链接
    private static final String DATABASE_URL = "***********************************************************************************************************************************";
    // 数据库用户名
    private static final String DATABASE_USERNAME = "tth";
    // 数据库密码
    private static final String DATABASE_PASSWORD = "7aCS5CZhjbkpAfy2";
    // 包名（根据自己项目修改）
    private static final String PACKAGE_NAME = "com.tth";
    // 模块名称（如果项目中有多个模块需要设置（如：用户模块、商品模块、订单模块），只有一个模块就不用设置（为空就好））
    private static final String SECOND_MODULE = "";
    // 作者
    private static final String AUTHOR = "rongjie";
    // 表前缀（org_user表需要去掉前缀时这里填写"org_"）
    private static final String TABLE_PREFIX = "tth_";
    // 生成代码文件的路径
    private static String PARENT = PACKAGE_NAME + (StrUtil.isNotBlank(SECOND_MODULE) ? "." + SECOND_MODULE : "");

    public static void main(String[] args) {
        // 生成的表名
//        String tables = "tth_admin_user,tth_customer_user,tth_permission,tth_role,tth_role_permission,tth_user_auth,tth_user_base,tth_user_device,tth_user_ext,tth_user_role";
        String tables = "tth_system_config";
        PARENT = PACKAGE_NAME + ".modules.system";
        for (String table : tables.split(",")) {
            String projectPath = System.getProperty("user.dir");
            Map<OutputFile, String> packageInfo = new HashMap<>();
            String javaPath = projectPath + "/src/main/java/" + PARENT.replace(".", "/") + "/";
            // XML文件放在resources目录下的mapper文件夹中
            String resourcePath = projectPath + "/src/main/resources/mapper/" + PARENT.substring(PARENT.lastIndexOf(".") + 1);
            packageInfo.put(OutputFile.xml, resourcePath);
            packageInfo.put(OutputFile.entity, javaPath + "entity");
            packageInfo.put(OutputFile.service, javaPath + "service");
            packageInfo.put(OutputFile.serviceImpl, javaPath + "service/impl");
            packageInfo.put(OutputFile.mapper, javaPath + "mapper");
            packageInfo.put(OutputFile.controller, javaPath + "controller");


            // 表字段填充
            Column[] columns = new Column[]{
                    new Column("created_time", FieldFill.INSERT),
                    new Column("modified_time", FieldFill.INSERT_UPDATE),
                    new Column("version", FieldFill.INSERT_UPDATE),
                    new Column("modified_by", FieldFill.INSERT),
                    new Column("updated_by", FieldFill.INSERT_UPDATE)
            };


            // 提取模块名称，用于权限标识
            String moduleName = PARENT.substring(PARENT.lastIndexOf(".") + 1);

            // 创建自定义变量Map
            Map<String, Object> customMap = new HashMap<>();
            customMap.put("moduleName", moduleName);  // 模块名称，用于权限标识
            customMap.put("apiPath", "/" + moduleName);  // API路径
            customMap.put("packageName", PARENT);  // 包名

            FastAutoGenerator
                    .create(DATABASE_URL, DATABASE_USERNAME, DATABASE_PASSWORD)
                    .globalConfig(builder -> builder
                            .author(AUTHOR) // 设置作者名
                            .outputDir(projectPath + "/src/main/java") // 指定代码生成的输出目录
                            .commentDate("YYYY-MM-dd") // 设置注释日期格式
                            .dateType(DateType.ONLY_DATE) // 设置时间类型策略
                            .enableSpringdoc() // openAPI 方式
                    ) // 全局配置
                    .packageConfig(builder -> builder
                            .parent(PARENT) // 设置父包名
                            .xml("mapper") // 设置 Mapper XML 包名
                            .entity("entity") // 设置 Entity 包名
                            .service("service") // 设置 Service 包名
                            .serviceImpl("service.impl") // 设置 Service Impl 包名
                            .mapper("mapper") // 设置 Mapper 包名
                            .controller("controller") // 设置 Controller 包名
                            .pathInfo(packageInfo) // 设置路径配置信息
                            .build()
                    ) // 包配置
                    .injectionConfig(builder -> {
                        builder.beforeOutputFile((tableInfo, objectMap) -> {
                            // 将自定义变量添加到模板变量中
                            objectMap.putAll(customMap);
                        });
                    })
                    .strategyConfig(builder -> builder
                                    .addInclude(table)  // 设置需要生成的表名
                                    .enableSkipView() // 开启跳过视图
                                    .addTablePrefix(TABLE_PREFIX) //  增加过滤表前缀

                                    // 实体策略配置
                                    .entityBuilder()
                                    .superClass(BaseEntity.class) // 设置父类
                                    .disableSerialVersionUID() // 禁用生成 serialVersionUID
                                    .enableChainModel() // 开启链式模型
                                    .enableLombok() // 开启 Lombok 模型
                                    .enableRemoveIsPrefix() // 开启 Boolean 类型字段移除 is 前缀
                                    .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                                    .versionColumnName("version") // 乐观锁字段名(数据库字段)
                                    .versionPropertyName("version") // 乐观锁属性名(实体)
                                    .logicDeleteColumnName("deleted") // 逻辑删除字段名(数据库字段)
                                    .logicDeletePropertyName("deleted") // 逻辑删除属性名(实体)
                                    .naming(NamingStrategy.underline_to_camel) // 默认下划线转驼峰命名: NamingStrategy.underline_to_camel
                                    .addSuperEntityColumns("id", "created_time",
                                            "modified_time", "created_by",
                                            "modified_by", "version",
                                            "deleted_time", "deleted_by",
                                            "deleted") // 添加父类公共字段
                                    .idType(IdType.AUTO) // 全局主键类型
                                    .formatFileName("%s") // 格式化文件名称
                                    .addTableFills(columns) // 添加表字段填充

                                    // Controller 策略配置
                                    .controllerBuilder()
                                    .superClass(BaseController.class)
                                    .formatFileName("%sController") // 格式化文件名称
                                    .enableRestStyle()  // 启用 REST 风格

                                    // Service 策略配置
                                    .serviceBuilder()
                                    .formatServiceFileName("%sService")
                                    .superServiceClass(BaseService.class)
                                    .superServiceImplClass(BaseServiceImpl.class)
                                    .formatServiceImplFileName("%sServiceImpl")

                                    // Mapper 策略配置
                                    .mapperBuilder()
                                    .enableBaseResultMap() // 启用 BaseResultMap 生成
                                    .enableBaseColumnList() // 启用 BaseColumnList
                                    .superClass(BaseMapper.class)
                                    .formatMapperFileName("%sMapper")
                                    .formatXmlFileName("%s").enableFileOverride()
                    ) // 策略配置
                    .templateEngine(new FreemarkerTemplateEngine()) // 使用 Freemarker 模板引擎
                    .execute();
        }
    }
}
