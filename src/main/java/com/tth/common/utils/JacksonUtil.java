package com.tth.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tth.framework.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Jackson工具类
 * 封装了常用的JSON序列化和反序列化操作
 * 使用Spring容器中配置的ObjectMapper，确保与项目配置一致
 */
@Slf4j
public class JacksonUtil {

    /**
     * 获取ObjectMapper实例
     * 使用SpringContextHolder从应用上下文中获取配置好的ObjectMapper
     *
     * @return ObjectMapper实例
     */
    private static ObjectMapper getObjectMapper() {
        try {
            return SpringContextHolder.getBean(ObjectMapper.class);
        } catch (Exception e) {
            log.error("获取ObjectMapper实例异常: {}", e.getMessage(), e);
            throw new IllegalStateException("ObjectMapper未初始化，请确保Jackson配置正确");
        }
    }

    /**
     * 对象转JSON字符串
     *
     * @param obj 要序列化的对象
     * @return JSON字符串，失败时返回null
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            return getObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            log.error("对象序列化为JSON失败: obj={}, error={}", obj, e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   泛型
     * @return 反序列化后的对象，失败时返回null
     */
    public static <T> T toBean(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }

        try {
            return getObjectMapper().readValue(json, clazz);
        } catch (Exception e) {
            log.error("JSON反序列化为对象失败: json={}, clazz={}, error={}",
                json, clazz.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Object转指定类型对象
     * 适用于从Redis等地方获取的Object需要转换为具体类型的场景
     *
     * @param obj   要转换的对象
     * @param clazz 目标类型
     * @param <T>   泛型
     * @return 转换后的对象，失败时返回null
     */
    public static <T> T convertValue(Object obj, Class<T> clazz) {
        if (obj == null) {
            return null;
        }

        // 如果已经是目标类型，直接返回
        if (clazz.isInstance(obj)) {
            return clazz.cast(obj);
        }

        try {
            // 先转为JSON字符串，再转为目标类型
            String jsonString = obj.toString();
            return getObjectMapper().readValue(jsonString, clazz);
        } catch (Exception e) {
            log.error("对象类型转换失败: obj={}, clazz={}, error={}",
                obj, clazz.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转List
     *
     * @param json  JSON字符串
     * @param clazz List元素类型
     * @param <T>   泛型
     * @return List对象，失败时返回空List
     */
    public static <T> List<T> toList(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            ObjectMapper objectMapper = getObjectMapper();
            return objectMapper.readValue(json,
                objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            log.error("JSON反序列化为List失败: json={}, clazz={}, error={}",
                json, clazz.getName(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Object转List
     * 适用于从Redis等地方获取的Object需要转换为List的场景
     *
     * @param obj   要转换的对象
     * @param clazz List元素类型
     * @param <T>   泛型
     * @return List对象，失败时返回空List
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> convertList(Object obj, Class<T> clazz) {
        if (obj == null) {
            return new ArrayList<>();
        }

        // 如果已经是List类型，直接返回
        if (obj instanceof List) {
            return (List<T>) obj;
        }

        try {
            String jsonString = obj.toString();
            return toList(jsonString, clazz);
        } catch (Exception e) {
            log.error("对象转换为List失败: obj={}, clazz={}, error={}",
                obj, clazz.getName(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * JSON字符串转Map
     *
     * @param json       JSON字符串
     * @param keyClass   Map键类型
     * @param valueClass Map值类型
     * @param <K>        键泛型
     * @param <V>        值泛型
     * @return Map对象，失败时返回空Map
     */
    public static <K, V> Map<K, V> toMap(String json, Class<K> keyClass, Class<V> valueClass) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            ObjectMapper objectMapper = getObjectMapper();
            return objectMapper.readValue(json,
                objectMapper.getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (Exception e) {
            log.error("JSON反序列化为Map失败: json={}, keyClass={}, valueClass={}, error={}",
                json, keyClass.getName(), valueClass.getName(), e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * Object转Map
     * 适用于从Redis等地方获取的Object需要转换为Map的场景
     *
     * @param obj        要转换的对象
     * @param keyClass   Map键类型
     * @param valueClass Map值类型
     * @param <K>        键泛型
     * @param <V>        值泛型
     * @return Map对象，失败时返回空Map
     */
    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> convertMap(Object obj, Class<K> keyClass, Class<V> valueClass) {
        if (obj == null) {
            return new HashMap<>();
        }

        // 如果已经是Map类型，直接返回
        if (obj instanceof Map) {
            return (Map<K, V>) obj;
        }

        try {
            String jsonString = obj.toString();
            return toMap(jsonString, keyClass, valueClass);
        } catch (Exception e) {
            log.error("对象转换为Map失败: obj={}, keyClass={}, valueClass={}, error={}",
                obj, keyClass.getName(), valueClass.getName(), e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 使用TypeReference进行复杂类型转换
     * 适用于泛型嵌套等复杂场景，如 List<Map<String, Object>>
     *
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @param <T>           泛型
     * @return 转换后的对象，失败时返回null
     */
    public static <T> T parse(String json, TypeReference<T> typeReference) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }

        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (Exception e) {
            log.error("JSON反序列化失败: json={}, typeReference={}, error={}",
                json, typeReference.getType(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Object转换为复杂类型
     * 适用于从Redis等地方获取的Object需要转换为复杂类型的场景
     *
     * @param obj           要转换的对象
     * @param typeReference 类型引用
     * @param <T>           泛型
     * @return 转换后的对象，失败时返回null
     */
    public static <T> T convertValue(Object obj, TypeReference<T> typeReference) {
        if (obj == null) {
            return null;
        }

        try {
            String jsonString = obj.toString();
            return parse(jsonString, typeReference);
        } catch (Exception e) {
            log.error("对象类型转换失败: obj={}, typeReference={}, error={}",
                obj, typeReference.getType(), e.getMessage(), e);
            return null;
        }
    }
}
