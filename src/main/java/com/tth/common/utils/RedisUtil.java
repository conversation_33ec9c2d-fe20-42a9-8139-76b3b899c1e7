package com.tth.common.utils;

import cn.hutool.core.util.StrUtil;
import com.tth.framework.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.script.RedisScript;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redis工具类
 * 封装了常用的Redis操作，简化Redis的使用
 * 支持通过类名.方法名的静态方法调用方式
 * 基于RedisTemplate实现，支持对象存储和获取
 */
@Slf4j
public class RedisUtil {

    /**
     * 获取RedisTemplate实例
     * 使用SpringContextHolder从应用上下文中获取
     *
     * @return RedisTemplate实例
     */
    @SuppressWarnings("unchecked")
    private static RedisTemplate<String, Object> getRedisTemplate() {
        try {
            return SpringContextHolder.getBean("redisTemplate", RedisTemplate.class);
        } catch (Exception e) {
            log.error("获取RedisTemplate实例异常: {}", e.getMessage(), e);
            throw new IllegalStateException("RedisTemplate未初始化，请确保RedisConfig正确配置");
        }
    }

    // 直接在各个方法中使用getRedisTemplate().opsForXXX()

    // ========== 通用操作 ==========



    /**
     * 指定缓存失效时间
     *
     * @param key     键
     * @param timeout 时间(秒)
     * @return 是否成功
     */
    public static boolean expire(String key, long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 指定缓存失效时间
     *
     * @param key      键
     * @param timeout  时间
     * @param timeUnit 时间单位
     * @return 是否成功
     */
    public static boolean expire(String key, long timeout, TimeUnit timeUnit) {
        try {
            if (timeout > 0) {
                getRedisTemplate().expire(key, timeout, timeUnit);
            }
            return true;
        } catch (Exception e) {
            log.error("设置Redis过期时间异常: key={}, timeout={}, timeUnit={}, error={}", key, timeout, timeUnit, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取key的过期时间
     *
     * @param key 键
     * @return 时间(秒) 返回0代表永久有效
     */
    public static long getExpire(String key) {
        return getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 获取key的过期时间
     *
     * @param key      键
     * @param timeUnit 时间单位
     * @return 过期时间
     */
    public static long getExpire(String key, TimeUnit timeUnit) {
        Long expire = getRedisTemplate().getExpire(key, timeUnit);
        return expire != null ? expire : -1;
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true存在 false不存在
     */
    public static boolean hasKey(String key) {
        try {
            Boolean hasKey = getRedisTemplate().hasKey(key);
            return hasKey != null && hasKey;
        } catch (Exception e) {
            log.error("判断Redis键是否存在异常: key={}, error={}", key, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 键（一个或多个）
     */
    public static void delete(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                getRedisTemplate().delete(key[0]);
            } else {
                getRedisTemplate().delete(Arrays.asList(key));
            }
        }
    }

    /**
     * 按前缀删除缓存
     *
     * @param prefix 前缀
     */
    public static void deleteByPrefix(String prefix) {
        if (StrUtil.isBlank(prefix)) {
            return;
        }
        Set<String> keys = getRedisTemplate().keys(prefix + "*");
        if (keys != null && !keys.isEmpty()) {
            getRedisTemplate().delete(keys);
            log.debug("已删除前缀为[{}]的{}个缓存", prefix, keys.size());
        }
    }

    // ========== String操作 ==========

    /**
     * 获取缓存
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        if (key == null) {
            return null;
        }
        RedisTemplate<String, Object> redisTemplate = getRedisTemplate();
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取缓存并转换为指定类型
     *
     * @param key   键
     * @param clazz 类型
     * @param <T>   泛型
     * @return 值
     */
    public static <T> T get(String key, Class<T> clazz) {
        Object value = get(key);
        return JacksonUtil.convertValue(value, clazz);
    }

    /**
     * 获取缓存并转换为Map类型
     *
     * @param key        键
     * @param keyClass   Map键的类型
     * @param valueClass Map值的类型
     * @param <K>        Map键的泛型
     * @param <V>        Map值的泛型
     * @return Map对象
     */
    public static <K, V> Map<K, V> getMap(String key, Class<K> keyClass, Class<V> valueClass) {
        Object value = get(key);
        return JacksonUtil.convertMap(value, keyClass, valueClass);
    }

    /**
     * 获取缓存并转换为List
     *
     * @param key   键
     * @param clazz 类型
     * @param <T>   泛型
     * @return 值
     */
    public static <T> List<T> getList(String key, Class<T> clazz) {
        Object value = get(key);
        return JacksonUtil.convertList(value, clazz);
    }

    /**
     * 设置缓存
     *
     * @param key   键
     * @param value 值
     * @return 是否成功
     */
    public static boolean set(String key, Object value) {
        try {
            RedisTemplate<String, Object> redisTemplate = getRedisTemplate();
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置Redis缓存异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 设置缓存并设置过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 时间(秒) 小于等于0则不设置过期时间
     * @return 是否成功
     */
    public static boolean set(String key, Object value, long timeout) {
        return set(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置缓存并设置过期时间
     *
     * @param key      键
     * @param value    值
     * @param timeout  时间
     * @param timeUnit 时间单位
     * @return 是否成功
     */
    public static boolean set(String key, Object value, long timeout, TimeUnit timeUnit) {
        try {
            if (timeout > 0) {
                getRedisTemplate().opsForValue().set(key, value, timeout, timeUnit);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("设置Redis缓存异常: key={}, value={}, timeout={}, timeUnit={}, error={}", key, value, timeout, timeUnit, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 递增因子(大于0)
     * @return 递增后的值
     */
    public static long increment(String key, long delta) {
        if (delta < 0) {
            throw new IllegalArgumentException("递增因子必须大于0");
        }
        return getRedisTemplate().opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 递减因子(大于0)
     * @return 递减后的值
     */
    public static long decrement(String key, long delta) {
        if (delta < 0) {
            throw new IllegalArgumentException("递减因子必须大于0");
        }
        return getRedisTemplate().opsForValue().decrement(key, delta);
    }

    // ========== Hash操作 ==========

    /**
     * 获取Hash中的数据
     *
     * @param key     Redis键
     * @param hashKey Hash键
     * @return Hash中的对象
     */
    public static Object hGet(String key, String hashKey) {
        return getRedisTemplate().opsForHash().get(key, hashKey);
    }

    /**
     * 获取Hash中的数据并转换为指定类型
     *
     * @param key     Redis键
     * @param hashKey Hash键
     * @param clazz   类型
     * @param <T>     泛型
     * @return Hash中的对象
     */
    public static <T> T hGet(String key, String hashKey, Class<T> clazz) {
        Object value = hGet(key, hashKey);
        return JacksonUtil.convertValue(value, clazz);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key      Redis键
     * @param hashKeys Hash键集合
     * @return Hash对象集合
     */
    @SuppressWarnings("unchecked")
    public static List<Object> hMultiGet(String key, Collection<String> hashKeys) {
        return getRedisTemplate().opsForHash().multiGet(key, (Collection) hashKeys);
    }

    /**
     * 获取整个Hash
     *
     * @param key Redis键
     * @return Hash对象
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> hGetAll(String key) {
        // RedisTemplate的Hash操作返回的是Map<Object, Object>
        // 但在实际使用中，Hash的key都是字符串，所以可以安全地转换
        return (Map<String, Object>) (Map) getRedisTemplate().opsForHash().entries(key);
    }

    /**
     * 设置Hash中的数据
     *
     * @param key     Redis键
     * @param hashKey Hash键
     * @param value   值
     */
    public static void hSet(String key, String hashKey, Object value) {
        getRedisTemplate().opsForHash().put(key, hashKey, value);
    }

    /**
     * 设置Hash中的数据，并设置过期时间
     *
     * @param key     Redis键
     * @param hashKey Hash键
     * @param value   值
     * @param timeout 过期时间(秒)
     */
    public static void hSet(String key, String hashKey, Object value, long timeout) {
        hSet(key, hashKey, value);
        expire(key, timeout);
    }

    /**
     * 设置多个Hash中的数据
     *
     * @param key  Redis键
     * @param maps Hash键值对
     */
    public static void hSetAll(String key, Map<String, Object> maps) {
        getRedisTemplate().opsForHash().putAll(key, maps);
    }

    /**
     * 设置多个Hash中的数据，并设置过期时间
     *
     * @param key     Redis键
     * @param maps    Hash键值对
     * @param timeout 过期时间(秒)
     */
    public static void hSetAll(String key, Map<String, Object> maps, long timeout) {
        hSetAll(key, maps);
        expire(key, timeout);
    }

    /**
     * 删除Hash中的数据
     *
     * @param key      Redis键
     * @param hashKeys Hash键集合
     * @return 删除数量
     */
    public static long hDelete(String key, Object... hashKeys) {
        return getRedisTemplate().opsForHash().delete(key, hashKeys);
    }

    /**
     * 判断Hash中是否存在数据
     *
     * @param key     Redis键
     * @param hashKey Hash键
     * @return 是否存在
     */
    public static boolean hExists(String key, String hashKey) {
        return getRedisTemplate().opsForHash().hasKey(key, hashKey);
    }

    /**
     * Hash中的数据递增
     *
     * @param key     Redis键
     * @param hashKey Hash键
     * @param delta   递增因子(大于0)
     * @return 递增后的值
     */
    public static long hIncrement(String key, String hashKey, long delta) {
        if (delta < 0) {
            throw new IllegalArgumentException("递增因子必须大于0");
        }
        return getRedisTemplate().opsForHash().increment(key, hashKey, delta);
    }

    // ========== List操作 ==========

    /**
     * 获取List缓存的长度
     *
     * @param key Redis键
     * @return 长度
     */
    public static long lSize(String key) {
        try {
            Long size = getRedisTemplate().opsForList().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取List缓存长度异常: key={}, error={}", key, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 通过索引获取List中的元素
     *
     * @param key   Redis键
     * @param index 索引(0表示第一个元素，-1表示最后一个元素)
     * @return 元素
     */
    public static Object lIndex(String key, long index) {
        try {
            return getRedisTemplate().opsForList().index(key, index);
        } catch (Exception e) {
            log.error("获取List缓存元素异常: key={}, index={}, error={}", key, index, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取List缓存中指定范围的元素
     *
     * @param key   Redis键
     * @param start 开始索引(0表示第一个元素)
     * @param end   结束索引(-1表示最后一个元素)
     * @return 元素列表
     */
    public static List<Object> lRange(String key, long start, long end) {
        try {
            return getRedisTemplate().opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("获取List缓存范围元素异常: key={}, start={}, end={}, error={}", key, start, end, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取List缓存中指定范围的元素并转换为指定类型
     *
     * @param key   Redis键
     * @param start 开始索引(0表示第一个元素)
     * @param end   结束索引(-1表示最后一个元素)
     * @param clazz 类型
     * @param <T>   泛型
     * @return 元素列表
     */
    public static <T> List<T> lRange(String key, long start, long end, Class<T> clazz) {
        List<Object> list = lRange(key, start, end);
        return list.stream()
                .map(item -> JacksonUtil.convertValue(item, clazz))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 存储在List缓存中的数据
     *
     * @param key   Redis键
     * @param value 值
     * @return 存储后的长度
     */
    public static long lRightPush(String key, Object value) {
        try {
            Long size = getRedisTemplate().opsForList().rightPush(key, value);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("存储List缓存异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 存储在List缓存中的多个数据
     *
     * @param key    Redis键
     * @param values 值列表
     * @return 存储后的长度
     */
    public static long lRightPushAll(String key, List<Object> values) {
        try {
            Long size = getRedisTemplate().opsForList().rightPushAll(key, values.toArray());
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("存储List缓存异常: key={}, values={}, error={}", key, values, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 存储在List缓存左侧的数据
     *
     * @param key   Redis键
     * @param value 值
     * @return 存储后的长度
     */
    public static long lLeftPush(String key, Object value) {
        try {
            Long size = getRedisTemplate().opsForList().leftPush(key, value);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("存储List缓存异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 存储在List缓存左侧的多个数据
     *
     * @param key    Redis键
     * @param values 值列表
     * @return 存储后的长度
     */
    public static long lLeftPushAll(String key, List<Object> values) {
        try {
            Long size = getRedisTemplate().opsForList().leftPushAll(key, values.toArray());
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("存储List缓存异常: key={}, values={}, error={}", key, values, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 从List缓存左侧弹出元素
     *
     * @param key Redis键
     * @return 弹出的元素
     */
    public static Object lLeftPop(String key) {
        try {
            return getRedisTemplate().opsForList().leftPop(key);
        } catch (Exception e) {
            log.error("弹出List缓存元素异常: key={}, error={}", key, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从List缓存右侧弹出元素
     *
     * @param key Redis键
     * @return 弹出的元素
     */
    public static Object lRightPop(String key) {
        try {
            return getRedisTemplate().opsForList().rightPop(key);
        } catch (Exception e) {
            log.error("弹出List缓存元素异常: key={}, error={}", key, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从List缓存中移除数据
     *
     * @param key   Redis键
     * @param count 移除数量(大于0从左到右，小于0从右到左，等于0全部移除)
     * @param value 值
     * @return 移除数量
     */
    public static long lRemove(String key, long count, Object value) {
        try {
            Long remove = getRedisTemplate().opsForList().remove(key, count, value);
            return remove != null ? remove : 0;
        } catch (Exception e) {
            log.error("移除List缓存异常: key={}, count={}, value={}, error={}", key, count, value, e.getMessage(), e);
            return 0;
        }
    }

    // ========== Set操作 ==========

    /**
     * 获取Set缓存的长度
     *
     * @param key Redis键
     * @return 长度
     */
    public static long sSize(String key) {
        try {
            Long size = getRedisTemplate().opsForSet().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取Set缓存长度异常: key={}, error={}", key, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取Set缓存中的所有元素
     *
     * @param key Redis键
     * @return 元素集合
     */
    public static Set<Object> sMembers(String key) {
        try {
            return getRedisTemplate().opsForSet().members(key);
        } catch (Exception e) {
            log.error("获取Set缓存元素异常: key={}, error={}", key, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 判断Set缓存中是否存在元素
     *
     * @param key   Redis键
     * @param value 值
     * @return 是否存在
     */
    public static boolean sIsMember(String key, Object value) {
        try {
            Boolean isMember = getRedisTemplate().opsForSet().isMember(key, value);
            return isMember != null && isMember;
        } catch (Exception e) {
            log.error("判断Set缓存元素异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 添加Set缓存
     *
     * @param key    Redis键
     * @param values 值
     * @return 添加数量
     */
    public static long sAdd(String key, Object... values) {
        try {
            Long count = getRedisTemplate().opsForSet().add(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("添加Set缓存异常: key={}, values={}, error={}", key, values, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 移除Set缓存中的元素
     *
     * @param key    Redis键
     * @param values 值
     * @return 移除数量
     */
    public static long sRemove(String key, Object... values) {
        try {
            Long count = getRedisTemplate().opsForSet().remove(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("移除Set缓存元素异常: key={}, values={}, error={}", key, values, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取两个Set的交集
     *
     * @param key      Redis键
     * @param otherKey 其他Redis键
     * @return 交集元素集合
     */
    public static Set<Object> sIntersect(String key, String otherKey) {
        try {
            return getRedisTemplate().opsForSet().intersect(key, otherKey);
        } catch (Exception e) {
            log.error("获取Set交集异常: key={}, otherKey={}, error={}", key, otherKey, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取多个Set的交集
     *
     * @param key       Redis键
     * @param otherKeys 其他Redis键集合
     * @return 交集元素集合
     */
    public static Set<Object> sIntersect(String key, Collection<String> otherKeys) {
        try {
            return getRedisTemplate().opsForSet().intersect(key, otherKeys);
        } catch (Exception e) {
            log.error("获取Set交集异常: key={}, otherKeys={}, error={}", key, otherKeys, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取两个Set的并集
     *
     * @param key      Redis键
     * @param otherKey 其他Redis键
     * @return 并集元素集合
     */
    public static Set<Object> sUnion(String key, String otherKey) {
        try {
            return getRedisTemplate().opsForSet().union(key, otherKey);
        } catch (Exception e) {
            log.error("获取Set并集异常: key={}, otherKey={}, error={}", key, otherKey, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取多个Set的并集
     *
     * @param key       Redis键
     * @param otherKeys 其他Redis键集合
     * @return 并集元素集合
     */
    public static Set<Object> sUnion(String key, Collection<String> otherKeys) {
        try {
            return getRedisTemplate().opsForSet().union(key, otherKeys);
        } catch (Exception e) {
            log.error("获取Set并集异常: key={}, otherKeys={}, error={}", key, otherKeys, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取两个Set的差集
     *
     * @param key      Redis键
     * @param otherKey 其他Redis键
     * @return 差集元素集合
     */
    public static Set<Object> sDifference(String key, String otherKey) {
        try {
            return getRedisTemplate().opsForSet().difference(key, otherKey);
        } catch (Exception e) {
            log.error("获取Set差集异常: key={}, otherKey={}, error={}", key, otherKey, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取多个Set的差集
     *
     * @param key       Redis键
     * @param otherKeys 其他Redis键集合
     * @return 差集元素集合
     */
    public static Set<Object> sDifference(String key, Collection<String> otherKeys) {
        try {
            return getRedisTemplate().opsForSet().difference(key, otherKeys);
        } catch (Exception e) {
            log.error("获取Set差集异常: key={}, otherKeys={}, error={}", key, otherKeys, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    // ========== ZSet操作 ==========

    /**
     * 获取ZSet缓存的长度
     *
     * @param key Redis键
     * @return 长度
     */
    public static long zSize(String key) {
        try {
            Long size = getRedisTemplate().opsForZSet().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取ZSet缓存长度异常: key={}, error={}", key, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取ZSet缓存中指定范围的元素
     *
     * @param key   Redis键
     * @param start 开始索引(0表示第一个元素)
     * @param end   结束索引(-1表示最后一个元素)
     * @return 元素集合
     */
    public static Set<Object> zRange(String key, long start, long end) {
        try {
            return getRedisTemplate().opsForZSet().range(key, start, end);
        } catch (Exception e) {
            log.error("获取ZSet缓存范围元素异常: key={}, start={}, end={}, error={}", key, start, end, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 添加ZSet缓存
     *
     * @param key   Redis键
     * @param value 值
     * @param score 分数
     * @return 是否成功
     */
    public static boolean zAdd(String key, Object value, double score) {
        try {
            Boolean success = getRedisTemplate().opsForZSet().add(key, value, score);
            return success != null && success;
        } catch (Exception e) {
            log.error("添加ZSet缓存异常: key={}, value={}, score={}, error={}", key, value, score, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 移除ZSet缓存中的元素
     *
     * @param key    Redis键
     * @param values 值
     * @return 移除数量
     */
    public static long zRemove(String key, Object... values) {
        try {
            Long count = getRedisTemplate().opsForZSet().remove(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("移除ZSet缓存元素异常: key={}, values={}, error={}", key, values, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取ZSet缓存中元素的分数
     *
     * @param key   Redis键
     * @param value 值
     * @return 分数
     */
    public static Double zScore(String key, Object value) {
        try {
            return getRedisTemplate().opsForZSet().score(key, value);
        } catch (Exception e) {
            log.error("获取ZSet缓存元素分数异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取ZSet缓存中元素的排名
     *
     * @param key   Redis键
     * @param value 值
     * @return 排名(从0开始)
     */
    public static Long zRank(String key, Object value) {
        try {
            return getRedisTemplate().opsForZSet().rank(key, value);
        } catch (Exception e) {
            log.error("获取ZSet缓存元素排名异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取ZSet缓存中元素的倒序排名
     *
     * @param key   Redis键
     * @param value 值
     * @return 排名(从0开始)
     */
    public static Long zReverseRank(String key, Object value) {
        try {
            return getRedisTemplate().opsForZSet().reverseRank(key, value);
        } catch (Exception e) {
            log.error("获取ZSet缓存元素倒序排名异常: key={}, value={}, error={}", key, value, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取指定分数范围的元素数量
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素数量
     */
    public static Long zCount(String key, double min, double max) {
        try {
            return getRedisTemplate().opsForZSet().count(key, min, max);
        } catch (Exception e) {
            log.error("获取ZSet缓存分数范围元素数量异常: key={}, min={}, max={}, error={}", key, min, max, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 增加元素的分数
     *
     * @param key   Redis键
     * @param value 值
     * @param delta 增加的分数
     * @return 增加后的分数
     */
    public static Double zIncrementScore(String key, Object value, double delta) {
        try {
            return getRedisTemplate().opsForZSet().incrementScore(key, value, delta);
        } catch (Exception e) {
            log.error("增加ZSet缓存元素分数异常: key={}, value={}, delta={}, error={}", key, value, delta, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取ZSet缓存中指定分数范围的元素
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素集合
     */
    public static Set<Object> zRangeByScore(String key, double min, double max) {
        try {
            return getRedisTemplate().opsForZSet().rangeByScore(key, min, max);
        } catch (Exception e) {
            log.error("获取ZSet缓存分数范围元素异常: key={}, min={}, max={}, error={}", key, min, max, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取ZSet缓存中指定分数范围的元素，并带有分数
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素集合，带有分数
     */
    public static Set<ZSetOperations.TypedTuple<Object>> zRangeByScoreWithScores(String key, double min, double max) {
        try {
            return getRedisTemplate().opsForZSet().rangeByScoreWithScores(key, min, max);
        } catch (Exception e) {
            log.error("获取ZSet缓存分数范围元素异常: key={}, min={}, max={}, error={}", key, min, max, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取ZSet缓存中指定范围的元素（倒序）
     *
     * @param key   Redis键
     * @param start 开始索引(0表示第一个元素)
     * @param end   结束索引(-1表示最后一个元素)
     * @return 元素集合
     */
    public static Set<Object> zReverseRange(String key, long start, long end) {
        try {
            return getRedisTemplate().opsForZSet().reverseRange(key, start, end);
        } catch (Exception e) {
            log.error("获取ZSet缓存倒序范围元素异常: key={}, start={}, end={}, error={}", key, start, end, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 获取ZSet缓存中指定分数范围的元素（倒序）
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素集合
     */
    public static Set<Object> zReverseRangeByScore(String key, double min, double max) {
        try {
            return getRedisTemplate().opsForZSet().reverseRangeByScore(key, min, max);
        } catch (Exception e) {
            log.error("获取ZSet缓存倒序分数范围元素异常: key={}, min={}, max={}, error={}", key, min, max, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    // ========== Lua脚本操作 ==========

    /**
     * 执行Lua脚本
     *
     * @param script Lua脚本
     * @param keys 键列表
     * @param args 参数列表
     * @param <T> 返回类型
     * @return 脚本执行结果
     */
    public static <T> T execute(RedisScript<T> script, List<String> keys, Object... args) {
        try {
            return getRedisTemplate().execute(script, keys, args);
        } catch (Exception e) {
            log.error("执行Lua脚本异常: script={}, keys={}, args={}, error={}", script.getSha1(), keys, args, e.getMessage(), e);
            throw e;
        }
    }

    // ========== 位图操作 ==========

    /**
     * 设置位图中指定偏移量上的位的值
     *
     * @param key    Redis键
     * @param offset 偏移量
     * @param value  值（0或者1）
     * @return 该位的原值
     */
    public static boolean setBit(String key, long offset, boolean value) {
        try {
            return Boolean.TRUE.equals(getRedisTemplate().opsForValue().setBit(key, offset, value));
        } catch (Exception e) {
            log.error("设置位图异常: key={}, offset={}, value={}, error={}", key, offset, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取位图中指定偏移量上的位的值
     *
     * @param key    Redis键
     * @param offset 偏移量
     * @return 该位的值
     */
    public static boolean getBit(String key, long offset) {
        try {
            Boolean bit = getRedisTemplate().opsForValue().getBit(key, offset);
            return bit != null && bit;
        } catch (Exception e) {
            log.error("获取位图异常: key={}, offset={}, error={}", key, offset, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 统计位图中设置为1的位的数量
     *
     * @param key Redis键
     * @return 位的数量
     */
    public static long bitCount(String key) {
        try {
            Long count = getRedisTemplate().execute((RedisCallback<Long>) connection -> connection.bitCount(key.getBytes()));
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("统计位图异常: key={}, error={}", key, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计位图中指定范围内设置为1的位的数量
     *
     * @param key   Redis键
     * @param start 开始字节
     * @param end   结束字节
     * @return 位的数量
     */
    public static long bitCount(String key, long start, long end) {
        try {
            Long count = getRedisTemplate().execute((RedisCallback<Long>) connection -> connection.bitCount(key.getBytes(), start, end));
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("统计位图范围异常: key={}, start={}, end={}, error={}", key, start, end, e.getMessage(), e);
            return 0;
        }
    }

    // ========== 批量操作 ==========

    /**
     * 批量获取多个key的值
     *
     * @param keys key列表
     * @return 值列表，顺序与keys对应，不存在的key对应null
     */
    public static List<String> multiGet(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<Object> values = getRedisTemplate().opsForValue().multiGet(keys);
            if (values == null) {
                return new ArrayList<>(Collections.nCopies(keys.size(), null));
            }

            List<String> result = new ArrayList<>();
            for (Object value : values) {
                result.add(value != null ? value.toString() : null);
            }

            log.debug("批量获取Redis值完成，查询{}个key，获得{}个非空值",
                keys.size(), result.stream().mapToInt(v -> v != null ? 1 : 0).sum());
            return result;

        } catch (Exception e) {
            log.error("批量获取Redis值异常: keys={}, error={}", keys, e.getMessage(), e);
            return new ArrayList<>(Collections.nCopies(keys.size(), null));
        }
    }
}
