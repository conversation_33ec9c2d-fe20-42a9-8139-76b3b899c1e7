package com.tth.framework.annotation;

import com.tth.framework.enums.LogLevel;

import java.lang.annotation.*;

/**
 * API日志注解，用于标记需要记录日志的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiLog {
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 模块名称
     */
    String module() default "";
    
    /**
     * 日志级别
     */
    LogLevel level() default LogLevel.STANDARD;
    
    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean logResponse() default true;
    
    /**
     * 是否记录请求头
     */
    boolean logHeaders() default true;
}
