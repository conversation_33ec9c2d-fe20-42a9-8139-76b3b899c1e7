package com.tth.framework.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 文件URL目标字段注解
 * 用于标记接收文件URL的目标字段
 * 
 * 使用方式：
 * <pre>
 * public class FairyTaleVO {
 *     &#064;MarkFileUrl(expireMinutes = 480, targetField = "chineseTitleAudioUrl")
 *     private Long chineseTitleAudioId;  // 源字段：文件ID
 *     
 *     &#064;FileUrlTarget
 *     private String chineseTitleAudioUrl;  // 目标字段：文件URL
 * }
 * </pre>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FileUrlTarget {
    
    /**
     * 对应的源字段名称
     * 如果不指定，则根据命名约定自动推断
     * 例如：chineseTitleAudioUrl -> chineseTitleAudioId
     */
    String sourceField() default "";
}
