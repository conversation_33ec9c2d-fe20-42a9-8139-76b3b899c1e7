package com.tth.framework.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据字典类型注解
 * 用于标识枚举类为数据字典类型
 * 添加了该注解，表示要自动添加进 dict data 中
 */
@Target(ElementType.TYPE) // 该注解可以应用于类或枚举
@Retention(RetentionPolicy.RUNTIME) // 运行时可用
public @interface MarkDictType {

    /**
     * 字典类型名称
     * 如果不指定，将使用枚举类的简单名称
     */
    String value();
}
