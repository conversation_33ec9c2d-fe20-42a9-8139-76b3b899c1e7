package com.tth.framework.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tth.framework.config.jackson.FileUrlSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 文件URL标记注解
 * 组合注解，内部使用@JsonSerialize，支持自定义过期时间
 *
 * 使用方式：
 * <pre>
 * &#064;MarkFileUrl   // 默认60分钟，自动推断目标字段
 * private Long avatarId;
 *
 * &#064;MarkFileUrl(expireMinutes = 120, targetField = "avatarUrl")  // 指定目标字段
 * private Long avatarId;
 *
 * // 对应的目标字段
 * &#064;FileUrlTarget
 * private String avatarUrl;
 * </pre>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = FileUrlSerializer.class)
public @interface MarkFileUrl {
    
    /**
     * 预签名URL过期时间（分钟），最大为600分钟，即10小时，超过600自动设置为600
     * 默认60分钟
     */
    int expireMinutes() default 60;
    
    /**
     * 当文件ID为null或文件不存在时的处理方式
     * true: 返回null
     * false: 返回 {"fileId": null, "url": null}
     */
    boolean nullWhenNotFound() default false;

    /**
     * 目标字段名称（接收URL的字段）
     * 如果不指定，则根据命名约定自动推断
     * 例如：avatarId -> avatarUrl, chineseTitleAudioId -> chineseTitleAudioUrl
     */
    String targetField() default "";
}
