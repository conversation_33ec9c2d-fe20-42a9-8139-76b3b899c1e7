package com.tth.framework.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tth.framework.config.jackson.FileUrlSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 文件URL标记注解（已废弃，推荐使用前端按需加载）
 *
 * 新的推荐方式：
 * <pre>
 * // 后端：只返回文件ID
 * private Long avatarId;
 *
 * // 前端：按需获取URL
 * const avatarUrl = await fileService.getFileUrl(avatarId);
 * </pre>
 *
 * @deprecated 推荐使用前端按需加载方案，性能更优
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = FileUrlSerializer.class)
public @interface MarkFileUrl {
    
    /**
     * 预签名URL过期时间（分钟），最大为600分钟，即10小时，超过600自动设置为600
     * 默认60分钟
     */
    int expireMinutes() default 60;
    
    /**
     * 当文件ID为null或文件不存在时的处理方式
     * true: 返回null
     * false: 返回 {"fileId": null, "url": null}
     */
    boolean nullWhenNotFound() default false;

    /**
     * 目标字段名称（接收URL的字段）
     * 如果不指定，则根据命名约定自动推断
     * 例如：avatarId -> avatarUrl, chineseTitleAudioId -> chineseTitleAudioUrl
     */
    String targetField() default "";
}
