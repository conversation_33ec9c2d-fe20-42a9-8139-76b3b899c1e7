package com.tth.framework.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tth.framework.config.jackson.MultiFileUrlSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 多文件URL标记注解
 * 组合注解，内部使用@JsonSerialize，支持自定义过期时间
 * 用于处理逗号分隔的多个文件ID字符串
 * 
 * 使用方式：
 * <pre>
 * &#064;MarkMultiFileUrl   // 默认60分钟
 * private String imageIds = "123,456,789";
 * 
 * &#064;MarkMultiFileUrl(expireMinutes = 120)  // 自定义120分钟
 * private String attachmentIds = "111,222";
 * 
 * &#064;MarkMultiFileUrl(expireMinutes = 30, nullWhenNotFound = false)
 * private String documentIds = "333,444,555";
 * </pre>
 * 
 * 序列化结果：
 * <pre>
 * [
 *   {"fileId": "123", "url": "https://..."},
 *   {"fileId": "456", "url": "https://..."},
 *   {"fileId": "789", "url": "https://..."}
 * ]
 * </pre>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = MultiFileUrlSerializer.class)
public @interface MarkMultiFileUrl {
    
    /**
     * 预签名URL过期时间（分钟），最大为600分钟，即10小时，超过600自动设置为600
     * 默认60分钟
     */
    int expireMinutes() default 60;
    
    /**
     * 当文件ID为null或文件不存在时的处理方式
     * true: 对于不存在的文件，在数组中返回null
     * false: 对于不存在的文件，在数组中返回 {"fileId": "xxx", "url": null}
     */
    boolean nullWhenNotFound() default false;
    
    /**
     * 文件ID分隔符，默认为逗号
     */
    String separator() default ",";
}
