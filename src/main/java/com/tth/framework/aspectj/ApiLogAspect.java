package com.tth.framework.aspectj;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.enums.LogLevel;
import com.tth.common.enums.SuccessStatusEnum;
import com.tth.framework.event.ApiLogEvent;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.utils.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import com.tth.framework.interceptor.PermissionInterceptor;
import com.tth.modules.system.entity.ApiRequestLog;
import com.tth.modules.system.enums.LogTypeEnum;
import com.tth.modules.system.service.ApiRequestLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.annotation.Annotation;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadLocalRandom;
import com.tth.framework.filter.CustomContentCachingRequestWrapper;

/**
 * API日志切面
 * 用于记录API请求日志
 */
@Slf4j
@Aspect
@Component
public class ApiLogAspect {

    @Resource(name = "commonExecutor")
    private Executor executor;

    @Resource
    private ApiRequestLogService apiLogService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 慢请求阈值，单位毫秒
     */
    private static final long SLOW_REQUEST_THRESHOLD = 10000;

    /**
     * 匹配所有Controller方法
     */
    @Pointcut("execution(* com.tth.modules.*.controller.*.*(..))")
    public void apiPointcut() {}

    /**
     * 匹配带有@ApiLog注解的方法
     */
    @Pointcut("@annotation(com.tth.framework.annotation.ApiLog)")
    public void apiLogPointcut() {}

    /**
     * 处理带有@ApiLog注解的请求
     * 使用@annotation参数绑定获取注解实例
     */
    @Around("apiLogPointcut() && @annotation(apiLog)")
    public Object aroundAnnotated(ProceedingJoinPoint point, ApiLog apiLog) throws Throwable {
        return processRequest(point, apiLog);
    }

    /**
     * 处理所有API请求（基础日志）
     */
    @Around("apiPointcut() && !apiLogPointcut()")
    public Object aroundAll(ProceedingJoinPoint point) throws Throwable {
        // 为没有注解的方法创建一个基础级别的默认日志配置
        ApiLog defaultLog = createDefaultApiLog();
        return processRequest(point, defaultLog);
    }

    /**
     * 处理请求并发布事件
     */
    private Object processRequest(ProceedingJoinPoint point, ApiLog apiLog) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return point.proceed(); // 非Web请求，直接执行目标方法
        }

        HttpServletRequest request = attributes.getRequest();
        HttpServletResponse response = attributes.getResponse();

        // 执行目标方法
        Object result = point.proceed(); // 直接执行目标方法，如果有异常直接抛出

        // 计算耗时
        long duration = System.currentTimeMillis() - startTime;

        // 发布API日志事件，由事件监听器处理日志记录
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                result,
                null, // 正常请求没有异常
                BaseResponseCode.SUCCESS.getCode(),
                duration,
                apiLog
        ));

        return result;
    }

    /**
     * 判断是否为慢请求
     */
    private boolean isSlowRequest(long startTime) {
        return System.currentTimeMillis() - startTime > SLOW_REQUEST_THRESHOLD;
    }

    /**
     * 判断是否应该保存到数据库
     */
    private boolean shouldSaveToDatabase(LogLevel level, long duration, SuccessStatusEnum successStatusEnum) {
        // 详细级别的日志总是保存
        if (level == LogLevel.DETAILED) {
            return true;
        }

        // 标准级别的日志总是保存
        if (level == LogLevel.STANDARD) {
            return true;
        }

        // 失败的请求总是保存
        if (successStatusEnum != SuccessStatusEnum.SUCCESS) {
            return true;
        }

        // 慢请求总是保存
        if (duration > SLOW_REQUEST_THRESHOLD) {
            return true;
        }

        // 基础级别的日志，采样保存（例如每100个请求保存1个）
        if (level == LogLevel.BASIC) {
            return ThreadLocalRandom.current().nextInt(100) == 0;
        }

        return false;
    }

    /**
     * 获取请求头信息
     */
    private String getHeadersAsString(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        return JSONUtil.toJsonStr(headers);
    }

    /**
     * 获取请求体
     * 直接从请求对象中获取请求体
     *
     * @param request HTTP请求对象
     * @return 请求体字符串，如果无法获取则返回空字符串
     */
    private String getRequestBody(HttpServletRequest request) {
        // 检查请求是否被包装为CustomContentCachingRequestWrapper
        if (request instanceof CustomContentCachingRequestWrapper wrapper) {
            // 直接使用getBodyString方法，已经在构造方法中根据请求类型进行了处理
            return wrapper.getBodyString();
        }

        log.debug("请求未被包装为 CustomContentCachingRequestWrapper，无法获取请求体");
        return "";
    }

    /**
     * 为没有注解的方法创建一个默认的ApiLog注解
     */
    private ApiLog createDefaultApiLog() {
        return new ApiLog() {
            @Override
            public Class<? extends Annotation> annotationType() {
                return ApiLog.class;
            }

            @Override
            public String value() {
                return "采样日志";
            }

            @Override
            public String module() {
                return "自动日志";
            }

            @Override
            public LogLevel level() {
                return LogLevel.BASIC;
            }

            @Override
            public boolean logParams() {
                return true;
            }

            @Override
            public boolean logResponse() {
                return true;
            }

            @Override
            public boolean logHeaders() {
                return false;
            }
        };
    }

    /**
     * 通用的日志记录方法
     * 用于处理正常请求和异常请求的日志记录
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param module 模块名称
     * @param operation 操作名称
     * @param responseStatus 响应状态码
     * @param bizStatus 业务成功状态
     * @param responseBody 响应体
     * @param errorMsg 错误信息
     * @param duration 请求耗时
     * @param logLevel 日志级别
     * @param logParams 是否记录请求参数
     * @param logHeaders 是否记录请求头
     * @param logResponse 是否记录响应体
     */
    private void saveApiLog(HttpServletRequest request, HttpServletResponse response,
                          String module, String operation, int responseStatus,
                          SuccessStatusEnum bizStatus, String responseBody, String errorMsg,
                          long duration, LogLevel logLevel, boolean logParams,
                          boolean logHeaders, boolean logResponse) {
        // 获取请求ID
        String requestId = ThreadContext.get(PermissionInterceptor.REQUEST_ID);
        if (requestId == null) {
            log.warn("日志记录中未找到请求ID");
            return;
        }

        // 创建日志对象
        ApiRequestLog apiRequestLog = new ApiRequestLog();
        apiRequestLog.setLogType(LogTypeEnum.SYSTEM_API); // 系统API日志
        apiRequestLog.setRequestId(requestId);
        apiRequestLog.setUrl(request.getRequestURI());
        apiRequestLog.setMethod(request.getMethod());
        apiRequestLog.setModule(module);
        apiRequestLog.setOperation(operation);
        String ip = ThreadContext.get(PermissionInterceptor.CURRENT_IP);
        apiRequestLog.setIp(ip);
        // 不在同步线程中获取IP位置信息，避免性能延迟
        apiRequestLog.setUserAgent(request.getHeader("User-Agent"));
        apiRequestLog.setDuration(duration);

        try {
            String contextUserId = ThreadContext.get(PermissionInterceptor.USER_ID);
            if(StrUtil.isNotBlank(contextUserId)){
                long userId = Long.parseLong(contextUserId);
                apiRequestLog.setUserId(userId);
            }
        } catch (Exception e) {
            // 忽略获取用户信息的异常
            log.debug("获取用户信息失败: {}", e.getMessage());
        }

        // 设置响应状态
        apiRequestLog.setResponseStatus(responseStatus);
        apiRequestLog.setBizStatus(bizStatus);
        apiRequestLog.setHttpStatus(HttpStatus.OK.value());

        // 记录请求体
        if (logParams) {
            try {
                apiRequestLog.setQueryParams(request.getQueryString());
                // 获取请求体，对于文件上传请求，只记录文件元数据
                String requestBody = getRequestBody(request);
                apiRequestLog.setRequestBody(requestBody);
            } catch (Exception e) {
                log.warn("记录请求体失败: {}", e.getMessage());
            }
        }

        // 记录请求头
        if (logHeaders) {
            apiRequestLog.setHeaderParams(getHeadersAsString(request));
        }

        // 记录响应体
        if (responseBody != null) {
            apiRequestLog.setResponseBody(responseBody);
        }

        // 记录错误信息
        if (errorMsg != null) {
            apiRequestLog.setErrorMsg(errorMsg);
        }

        // 判断是否应该保存到数据库
        boolean shouldSave = shouldSaveToDatabase(logLevel, duration, apiRequestLog.getBizStatus());

        // 保存到数据库
        if (shouldSave) {
            try {
                // 在异步操作前捕获当前线程的ThreadContext数据
                final String capturedUserId = ThreadContext.get(PermissionInterceptor.USER_ID);

                // 异步保存到数据库
                CompletableFuture.runAsync(() -> {
                    try {
                        // 在新线程中恢复ThreadContext数据
                        if (capturedUserId != null) {
                            ThreadContext.put(PermissionInterceptor.USER_ID, capturedUserId);
                        }

                        try {
                            // 在异步线程中获取IP位置信息
                            if (apiRequestLog.getIp() != null) {
                                apiRequestLog.setIpPosition(IpUtil.getRealAddressByIP(apiRequestLog.getIp()));
                            }

                            apiLogService.save(apiRequestLog);
                            log.debug("日志保存成功，请求ID: {}", requestId);
                        } catch (Exception e) {
                            log.error("保存API日志失败: {}", e.getMessage(), e);
                        } finally {
                            // 清理新线程中的ThreadContext数据
                            ThreadContext.remove(PermissionInterceptor.USER_ID);
                        }
                    } catch (Exception e) {
                        log.error("在异步线程中处理ThreadContext失败: {}", e.getMessage(), e);
                    }
                }, executor);
            } catch (Exception e) {
                log.error("准备保存API日志失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 监听API日志事件
     * 处理正常请求和异常请求的日志记录
     */
    @EventListener
    public void onApiLogEvent(ApiLogEvent event) {
        if (event.isExceptionEvent()) {
            log.debug("收到API异常事件，开始记录日志");
        } else {
            log.debug("收到API请求事件，开始记录日志");
        }

        HttpServletRequest request = event.getRequest();
        HttpServletResponse response = event.getResponse();
        Object result = event.getResult();
        Exception exception = event.getException();
        int responseStatus = event.getResponseStatus();
        SuccessStatusEnum bizStatus = event.getBizStatus();
        long duration = event.getDuration();
        String module = event.getModule();
        String operation = event.getOperation();

        // 处理响应体
        String responseBody = null;
        if (event.isExceptionEvent()) {
            // 异常情况下，响应体已经是字符串
            responseBody = (String) result;
        } else if (event.getApiLog() != null) {
            // 正常情况下，需要将响应对象转换为字符串
            ApiLog apiLog = event.getApiLog();
            if ((apiLog.level() != LogLevel.BASIC && apiLog.logResponse()) || isSlowRequest(duration)) {
                try {
                    responseBody = objectMapper.writeValueAsString(result);

                    // 对于详细级别，保存完整响应；对于其他级别，可能需要截断
                    if (apiLog.level() != LogLevel.DETAILED && responseBody.length() > 1000) {
                        responseBody = responseBody.substring(0, 1000) + "...";
                    }
                } catch (Exception e) {
                    log.warn("记录响应结果失败: {}", e.getMessage());
                }
            }
        }

        // 错误信息
        String errorMsg = null;
        if (exception != null) {
            errorMsg = ExceptionUtils.getStackTrace(exception);
        }

        // 日志级别和参数设置
        LogLevel logLevel = LogLevel.DETAILED; // 默认使用详细级别
        boolean logParams = true;
        boolean logHeaders = true;
        boolean logResponse = true;

        if (event.getApiLog() != null) {
            ApiLog apiLog = event.getApiLog();
            logLevel = apiLog.level();
            logParams = apiLog.logParams();
            logHeaders = apiLog.logHeaders();
            logResponse = apiLog.logResponse();
        }

        // 调用通用的日志记录方法
        saveApiLog(
                request,
                response,
                module,
                operation,
                responseStatus,
                bizStatus,
                responseBody,
                errorMsg,
                duration,
                logLevel,
                logParams,
                logHeaders,
                logResponse
        );
    }
}
