package com.tth.framework.aspectj;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import com.tth.framework.exception.code.PermissionExceptionCode;
import com.tth.framework.exception.core.PermissionException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import com.tth.framework.annotation.RateLimiter;
import com.tth.framework.enums.LimitType;
import com.tth.framework.utils.RequestContextUtil;
import com.tth.common.utils.RedisUtil;

/**
 * 限流处理
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class RateLimiterAspect {

    private RedisScript<Long> limitScript;

    @Autowired
    public void setLimitScript(RedisScript<Long> limitScript) {
        this.limitScript = limitScript;
    }

    @Before("@annotation(rateLimiter)")
    public void doBefore(JoinPoint point, RateLimiter rateLimiter) {
        int time = rateLimiter.time();
        int count = rateLimiter.count();

        String combineKey = getCombineKey(rateLimiter, point);
        // 创建String类型的键列表
        List<String> keys = Collections.singletonList(combineKey);
        // 使用RedisUtil执行Lua脚本
        Long number = RedisUtil.execute(limitScript, keys, count, time);
        // 防御性检查，在Redis连接异常等情况下，number可能为null
        if (number == null || number.intValue() > count) {
            throw new PermissionException(PermissionExceptionCode.API_RATE_LIMIT_EXCEEDED, "访问过于频繁，请稍候再试");
        }
        log.info("限流请求'{}',当前请求'{}',缓存key'{}'", count, number.intValue(), combineKey);
    }

    /**
     * 生成限流键
     *
     * @param rateLimiter 限流注解
     * @param point 切点
     * @return 限流键
     */
    public String getCombineKey(RateLimiter rateLimiter, JoinPoint point) {
        StringBuilder stringBuffer = new StringBuilder(rateLimiter.key());
        if (rateLimiter.limitType() == LimitType.IP) {
            stringBuffer.append(RequestContextUtil.getClientIp()).append("-");
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = method.getDeclaringClass();
        stringBuffer.append(targetClass.getName()).append("-").append(method.getName());
        return stringBuffer.toString();
    }
}
