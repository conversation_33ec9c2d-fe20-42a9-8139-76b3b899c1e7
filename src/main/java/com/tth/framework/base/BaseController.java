package com.tth.framework.base;

import cn.dev33.satoken.stp.StpUtil;
import com.tth.framework.annotation.RepeatSubmit;
import com.tth.framework.constants.PermissionConstants;
import com.tth.framework.exception.code.PermissionExceptionCode;
import com.tth.framework.exception.code.SystemExceptionCode;
import com.tth.framework.exception.code.ValidationExceptionCode;
import com.tth.framework.exception.core.AuthException;
import com.tth.framework.exception.core.SystemException;
import com.tth.framework.exception.core.ValidationException;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.response.R;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
public class BaseController<T extends BaseEntity> {

    public BaseService<T> getBaseService() throws Exception {
        throw new Exception("getBaseService Error");
    }

    /**
     * 获取模块名称，用于构建权限标识
     * 子类必须覆盖此方法提供具体的模块名称
     * 默认返回空字符串，表示无权限
     */
    protected String getModuleName() {
        return "";
    }

    /**
     * 检查当前用户是否有指定模块的指定操作权限
     */
    protected void checkPermission(String operation) {
        if(StpUtil.hasRole("SUPER_ADMIN")){
            return;
        }

        String moduleName = getModuleName();
        if (moduleName.isEmpty()) {
            throw new AuthException(PermissionExceptionCode.MODULE_NAME_NOT_SPECIFIED);
        }

        String permission = moduleName + ":" + operation;
        if (!StpUtil.hasPermission(permission)) {
            throw new AuthException(PermissionExceptionCode.FORBIDDEN);
        }
    }

    @RepeatSubmit
    @Operation(summary = "标准添加记录", description = "权限后缀：【:" + PermissionConstants.Operation.INSERT + "】")
    @PostMapping("/insert")
    public R<T> insert(@RequestBody T entity) throws Exception {
        // 检查新增权限
        checkPermission(PermissionConstants.Operation.INSERT);

        entity.setId(null);
        this.getBaseService().save(entity);
        return R.success(entity);
    }

    @Operation(summary = "标准根据ID删除记录", description = "权限后缀：【:" + PermissionConstants.Operation.DELETE + "】")
    @PostMapping("/deleteById")
    public R<Boolean> deleteById(Long id) throws Exception {
        checkPermission(PermissionConstants.Operation.DELETE);

        this.getBaseService().deleteById(id);
        return R.success();
    }

    @Operation(summary = "标准根据IDs批量删除记录", description = "权限后缀：【:" + PermissionConstants.Operation.DELETE + "】")
    @PostMapping("/deleteByIdList")
    public R<Boolean> deleteByByIdList(@RequestBody IdListDTO idListDTO) throws Exception {
        checkPermission(PermissionConstants.Operation.DELETE);

        this.getBaseService().deleteByByIdList(idListDTO.getIdList());
        return R.success();
    }

    @Operation(summary = "标准列表或分页查询", description = "权限后缀：【:" + PermissionConstants.Operation.QUERY + "】")
    @PostMapping("/listOrPage")
    public R<PageResult<T>> listOrPage(@RequestBody Paging paging) throws Exception {
        checkPermission(PermissionConstants.Operation.QUERY);

        PageResult<T> pageResult = this.getBaseService().listOrPage(paging);
        return R.success(pageResult);
    }

    @Operation(summary = "标准根据ID查询记录", description = "权限后缀：【:" + PermissionConstants.Operation.QUERY + "】")
    @GetMapping("/queryById")
    public R<T> queryById(Long id) throws Exception {
        checkPermission(PermissionConstants.Operation.QUERY);

        if (id == null) {
            throw new ValidationException(ValidationExceptionCode.ID_REQUIRED);
        }

        T entity = this.getBaseService().queryById(id);
        if(entity == null){
            return R.custom(BaseResponseCode.NULL);
        }
        return R.success(entity);
    }

    @Operation(summary = "标准根据ID查询记录", description = "权限后缀：【:" + PermissionConstants.Operation.QUERY + "】<br>与queryById不同，getById仅仅会获取当前表的记录，而非关联表的")
    @GetMapping("/getById")
    public R<T> getById(Long id) throws Exception {
        checkPermission(PermissionConstants.Operation.QUERY);

        if (id == null) {
            throw new ValidationException(ValidationExceptionCode.ID_REQUIRED);
        }

        T entity = this.getBaseService().getById(id);
        if(entity == null){
            return R.custom(BaseResponseCode.NULL);
        }
        return R.success(entity);
    }

    @Operation(summary = "标准可选更新记录", description = "权限后缀：【:" + PermissionConstants.Operation.UPDATE + "】<br>根据id对非空的字段进行更新，忽略null值的字段，如果要设为null，则需要在canNullFields中指定，多个字段用英文逗号分隔")
    @PostMapping("/updateByIdSelective")
    public R<T> updateByIdSelective(@RequestBody T entity) throws Exception {
        checkPermission(PermissionConstants.Operation.UPDATE);

        if (entity.getId() == null) {
            throw new ValidationException(ValidationExceptionCode.ID_REQUIRED);
        }
        this.getBaseService().updateByIdSelective(entity);
        return R.success(this.getBaseService().getById(entity.getId()));
    }

    @Operation(summary = "标准批量更新记录", description = "权限后缀：【:" + PermissionConstants.Operation.UPDATE + "】<br>根据id批量更新记录，只有可选更新，忽略null值")
    @PostMapping("/updateBatchById")
    public R<T> updateBatch(@RequestBody EntityListDTO<T> entityListDTO) throws Exception {
        checkPermission(PermissionConstants.Operation.UPDATE);

        // 验证每个ID不为空
        for (T t : entityListDTO.getEntityList()) {
            if (t.getId() == null) {
                throw new ValidationException(ValidationExceptionCode.ID_REQUIRED);
            }
        }
        int updateStatus = this.getBaseService().updateBatchByIdSelective(entityListDTO.getEntityList());
        if (updateStatus < 0) {
            throw new SystemException(SystemExceptionCode.INTERNAL_SERVER_ERROR);
        }
        return R.success();
    }

}
