package com.tth.framework.base;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BaseModel
 */
@Schema(description = "通用实体类")

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BaseEntity implements Serializable {

    @Schema(description = "主键ID", example = "10000")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "创建人", example = "10000")
    @TableField(fill = FieldFill.INSERT)
    private Long createdBy;

    @Schema(description = "创建时间", example = "2024-11-01 12:12:12")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @Schema(description = "更新人", example = "10000")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifiedBy;

    @Schema(description = "修改时间", example = "2024-11-01 12:12:12")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;

    @Schema(description = "删除人", example = "10000")
    private Long deletedBy;

    @Schema(description = "删除时间", example = "2024-11-01 12:12:12")
    private LocalDateTime deletedTime;

    @Version
    @Schema(description = "版本号", example = "1")
    private Integer version;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否删除", example = "false")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Boolean deleted;

    @Schema(description = "更新时可以为null的字段")
    @TableField(exist = false)
    private String canNullFields;
}
