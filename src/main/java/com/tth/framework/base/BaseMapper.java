package com.tth.framework.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface BaseMapper<T extends BaseEntity> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<T> {

    /**
     * 自定义删除-逻辑删除(危险操作)
     * @param wrapper 条件
     */
    int deleteCustom(@Param("ew") Wrapper<T> wrapper);

    /**
     * 分页条件查询
     *
     * @param wrapper 分页条件
     * @param offset 开始行
     * @param count 返回的行数
     * @return 返回分页结果
     * @throws SQLException SQL异常
     */
    List<T> queryByPage(@Param("ew") Wrapper<T> wrapper,
                        @Param("offset") Integer offset,
                        @Param("count") Integer count) throws SQLException;

    /**
     * @date 2025/2/20 14:22
     * @param wrapper 分页条件
     * @return 返回分页结果
     * @throws SQLException SQL异常
     */
    Long queryCount(@Param("ew") Wrapper<T> wrapper) throws SQLException;

    /**
     * 根据ID查询记录 标准查询
     *
     * @param id id
     * @return 返回结果
     * @throws SQLException 异常
     */
    T queryById(@Param("id") Long id) throws SQLException;

    /**
     * 根据ID更新对象
     * @param entity 对象
     * @return 返回结果
     * @throws SQLException SQL 异常
     */
    int updateByIdSelective(T entity) throws SQLException;

    /**
     * 根据ID批量更新对象
     * @param entities 对象
     * @return 返回结果
     * @throws SQLException SQL 异常
     */
    int updateBatchByIdSelective(List<T> entities) throws SQLException;

    /**
     * 自定义连表分页查询
     * 在XML中实现具体的SQL语句
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    <E> IPage<E> selectJoinPage(IPage<E> page, @Param("params") Map<String, Object> params);
}
