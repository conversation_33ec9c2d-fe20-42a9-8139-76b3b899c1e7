package com.tth.framework.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;

import java.util.Map;

import java.util.List;

public interface BaseService<T extends BaseEntity> extends IService<T> {

    /**
     * 根据ID删除记录
     * @param id 记录ID
     * @throws Exception 异常
     */
    void deleteById(Long id) throws Exception;

    void deleteByByIdList(List<Long> idList) throws Exception;

    /**
     * 标准分页查询
     *
     * @param paging 分页参数
     * @return 返回查询结果
     * @throws Exception 异常
     */
    PageResult<T> listOrPage(Paging paging) throws Exception;

    /**
     * 标准根据Id查询
     *
     * @param id id
     * @return 返回结果
     * @throws Exception 异常
     */
    T queryById(Long id) throws Exception;


    /**
     * 根据ID更新记录
     * @param entity 更新对象
     */
    void updateByIdSelective(T entity) throws Exception;

    int updateBatchByIdSelective(List<T> entityList) throws Exception;

    /**
     * 标准分页查询
     *
     * @param wrapper wrapper
     * @return 返回对象
     * @throws Exception 异常
     */
    List<T> queryByExamplePage(Wrapper<T> wrapper) throws Exception;


    /**
     * 查询一条记录
     *
     * @param wrapper wrapper
     * @return 返回对象
     * @throws Exception 异常
     */
    T queryOneByExamplePage(Wrapper<T> wrapper) throws Exception;


    /**
     * 自定义删除-物理删除(危险操作)
     * @param wrapper 条件
     * @throws Exception 异常
     */
    void deleteCustom(Wrapper<T> wrapper) throws Exception;

    /**
     * 使用MyBatis Plus原生分页功能进行分页查询
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    <E extends Page<T>> E pageByMybatisPlus(E page, Wrapper<T> queryWrapper);

    /**
     * 连表分页查询
     * 支持自定义返回结果类型
     *
     * @param page 分页参数
     * @param params 查询参数
     * @param <E> 返回结果类型
     * @return 分页结果
     */
    <E> IPage<E> pageJoin(IPage<E> page, Map<String, Object> params);
}
