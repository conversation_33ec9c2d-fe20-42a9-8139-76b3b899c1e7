package com.tth.framework.base;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tth.framework.exception.code.ValidationExceptionCode;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.exception.core.ValidationException;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.utils.AuthUtil;
import com.tth.framework.utils.Tool;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
public class BaseServiceImpl<M extends BaseMapper<N>, N extends BaseEntity> extends ServiceImpl<M, N> implements BaseService<N> {

    /**
     * 当前泛型T的类
     */
    private final Class<N> entityClazz;

    public BaseServiceImpl() {
        this.entityClazz = getEntityClazz(this.getClass());
    }

    /**
     * 获取service的泛型真实类型
     */
    @SuppressWarnings("unchecked")
    private Class<N> getEntityClazz(Class<?> clazz) {
        if (clazz == null) {
            return null;
        }
        if (Object.class == clazz) {
            return null;
        }
        // 当前对象的直接超类的 Type
        Type genericSuperclass = clazz.getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType parameterizedType) {
            // 返回表示此类型实际类型参数的 Type 对象的数组
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            return (Class<N>) actualTypeArguments[1];
        }
        // 如果是BasicServiceImpl的子类，则继续递归
        boolean isFather = BaseServiceImpl.class.isAssignableFrom(clazz);
        if (isFather) {
            Class<?> superClass = clazz.getSuperclass();
            return getEntityClazz(superClass);
        }
        return null;
    }

    @Override
    public void deleteById(Long id) throws Exception {
        if (id == null) {
            throw new ValidationException(ValidationExceptionCode.ID_REQUIRED);
        }

        UpdateWrapper<N> uw = new UpdateWrapper<>();
        uw.set("deleted_by", null);
        uw.set("deleted_time", LocalDateTime.now());
        uw.set("deleted", true);
        uw.eq("id", id);

        boolean result = this.update(uw);
        if(!result){
            throw new BizException(BaseResponseCode.FAIL);
        }
    }

    @Override
    public void deleteByByIdList(List<Long> idList) {
        UpdateWrapper<N> uw = new UpdateWrapper<>();
        uw.set("deleted_by", AuthUtil.getUserId());
        uw.set("deleted_time", LocalDateTime.now());
        uw.set("deleted", true);
        uw.in("id", idList);

        boolean result = this.update(uw);
        if(!result){
            throw new BizException(BaseResponseCode.FAIL);
        }
    }

    @Override
    public PageResult<N> listOrPage(Paging paging) throws Exception {
        QueryWrapper<N> queryWrapper = Tool.getLambdaQueryWrapper(paging);
        if(!paging.getPageFlag()){  // 如果不分页
            List<N> list = this.getBaseMapper().queryByPage(
                    queryWrapper,0, 10000);
            return new PageResult<>(list);
        }

        List<N> list = this.getBaseMapper().queryByPage(
                queryWrapper, // 查询条件
                (paging.getPageNum() - 1) * paging.getPageSize(), // 查询开始下标
                paging.getPageSize()); // 查询记录数量
        if (paging.getCountFlag()) {
            long count = this.getBaseMapper().queryCount(queryWrapper);
            return new PageResult<>(paging.getPageNum(), paging.getPageSize(), count, list);
        }
        return new PageResult<>(paging.getPageNum(), paging.getPageSize(), list);
    }

    @Override
    public List<N> queryByExamplePage(Wrapper<N> wrapper) throws Exception {
        return this.getBaseMapper().queryByPage(
                wrapper,
                0,
                10000);
    }

    @Override
    public N queryOneByExamplePage(Wrapper<N> wrapper) throws Exception {
        List<N> list = this.getBaseMapper().queryByPage(
                wrapper, // 查询条件
                0, // 查询开始下标
                1); // 查询记录数量
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public N queryById(Long id) throws Exception {
        return this.getBaseMapper().queryById(id);
    }

    @Override
    public void deleteCustom(Wrapper<N> wrapper) throws Exception {
        if (wrapper == null || wrapper.isEmptyOfWhere()) {
            throw new ValidationException(ValidationExceptionCode.PARAMS_REQUIRED, "删除条件不允许为空");
        }
        int num = this.getBaseMapper().deleteCustom(wrapper);
        if (num < 0) {
            throw new BizException(BaseResponseCode.SUCCESS, "删除记录失败");
        }
    }

    @Override
    public void updateByIdSelective(N entity) throws Exception {
        QueryWrapper<N> qw = new QueryWrapper<N>()
                .select("version")
                .eq("id", entity.getId());
        N n = this.getBaseMapper().selectOne(qw);
        if(ObjUtil.isNull(n)){
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND);
        }
        entity.setVersion(n.getVersion());

        Long userId = AuthUtil.getUserId();
        if(ObjUtil.isNull(userId)){
            userId = 0L;
        }
        entity.setModifiedBy(userId);

        if(this.getBaseMapper().updateByIdSelective(entity) <= 0){
            throw new BizException(BaseResponseCode.FAIL);
        }
    }

    /**
     * 只有可选更新，而且没有乐观锁限制
     */
    @Override
    public int updateBatchByIdSelective(List<N> entityList) throws Exception {
        Long userId = AuthUtil.getUserId();
        if(ObjUtil.isNull(userId)){
            userId = 0L;
        }
        for (N entity : entityList) {
            entity.setModifiedBy(userId);
        }
        return this.getBaseMapper().updateBatchByIdSelective(entityList);
    }

    @Override
    public <E extends Page<N>> E pageByMybatisPlus(E page, Wrapper<N> queryWrapper) {
        return this.baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public <E> IPage<E> pageJoin(IPage<E> page, Map<String, Object> params) {
        return this.baseMapper.selectJoinPage(page, params);
    }
}
