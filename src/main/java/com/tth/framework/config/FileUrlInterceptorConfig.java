package com.tth.framework.config;

import com.tth.framework.interceptor.FileUrlInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 文件URL拦截器配置
 * 将FileUrlInterceptor注册到MyBatis中
 */
@Configuration
public class FileUrlInterceptorConfig {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    private FileUrlInterceptor fileUrlInterceptor;

    @PostConstruct
    public void addInterceptor() {
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            sqlSessionFactory.getConfiguration().addInterceptor(fileUrlInterceptor);
        }
    }
}
