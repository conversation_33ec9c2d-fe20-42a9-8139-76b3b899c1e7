package com.tth.framework.config.jackson;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tth.framework.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量文件URL响应包装器
 * 用于在序列化响应时自动处理文件URL的批量查询
 */
@Slf4j
public class BatchFileUrlResponseWrapper {
    
    /**
     * 包装响应对象，自动处理文件URL批量查询
     * 
     * @param responseData 响应数据
     * @return 处理后的JSON字符串
     */
    public static String wrapResponse(Object responseData) {
        if (responseData == null) {
            return "null";
        }
        
        try {
            ObjectMapper objectMapper = SpringContextHolder.getBean(ObjectMapper.class);
            
            // 开始批量处理上下文
            FileUrlBatchProcessor.startBatch();
            
            try {
                // 第一次序列化：收集所有需要查询的文件ID
                String firstPass = objectMapper.writeValueAsString(responseData);
                
                // 执行批量查询
                FileUrlBatchProcessor.executeBatchQuery();
                
                // 第二次序列化：使用批量查询的结果
                String finalResult = objectMapper.writeValueAsString(responseData);
                
                log.debug("批量文件URL处理完成");
                return finalResult;
                
            } finally {
                // 确保清理ThreadLocal
                FileUrlBatchProcessor.endBatch();
            }
            
        } catch (JsonProcessingException e) {
            log.error("序列化响应数据失败", e);
            return "{\"error\":\"序列化失败\"}";
        } catch (Exception e) {
            log.error("批量文件URL处理失败", e);
            // 降级处理：直接序列化，不使用批量优化
            try {
                ObjectMapper objectMapper = SpringContextHolder.getBean(ObjectMapper.class);
                return objectMapper.writeValueAsString(responseData);
            } catch (JsonProcessingException ex) {
                log.error("降级序列化也失败", ex);
                return "{\"error\":\"序列化失败\"}";
            }
        }
    }
    
    /**
     * 检查对象是否包含文件URL字段
     * 用于判断是否需要使用批量处理
     */
    public static boolean containsFileUrlFields(Object obj) {
        if (obj == null) {
            return false;
        }
        
        // 简单的启发式检查：如果是集合或包含List字段，可能需要批量处理
        String className = obj.getClass().getSimpleName();
        return className.contains("List") || 
               className.contains("Page") || 
               className.contains("Collection") ||
               obj.toString().contains("fileId") ||
               obj.toString().contains("avatarId") ||
               obj.toString().contains("imageId");
    }
}
