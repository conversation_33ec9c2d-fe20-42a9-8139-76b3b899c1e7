package com.tth.framework.config.jackson;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tth.framework.interfaces.IEnumDesc;

import java.io.IOException;

/**
 * 通用枚举序列化器
 * 将实现了IEnum接口的枚举序列化为JSON对象
 * 只序列化固定的value和desc字段
 */
public class EnumSerializer extends JsonSerializer<Enum<?>> {

    @Override
    public void serialize(Enum<?> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        // 只处理实现了IEnum接口的枚举
        if (!(value instanceof IEnum)) {
            // 如果枚举没有实现IEnum接口，则使用默认的序列化方式
            gen.writeString(value.name());
            return;
        }

        gen.writeStartObject();

        // 写入value字段，使用枚举名称
        gen.writeStringField("value", value.name());

        // 写入desc字段
        String desc = getDescString(value);
        gen.writeStringField("desc", desc);

        // 写入dictType字段，使用枚举类的类名（驼峰转全大写+下划线）
        String className = value.getClass().getSimpleName();
        String dictType = StrUtil.toSymbolCase(className, '_').toUpperCase();
        gen.writeStringField("dictType", dictType);

        gen.writeEndObject();
    }

    /**
     * 获取枚举的描述信息
     * 如果枚举实现了IEnumDesc接口，则使用getDesc()方法的返回值
     * 否则使用枚举名称
     *
     * @param enumValue 枚举值
     * @return 枚举的描述信息
     */
    private String getDescString(Enum<?> enumValue) {
        // 如果枚举实现了IEnumDesc接口，则使用getDesc()方法
        if (enumValue instanceof IEnumDesc) {
            String desc = ((IEnumDesc) enumValue).getDesc();
            return desc != null ? desc : enumValue.name();
        }

        // 如果没有实现IEnumDesc接口，则返回枚举名称
        return enumValue.name();
    }
}