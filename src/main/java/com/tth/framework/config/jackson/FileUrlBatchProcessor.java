package com.tth.framework.config.jackson;

import cn.hutool.core.collection.CollUtil;
import com.tth.framework.utils.SpringContextHolder;
import com.tth.modules.system.service.OssFileService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件URL批量处理器
 * 用于在序列化过程中收集文件ID，批量查询文件URL，避免N+1查询问题
 */
@Slf4j
public class FileUrlBatchProcessor {
    
    private static final ThreadLocal<FileUrlContext> CONTEXT = new ThreadLocal<>();
    
    /**
     * 开始批量处理上下文
     */
    public static void startBatch() {
        CONTEXT.set(new FileUrlContext());
    }
    
    /**
     * 结束批量处理上下文并清理
     */
    public static void endBatch() {
        CONTEXT.remove();
    }
    
    /**
     * 获取文件URL（批量模式）
     * 如果在批量上下文中，先收集fileId，延迟查询
     * 如果不在批量上下文中，直接查询
     */
    public static String getFileUrl(Long fileId, int expireMinutes) {
        if (fileId == null) {
            return null;
        }
        
        FileUrlContext context = CONTEXT.get();
        if (context == null) {
            // 不在批量上下文中，直接查询
            OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
            return ossFileService.getFileUrlById(fileId, expireMinutes);
        }
        
        // 在批量上下文中，先收集fileId
        String cacheKey = fileId + ":" + expireMinutes;
        
        // 检查是否已经查询过
        if (context.urlCache.containsKey(cacheKey)) {
            return context.urlCache.get(cacheKey);
        }
        
        // 添加到待查询列表
        context.pendingFileIds.computeIfAbsent(expireMinutes, k -> new HashSet<>()).add(fileId);
        
        // 标记为待查询
        context.urlCache.put(cacheKey, null);
        
        return null; // 第一次返回null，等待批量查询
    }
    
    /**
     * 执行批量查询
     * 查询所有收集到的文件ID对应的URL
     */
    public static void executeBatchQuery() {
        executeBatchQuery(1000); // 默认最大批量大小
    }

    /**
     * 执行批量查询
     * 查询所有收集到的文件ID对应的URL
     *
     * @param maxBatchSize 最大批量查询大小
     */
    public static void executeBatchQuery(int maxBatchSize) {
        FileUrlContext context = CONTEXT.get();
        if (context == null || context.pendingFileIds.isEmpty()) {
            return;
        }

        try {
            OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
            int totalQueries = 0;

            // 按过期时间分组批量查询
            for (Map.Entry<Integer, Set<Long>> entry : context.pendingFileIds.entrySet()) {
                int expireMinutes = entry.getKey();
                Set<Long> fileIds = entry.getValue();

                if (CollUtil.isNotEmpty(fileIds)) {
                    // 分批查询，避免单次查询过多
                    List<Long> fileIdList = new ArrayList<>(fileIds);
                    for (int i = 0; i < fileIdList.size(); i += maxBatchSize) {
                        int endIndex = Math.min(i + maxBatchSize, fileIdList.size());
                        Set<Long> batchFileIds = new HashSet<>(fileIdList.subList(i, endIndex));

                        // 批量查询
                        Map<Long, String> urlMap = ossFileService.batchGetFileUrls(batchFileIds, expireMinutes);
                        totalQueries++;

                        // 更新缓存
                        for (Long fileId : batchFileIds) {
                            String cacheKey = fileId + ":" + expireMinutes;
                            String url = urlMap.get(fileId);
                            context.urlCache.put(cacheKey, url);
                        }
                    }
                }
            }

            // 清空待查询列表
            context.pendingFileIds.clear();

            log.debug("批量查询文件URL完成，执行了{}次批量查询", totalQueries);

        } catch (Exception e) {
            log.error("批量查询文件URL失败", e);
        }
    }
    
    /**
     * 获取已缓存的文件URL
     */
    public static String getCachedFileUrl(Long fileId, int expireMinutes) {
        if (fileId == null) {
            return null;
        }
        
        FileUrlContext context = CONTEXT.get();
        if (context == null) {
            return null;
        }
        
        String cacheKey = fileId + ":" + expireMinutes;
        return context.urlCache.get(cacheKey);
    }
    
    /**
     * 检查是否在批量处理上下文中
     */
    public static boolean isInBatchContext() {
        return CONTEXT.get() != null;
    }
    
    /**
     * 文件URL处理上下文
     */
    private static class FileUrlContext {
        // 按过期时间分组的待查询文件ID
        private final Map<Integer, Set<Long>> pendingFileIds = new HashMap<>();
        // 文件URL缓存 key: fileId:expireMinutes, value: url
        private final Map<String, String> urlCache = new ConcurrentHashMap<>();
    }
}
