package com.tth.framework.config.jackson;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.utils.SpringContextHolder;
import com.tth.modules.system.service.OssFileService;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 文件URL序列化器
 * 支持通过@MarkFileUrl注解自定义过期时间和null处理方式
 *
 * 使用方式：
 * <pre>
 * &#064;MarkFileUrl   // 默认60分钟
 * private Long avatarId;
 *
 * &#064;MarkFileUrl(expireMinutes  = 120)  // 自定义120分钟
 * private Long backgroundImageId;
 *
 * &#064;MarkFileUrl(expireMinutes  = 30, nullWhenNotFound = false)
 * private Long documentId;
 * </pre>
 */
@Slf4j
public class FileUrlSerializer extends JsonSerializer<Long> implements ContextualSerializer {

    private final int expireMinutes;
    private final boolean nullWhenNotFound;

    public FileUrlSerializer() {
        this(60, true);
    }

    public FileUrlSerializer(int expireMinutes, boolean nullWhenNotFound) {
        this.expireMinutes = expireMinutes;
        this.nullWhenNotFound = nullWhenNotFound;
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) {
        if (property != null) {
            MarkFileUrl markFileUrl = property.getAnnotation(MarkFileUrl.class);
            if (markFileUrl != null) {
                return new FileUrlSerializer(markFileUrl.expireMinutes(), markFileUrl.nullWhenNotFound());
            }
        }
        return this;
    }

    @Override
    public void serialize(Long fileId, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 如果文件ID为null
        if (fileId == null) {
            if (nullWhenNotFound) {
                gen.writeNull();
            } else {
                writeFileUrlObject(gen, null, null);
            }
            return;
        }

        // 最大过期时间限制为600分钟(10小时)
        int realExpireMinutes = expireMinutes;
        if(expireMinutes > 600) {
            realExpireMinutes = 600;
            log.warn("文件URL过期时间设置为：{}分钟，超过600分钟限制，已自动调整为600分钟限制", expireMinutes);
        }

        try {
            String fileUrl;

            // 检查是否在批量处理上下文中
            if (FileUrlBatchProcessor.isInBatchContext()) {
                // 先尝试从批量处理器获取缓存的URL
                fileUrl = FileUrlBatchProcessor.getCachedFileUrl(fileId, realExpireMinutes);
                if (fileUrl == null) {
                    // 如果没有缓存，添加到批量查询队列
                    FileUrlBatchProcessor.getFileUrl(fileId, realExpireMinutes);
                    // 第一次序列化时返回占位符，等待批量查询完成后重新序列化
                    writeFileUrlObject(gen, fileId, null);
                    return;
                }
            } else {
                // 不在批量上下文中，直接查询
                OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
                fileUrl = ossFileService.getFileUrlById(fileId, realExpireMinutes);
            }

            if (StrUtil.isBlank(fileUrl)) {
                // 文件不存在的情况
                if (nullWhenNotFound) {
                    gen.writeNull();
                } else {
                    writeFileUrlObject(gen, fileId, null);
                }
            } else {
                // 正常情况，写入完整的文件信息
                writeFileUrlObject(gen, fileId, fileUrl);
            }

        } catch (Exception e) {
            log.error("获取文件URL失败，文件ID: {}, 过期时间: {}分钟", fileId, realExpireMinutes, e);
            // 发生异常时的处理
            if (nullWhenNotFound) {
                gen.writeNull();
            } else {
                writeFileUrlObject(gen, fileId, null);
            }
        }
    }

    /**
     * 写入文件URL对象
     */
    private void writeFileUrlObject(JsonGenerator gen, Long fileId, String url) throws IOException {
        gen.writeStartObject();
        gen.writeObjectField("fileId", fileId);
        gen.writeStringField("url", url);
        gen.writeEndObject();
    }
}
