package com.tth.framework.config.jackson;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Jackson配置类
 * 使用Jackson2ObjectMapperBuilderCustomizer优雅地配置Jackson
 */
@Configuration
public class JacksonConfig {

    private static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    private static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";

    /**
     * Java 8日期时间模块定制器
     * 处理LocalDateTime、LocalDate、LocalTime的序列化和反序列化
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonJavaTimeCustomizer() {
        return builder -> {
            // 创建Java时间模块
            JavaTimeModule javaTimeModule = new JavaTimeModule();

            // LocalDateTime序列化和反序列化
            javaTimeModule.addSerializer(LocalDateTime.class,
                    new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT)));
            javaTimeModule.addDeserializer(LocalDateTime.class,
                    new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT)));

            // LocalDate序列化和反序列化
            javaTimeModule.addSerializer(LocalDate.class,
                    new LocalDateSerializer(DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT)));
            javaTimeModule.addDeserializer(LocalDate.class,
                    new LocalDateDeserializer(DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT)));

            // LocalTime序列化和反序列化
            javaTimeModule.addSerializer(LocalTime.class,
                    new LocalTimeSerializer(DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT)));
            javaTimeModule.addDeserializer(LocalTime.class,
                    new LocalTimeDeserializer(DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT)));

            // 注册模块
            builder.modules(javaTimeModule);

            // 禁用将日期写为时间戳
            builder.featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        };
    }

    /**
     * 通用Jackson特性定制器
     * 处理一些通用的序列化/反序列化特性
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonFeatureCustomizer() {
        return builder -> {
            // 反序列化特性
            builder.featuresToEnable(
                    // 允许将空字符串("")转换为null对象
                    DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT
            );

            builder.featuresToDisable(
                    // 忽略未知属性，防止反序列化失败
                    DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES
            );

            // 序列化特性
            builder.featuresToDisable(
                    // 允许序列化空对象
                    SerializationFeature.FAIL_ON_EMPTY_BEANS
            );
        };
    }

    /**
     * 枚举处理定制器
     * 为实现IEnum接口的枚举类型注册自定义序列化器
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonEnumCustomizer() {
        return builder -> {
            // 注册自定义的枚举序列化器
            builder.serializerByType(Enum.class, new EnumSerializer());
        };
    }

    /**
     * Long类型序列化定制器
     * 将所有Long类型字段序列化为String类型，避免前端精度丢失问题
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonLongCustomizer() {
        return builder -> {
            // 将Long类型序列化为String
            builder.serializerByType(Long.class, ToStringSerializer.instance);
            builder.serializerByType(long.class, ToStringSerializer.instance);
        };
    }
}