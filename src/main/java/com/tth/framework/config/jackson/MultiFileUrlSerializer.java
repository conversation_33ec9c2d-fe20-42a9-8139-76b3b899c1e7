package com.tth.framework.config.jackson;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.tth.framework.annotation.MarkMultiFileUrl;
import com.tth.framework.utils.SpringContextHolder;
import com.tth.modules.system.service.OssFileService;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.*;

/**
 * 多文件URL序列化器
 * 支持通过@MarkMultiFileUrl注解自定义过期时间和null处理方式
 * 处理逗号分隔的多个文件ID字符串
 *
 * 使用方式：
 * <pre>
 * &#064;MarkMultiFileUrl   // 默认60分钟
 * private String imageIds = "123,456,789";
 *
 * &#064;MarkMultiFileUrl(expireMinutes = 120)  // 自定义120分钟
 * private String attachmentIds = "111,222";
 *
 * &#064;MarkMultiFileUrl(expireMinutes = 30, nullWhenNotFound = false)
 * private String documentIds = "333,444,555";
 * </pre>
 * 
 * 序列化结果：
 * <pre>
 * [
 *   {"fileId": "123", "url": "https://..."},
 *   {"fileId": "456", "url": "https://..."},
 *   {"fileId": "789", "url": "https://..."}
 * ]
 * </pre>
 */
@Slf4j
public class MultiFileUrlSerializer extends JsonSerializer<String> implements ContextualSerializer {

    private final int expireMinutes;
    private final boolean nullWhenNotFound;
    private final String separator;

    public MultiFileUrlSerializer() {
        this(60, true, ",");
    }

    public MultiFileUrlSerializer(int expireMinutes, boolean nullWhenNotFound, String separator) {
        this.expireMinutes = expireMinutes;
        this.nullWhenNotFound = nullWhenNotFound;
        this.separator = separator;
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) {
        if (property != null) {
            MarkMultiFileUrl markMultiFileUrl = property.getAnnotation(MarkMultiFileUrl.class);
            if (markMultiFileUrl != null) {
                return new MultiFileUrlSerializer(
                    markMultiFileUrl.expireMinutes(), 
                    markMultiFileUrl.nullWhenNotFound(),
                    markMultiFileUrl.separator()
                );
            }
        }
        return this;
    }

    @Override
    public void serialize(String fileIds, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 如果文件ID字符串为null或空
        if (StrUtil.isBlank(fileIds)) {
            gen.writeStartArray();
            gen.writeEndArray();
            return;
        }

        // 最大过期时间限制为600分钟(10小时)
        int realExpireMinutes = expireMinutes;
        if(expireMinutes > 600) {
            realExpireMinutes = 600;
            log.warn("文件URL过期时间设置为：{}分钟，超过600分钟限制，已自动调整为600分钟限制", expireMinutes);
        }

        // 分割文件ID字符串
        String[] fileIdArray = fileIds.split(separator);
        Set<Long> fileIdSet = new HashSet<>();
        List<String> fileIdList = new ArrayList<>();
        
        // 解析并验证文件ID
        for (String fileIdStr : fileIdArray) {
            String trimmedId = fileIdStr.trim();
            if (StrUtil.isNotBlank(trimmedId)) {
                try {
                    Long fileId = Long.parseLong(trimmedId);
                    fileIdSet.add(fileId);
                    fileIdList.add(trimmedId);
                } catch (NumberFormatException e) {
                    log.warn("无效的文件ID格式: {}", trimmedId);
                    // 对于无效的ID，如果不忽略null，则添加到列表中用于后续处理
                    if (!nullWhenNotFound) {
                        fileIdList.add(trimmedId);
                    }
                }
            }
        }

        try {
            // 获取OssFileService实例
            OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
            
            // 批量获取文件URL
            Map<Long, String> fileUrlMap = ossFileService.batchGetFileUrls(fileIdSet, realExpireMinutes);

            // 开始写入数组
            gen.writeStartArray();

            // 按原始顺序写入文件信息
            for (String fileIdStr : fileIdList) {
                try {
                    Long fileId = Long.parseLong(fileIdStr);
                    String fileUrl = fileUrlMap.get(fileId);
                    
                    if (StrUtil.isBlank(fileUrl)) {
                        // 文件不存在的情况
                        if (nullWhenNotFound) {
                            gen.writeNull();
                        } else {
                            writeFileUrlObject(gen, fileIdStr, null);
                        }
                    } else {
                        // 正常情况，写入完整的文件信息
                        writeFileUrlObject(gen, fileIdStr, fileUrl);
                    }
                } catch (NumberFormatException e) {
                    // 无效的文件ID
                    if (!nullWhenNotFound) {
                        writeFileUrlObject(gen, fileIdStr, null);
                    }
                }
            }

            gen.writeEndArray();

        } catch (Exception e) {
            log.error("批量获取文件URL失败，文件IDs: {}, 过期时间: {}分钟", fileIds, realExpireMinutes, e);
            
            // 发生异常时的处理
            gen.writeStartArray();
            for (String fileIdStr : fileIdList) {
                if (nullWhenNotFound) {
                    gen.writeNull();
                } else {
                    writeFileUrlObject(gen, fileIdStr, null);
                }
            }
            gen.writeEndArray();
        }
    }

    /**
     * 写入文件URL对象
     */
    private void writeFileUrlObject(JsonGenerator gen, String fileId, String url) throws IOException {
        gen.writeStartObject();
        gen.writeStringField("fileId", fileId);
        gen.writeStringField("url", url);
        gen.writeEndObject();
    }
}
