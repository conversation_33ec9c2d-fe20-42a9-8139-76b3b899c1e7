//package com.tth.framework.config.mybatis;
//
//import com.baomidou.mybatisplus.extension.ddl.IDdl;
//import jakarta.annotation.Resource;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
//import org.springframework.stereotype.Component;
//
//import javax.sql.DataSource;
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.SQLException;
//import java.sql.Statement;
//import java.util.List;
//import java.util.function.Consumer;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//@Component
//@Log4j2
//public class MysqlDdl implements IDdl {
//
//    @Autowired
//    private DataSource dataSource;
//
//    @Resource
//    private DataSourceProperties dataSourceProperties;
//
//    @Override
//    public void runScript(Consumer<DataSource> consumer) {
//        // 1. 先检查数据库
//        databaseExists();
//        // 2. 再执行脚本¬
//        consumer.accept(dataSource);
//    }
//
//    private void databaseExists() {
//        try (Connection connection = dataSource.getConnection()) {
//            log.info("数据库连接成功");
//            // 如果连接成功，则数据库存在
//            return;
//        } catch (SQLException sqlException) {
//            String errorMessage = sqlException.getMessage();
//            log.error("连接数据库异常 {}", errorMessage);
//            log.error("数据库URL {} \n 用户名 {} \n 密码 {} ",
//                    dataSourceProperties.getUrl(),
//                    dataSourceProperties.getUsername(),
//                    dataSourceProperties.getPassword());
//            if (sqlException.getSQLState().equals("42000") && errorMessage.startsWith("Unknown database")) {
//                log.warn("数据库创建中～");
//                // 提取数据库名称
//                String dbName = extractDatabaseName(errorMessage);
//                if (!dbName.isEmpty()) {
//                    // 构建连接字符串并尝试创建数据库
//                    String jdbcUrl = dataSourceProperties.getUrl();
//                    // 获取主机和端口部分
//                    String targetUrl = jdbcUrl.substring(0, jdbcUrl.lastIndexOf('/'));
//                    try (Connection conn = DriverManager.getConnection(targetUrl + "?user=" + dataSourceProperties.getUsername() + "&password=" + dataSourceProperties.getPassword());
//                         Statement stmt = conn.createStatement()) {
//                        String createDatabaseSql = String.format("CREATE DATABASE IF NOT EXISTS `%s` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;", dbName);
//                        stmt.execute(createDatabaseSql);
//                        log.info("Database created successfully: {}", dbName);
//                    } catch (SQLException exception) {
//                        log.error("Error creating database", exception);
//                    }
//                }
//            }
//        }
//    }
//
//    private String extractDatabaseName(String errorMessage) {
//        String regex = "'(.*?)'";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher matcher = pattern.matcher(errorMessage);
//        if (matcher.find()) {
//            return matcher.group(1);
//        }
//        return "";
//    }
//
//    /**
//     * 获取要执行的SQL脚本文件列表
//     */
//    @Override
//    public List<String> getSqlFiles() {
//        return List.of(
//                "db/jm.sql",
//                "db/init.sql"
//        );
//    }
//}