package com.tth.framework.config.mybatisplus;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.tth.framework.interceptor.PermissionInterceptor;
import com.baomidou.mybatisplus.annotation.DbType;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.time.LocalDateTime;

/**
 * MyBatis-Plus配置类
 * 包含拦截器配置和自动填充字段处理器
 */
@Slf4j
@Configuration
public class MyBatisPlusConfig implements MetaObjectHandler {

    /**
     * 配置MyBatis-Plus拦截器
     * 包含乐观锁、分页、防全表更新与删除等功能
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 乐观锁拦截器，支持版本控制
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

        // 防止全表更新与删除插件
//        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

        return interceptor;
    }

    @Override
    public <T, E extends T> MetaObjectHandler strictInsertFill(MetaObject metaObject, String fieldName, Class<T> fieldType, E fieldVal) {
        return MetaObjectHandler.super.strictInsertFill(metaObject, fieldName, fieldType, fieldVal);
    }

    /**
     * 实现MetaObjectHandler接口的insertFill方法
     * 用于在插入时自动填充字段
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "modifiedTime", LocalDateTime.class, LocalDateTime.now());

        long userId = 0L;
        String contextUserId = ThreadContext.get(PermissionInterceptor.USER_ID);
        if(StrUtil.isNotBlank(contextUserId)){
            userId = Long.parseLong(contextUserId);
        }

        this.strictInsertFill(metaObject, "createdBy", Long.class, userId);
        this.strictInsertFill(metaObject, "modifiedBy", Long.class, userId);
        this.strictInsertFill(metaObject, "version", Integer.class, 1);
        this.strictInsertFill(metaObject, "deleted", Boolean.class, false);
        this.handleFloatFields(metaObject);
        this.handleDoubleFields(metaObject);
    }

    /**
     * 实现MetaObjectHandler接口的updateFill方法
     * 用于在更新时自动填充字段
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        long userId = 0L;
        String contextUserId = ThreadContext.get(PermissionInterceptor.USER_ID);
        if(StrUtil.isNotBlank(contextUserId)){
            userId = Long.parseLong(contextUserId);
        }

        this.strictUpdateFill(metaObject, "modifiedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "modifiedBy", Long.class, userId);
        this.handleFloatFields(metaObject);
        this.handleDoubleFields(metaObject);
    }

    /**
     * 处理Float类型字段，将NaN值替换为Null
     */
    private void handleFloatFields(MetaObject metaObject) {
        for (Field field : metaObject.getOriginalObject().getClass().getDeclaredFields()) {
            // 检查字段类型是否为 Float
            if (field.getType() == Float.class || field.getType() == float.class) {
                String fieldName = field.getName();
                Float fieldValue = (Float) metaObject.getValue(fieldName);
                // 如果字段值为 NaN，则替换为 NULL
                if (fieldValue != null && Float.isNaN(fieldValue)) {
                    metaObject.setValue(fieldName, null);
                }
            }
        }
    }

    /**
     * 处理Double类型字段，将NaN值替换为Null
     */
    private void handleDoubleFields(MetaObject metaObject) {
        for (Field field : metaObject.getOriginalObject().getClass().getDeclaredFields()) {
            // 检查字段类型是否为 Double
            if (field.getType() == Double.class || field.getType() == double.class) {
                String fieldName = field.getName();
                Double fieldValue = (Double) metaObject.getValue(fieldName);
                // 如果字段值为 NaN，则替换为 NULL
                if (fieldValue != null && Double.isNaN(fieldValue)) {
                    metaObject.setValue(fieldName, null);
                }
            }
        }
    }
}