package com.tth.framework.config.satoken;

import cn.dev33.satoken.stp.StpInterface;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.tth.modules.user.entity.Permission;
import com.tth.modules.user.entity.Role;
import com.tth.modules.user.entity.RolePermission;
import com.tth.modules.user.entity.UserBase;
import com.tth.modules.user.entity.UserRole;
import com.tth.modules.user.enums.field.UserTypeEnum;
import com.tth.modules.user.service.PermissionService;
import com.tth.modules.user.service.RolePermissionService;
import com.tth.modules.user.service.RoleService;
import com.tth.modules.user.service.UserBaseService;
import com.tth.modules.user.service.UserRoleService;
import com.tth.common.utils.RedisUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 自定义权限验证接口扩展
 * 实现Sa-Token的StpInterface接口，用于获取用户的角色和权限列表
 * 并在权限验证前检查模块名是否为空
 */
@Slf4j
@Component
public class StpInterfaceImpl implements StpInterface {

    // Redis缓存的key前缀
    private static final String ROLE_KEY_PREFIX = "auth:role:";
    private static final String PERMISSION_KEY_PREFIX = "auth:permission:";

    // 缓存过期时间，单位分钟
    private static final long CACHE_TIMEOUT = 30;

    public StpInterfaceImpl() {
        log.info("StpInterfaceImpl初始化成功，权限验证接口已注册");
    }

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private RoleService roleService;

    @Resource
    private RolePermissionService rolePermissionService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private UserBaseService userBaseService;

    /**
     * 获取指定账号的所有权限标识
     *
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 该账号的所有权限标识
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        String cacheKey = PERMISSION_KEY_PREFIX + loginId;

        // 先从 Redis 缓存中获取
        List<String> permissionList = RedisUtil.getList(cacheKey, String.class);
        if (ObjUtil.isNotNull(permissionList) && !permissionList.isEmpty()) {
            log.debug("从缓存获取用户[{}]的权限列表: {}", loginId, permissionList);
            return permissionList;
        }

        log.debug("从数据库获取用户[{}]的权限列表", loginId);

        // 获取用户的角色ID列表
        List<UserRole> userRoles = userRoleService.lambdaQuery()
                .eq(UserRole::getUserId, loginId)
                .list();

        if (CollUtil.isEmpty(userRoles)) {
            log.debug("用户[{}]没有角色，因此没有权限", loginId);
            // 缓存空结果，避免缓存穿透
            RedisUtil.set(cacheKey, new ArrayList<>(), CACHE_TIMEOUT, TimeUnit.MINUTES);
            return new ArrayList<>();
        }

        // 获取角色ID列表
        List<Long> roleIds = userRoles.stream()
                .map(UserRole::getRoleId)
                .collect(Collectors.toList());

        // 获取角色-权限关联信息
        List<RolePermission> rolePermissions = rolePermissionService.lambdaQuery()
                .in(RolePermission::getRoleId, roleIds)
                .list();

        if (CollUtil.isEmpty(rolePermissions)) {
            log.debug("用户[{}]的角色没有关联权限", loginId);
            // 缓存空结果，避免缓存穿透
            RedisUtil.set(cacheKey, new ArrayList<>(), CACHE_TIMEOUT, TimeUnit.MINUTES);
            return new ArrayList<>();
        }

        // 获取权限ID列表
        List<Long> permissionIds = rolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .collect(Collectors.toList());

        // 获取权限信息
        List<Permission> permissions = permissionService.lambdaQuery()
                .in(Permission::getId, permissionIds)
                .list();

        // 返回权限编码列表
        List<String> permissionCodes = permissions.stream()
                .map(Permission::getPermissionCode)
                .collect(Collectors.toList());

        // 将结果存入Redis缓存
        RedisUtil.set(cacheKey, permissionCodes, CACHE_TIMEOUT, TimeUnit.MINUTES);

        log.debug("用户[{}]的权限列表: {}", loginId, permissionCodes);
        return permissionCodes;
    }

    /**
     * 获取指定账号的所有角色标识
     *
     * @param loginId   账号id
     * @param loginType 账号类型
     * @return 该账号的所有角色标识
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        String cacheKey = ROLE_KEY_PREFIX + loginId;

        // 先从 Redis 缓存中获取
        List<String> roleList = RedisUtil.getList(cacheKey, String.class);
        if (ObjUtil.isNotNull(roleList) && !roleList.isEmpty()) {
            log.debug("从缓存获取用户[{}]的角色列表: {}", loginId, roleList);
            return roleList;
        }

        log.debug("从数据库获取用户[{}]的角色列表", loginId);

        // 获取用户的角色ID列表
        List<UserRole> userRoles = userRoleService.lambdaQuery()
                .eq(UserRole::getUserId, loginId)
                .list();

        List<String> roleCodes = new ArrayList<>();

        // 如果用户有分配的角色，则获取这些角色
        if (CollUtil.isNotEmpty(userRoles)) {
            // 获取角色ID列表
            List<Long> roleIds = userRoles.stream()
                    .map(UserRole::getRoleId)
                    .collect(Collectors.toList());

            // 获取角色信息
            List<Role> roles = roleService.lambdaQuery()
                    .in(Role::getId, roleIds)
                    .list();

            // 获取角色编码列表
            roleCodes.addAll(roles.stream()
                    .map(Role::getRoleCode)
                    .toList());
        }

        // 获取用户基础信息，获取用户类型
        UserBase userBase = null;
        try {
            userBase = userBaseService.getById(Long.valueOf(loginId.toString()));
        } catch (Exception e) {
            log.error("获取用户[{}]基础信息失败: {}", loginId, e.getMessage());
        }

        // 根据用户类型添加默认角色
        if (userBase != null && userBase.getUserType() != null) {
            UserTypeEnum userType = userBase.getUserType();
            String defaultRole = userType.name();
            roleCodes.add(defaultRole);
            log.debug("为用户[{}]添加基于用户类型[{}]的默认角色: {}", loginId, userType, defaultRole);
        } else {
            log.warn("无法获取用户[{}]的用户类型信息，跳过添加默认角色", loginId);
        }

        // 将结果存入Redis缓存
        RedisUtil.set(cacheKey, roleCodes, CACHE_TIMEOUT, TimeUnit.MINUTES);

        log.debug("用户[{}]的角色列表: {}", loginId, roleCodes);
        return roleCodes;
    }

    /**
     * 清除指定用户的权限缓存
     * 在用户角色或权限变更时调用此方法
     *
     * @param userId 用户ID
     */
    public void clearUserCache(Object userId) {
        String roleKey = ROLE_KEY_PREFIX + userId;
        String permissionKey = PERMISSION_KEY_PREFIX + userId;

        RedisUtil.delete(roleKey);
        RedisUtil.delete(permissionKey);

        log.debug("已清除用户[{}]的角色和权限缓存", userId);
    }
}