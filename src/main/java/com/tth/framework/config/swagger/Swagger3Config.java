package com.tth.framework.config.swagger;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Swagger3Config {

    @Bean
    public OpenAPI springOpenAPI() {
        // 访问路径：http://localhost:8080/swagger-ui.html
        return new OpenAPI().info(new Info()
                .title("听童话API")
                .description("听童话API接口文档")
                .version("0.0.1"));
    }
}
