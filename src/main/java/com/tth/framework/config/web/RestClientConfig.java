package com.tth.framework.config.web;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.tth.framework.filter.ClientHttpResponseWrapper;
import com.tth.framework.interceptor.PermissionInterceptor;
import com.tth.framework.utils.IpUtil;
import com.tth.modules.system.entity.ApiRequestLog;
import com.tth.modules.system.enums.LogTypeEnum;
import com.tth.modules.system.service.ApiRequestLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestClient;

import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import org.apache.commons.lang3.exception.ExceptionUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * RestClient配置类
 * 用于创建RestClient Bean，支持HTTP请求
 * Spring Boot 3.2.x新增的RestClient替代RestTemplate
 */
@Slf4j
@Configuration
public class RestClientConfig {

    @Resource(name = "commonExecutor")
    private Executor executor;

    @Resource
    private ApiRequestLogService apiRequestLogService;

    /**
     * 创建RestClient Bean
     * RestClient是Spring 6.1和Spring Boot 3.2中引入的新HTTP客户端
     * 相比RestTemplate，它提供了更现代的API和更好的性能
     *
     * @return RestClient实例
     */
    @Bean
    public RestClient restClient() {
        // 创建请求工厂并设置超时时间
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 连接超时时间，单位毫秒
        factory.setConnectTimeout(5000);
        // 读取超时时间，单位毫秒
        factory.setReadTimeout(5000);

        return RestClient.builder()
                .requestFactory(factory)
                .defaultStatusHandler(status -> status.is4xxClientError() || status.is5xxServerError(),
                        (request, response) -> {
                            // 简单地抛出第三方异常，使用通用错误码
                            throw new ThirdPartyException(ThirdPartyExceptionCode.RESPONSE_ERROR,
                                    "第三方服务响应错误: " + response.getStatusCode().value());
                        })
                .requestInterceptor((request, body, execution) -> {
                    // 生成请求ID
                    String requestId = IdUtil.fastSimpleUUID();
                    String requestUrl = request.getURI().toString();
                    String requestMethod = request.getMethod().name();

                    // 记录请求开始
                    long startTime = System.currentTimeMillis();
                    log.debug("发送RestClient请求 - 请求ID: {}, 方法: {}, URL: {}", requestId, requestMethod, requestUrl);

                    // 获取请求体
                    String requestBody = null;
                    if (body.length > 0) { // body可能是空数组，但不会是null
                        requestBody = new String(body, StandardCharsets.UTF_8);
                    }

                    // 执行请求
                    ClientHttpResponse response;
                    Exception exception = null;
                    String responseBody = null;
                    int httpStatus = 0;

                    try {
                        // 执行请求
                        response = execution.execute(request, body);
                        httpStatus = response.getStatusCode().value();

                        // 包装响应对象，使其可以被多次读取
                        ClientHttpResponseWrapper wrappedResponse = new ClientHttpResponseWrapper(response);

                        // 读取响应体并缓存，但不影响业务代码的读取
                        try {
                            responseBody = wrappedResponse.getBodyAsString();
                            log.debug("成功读取响应体，长度: {}", responseBody.length());
                        } catch (IOException e) {
                            log.warn("无法读取响应体: {}", e.getMessage());
                        }

                        return wrappedResponse;
                    } catch (Exception e) {
                        exception = e;
                        log.error("请求异常 - 请求ID: {}, 方法: {}, URL: {}, 异常: {}", requestId, requestMethod, requestUrl, e.getMessage());

                        // 特别处理超时异常
                        if (e instanceof SocketTimeoutException) {
                            throw new ThirdPartyException(ThirdPartyExceptionCode.TIMEOUT, e);
                        } else if (e instanceof ConnectException) {
                            throw new ThirdPartyException(ThirdPartyExceptionCode.CONNECTION_FAILED, e);
                        } else if (e instanceof IOException) {
                            throw new ThirdPartyException(ThirdPartyExceptionCode.IO_ERROR, e);
                        }

                        // 处理其他异常
                        throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, e);
                    } finally {
                        // 计算请求耗时
                        long duration = System.currentTimeMillis() - startTime;
                        log.debug("请求完成 - 请求ID: {}, 方法: {}, URL: {}, 状态码: {}, 耗时: {}ms",
                                requestId, requestMethod, requestUrl, httpStatus, duration);

                        // 异步保存日志
                        saveApiRequestLogAsync(
                                requestId,
                                requestUrl,
                                requestMethod,
                                request.getHeaders(),
                                requestBody,
                                responseBody,
                                httpStatus,
                                exception,
                                duration
                        );
                    }
                })
                .defaultHeaders(headers -> {
                    // 设置默认Content-Type为application/json
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    // 设置默认Accept为application/json
                    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                })
                .build();
    }

    /**
     * 异步保存API请求日志
     *
     * @param requestId 请求ID
     * @param url 请求URL
     * @param method 请求方法
     * @param headers 请求头
     * @param requestBody 请求体
     * @param responseBody 响应体
     * @param httpStatus 响应状态码
     * @param exception 异常信息（如果有）
     * @param duration 请求耗时
     */
    private void saveApiRequestLogAsync(String requestId, String url, String method, HttpHeaders headers,
                                     String requestBody, String responseBody, int httpStatus,
                                     Exception exception, long duration) {
        try {
            // 创建日志对象
            ApiRequestLog apiRequestLog = new ApiRequestLog();
            apiRequestLog.setLogType(LogTypeEnum.THIRD_PARTY_API); // 第三方API日志
            apiRequestLog.setRequestId(requestId);
            apiRequestLog.setUrl(url);
            apiRequestLog.setMethod(method);
            apiRequestLog.setModule("RestClient");
            apiRequestLog.setOperation("外部HTTP请求");

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                Map<String, String> headerMap = new HashMap<>();
                headers.forEach((name, values) -> {
                    headerMap.put(name, String.join(", ", values));
                });
                apiRequestLog.setHeaderParams(JSONUtil.toJsonStr(headerMap));
            }

            // 设置请求体
            if (requestBody != null) {
                apiRequestLog.setRequestBody(requestBody);
            }

            // 不设置并且也无法知道响应状态码
//            apiRequestLog.setResponseStatus(statusCode);
//            apiRequestLog.setBizStatus(statusCode);

            // 设置响应体
            if (responseBody != null) {
                // 如果响应体过长，只保留前2000个字符
                String trimmedResponseBody = responseBody;
                if (trimmedResponseBody.length() > 2000) {
                    trimmedResponseBody = trimmedResponseBody.substring(0, 2000) + "...";
                }
                apiRequestLog.setResponseBody(trimmedResponseBody);
            }

            // 设置状态和错误信息
            apiRequestLog.setHttpStatus(exception != null && httpStatus == 0 ? HttpStatus.INTERNAL_SERVER_ERROR.value() : httpStatus);
//            apiRequestLog.setStatus(exception != null ? SuccessStatusEnum.FAILURE : SuccessStatusEnum.SUCCESS);
            if (exception != null) {
                // 获取完整的异常堆栈信息
                String stackTrace = ExceptionUtils.getStackTrace(exception);
                apiRequestLog.setErrorMsg(stackTrace);
            }

            // 设置耗时
            apiRequestLog.setDuration(duration);

            // 设置IP地址
            String ip = IpUtil.getCurrentIp();
            apiRequestLog.setIp(ip);

            // 设置用户ID
            String userId = ThreadContext.get(PermissionInterceptor.USER_ID);
            if (userId != null) {
                try {
                    apiRequestLog.setUserId(Long.parseLong(userId));
                } catch (NumberFormatException e) {
                    log.warn("无法解析用户ID: {}", userId);
                }
            }

            // 异步保存到数据库
            CompletableFuture.runAsync(() -> {
                try {
                    // 在新线程中恢复ThreadContext数据
                    if (userId != null) {
                        ThreadContext.put(PermissionInterceptor.USER_ID, userId);
                    }

                    try {
                        // 在异步线程中获取IP位置信息
                        if (apiRequestLog.getIp() != null) {
                            apiRequestLog.setIpPosition(IpUtil.getRealAddressByIP(apiRequestLog.getIp()));
                        }

                        apiRequestLogService.save(apiRequestLog);
                        log.debug("RestClient请求日志保存成功，请求ID: {}", requestId);
                    } catch (Exception e) {
                        log.error("保存RestClient请求日志失败: {}", e.getMessage(), e);
                    } finally {
                        // 清理新线程中的ThreadContext数据
                        ThreadContext.remove(PermissionInterceptor.USER_ID);
                    }
                } catch (Exception e) {
                    log.error("在异步线程中处理ThreadContext失败: {}", e.getMessage(), e);
                }
            }, executor);
        } catch (Exception e) {
            log.error("准备保存RestClient请求日志失败: {}", e.getMessage(), e);
        }
    }
}
