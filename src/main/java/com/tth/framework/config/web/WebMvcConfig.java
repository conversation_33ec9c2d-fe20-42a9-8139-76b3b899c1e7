package com.tth.framework.config.web;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.tth.framework.interceptor.PermissionInterceptor;
import com.tth.framework.interceptor.RepeatSubmitInterceptor;
import com.tth.framework.interceptor.RequestIdInterceptor;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 * 用于配置拦截器、跨域等Web相关配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 不需要登录拦截的路径
     */
    private final String[] excludePathPatterns = {
            // 登录相关
            "/auth/login",
            "/auth/register",
            "/auth/token/refresh",
            // Swagger/Knife4j相关 - 使用更全面的排除路径
            "/doc.html",
            "/doc.html/**",
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/webjars/**",
            "/v3/api-docs/**",
            "/v2/api-docs/**",
            "/v2/api-docs-ext/**",
            "/api-docs/**",
            "/api-docs-ext/**",
            "/configuration/ui",
            "/configuration/security",
            "/favicon.ico",
            // 错误页面
            "/error"
    };

    @Resource
    private RepeatSubmitInterceptor repeatSubmitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册请求ID拦截器，对所有请求生成请求ID，不排除任何路径
        registry.addInterceptor(new RequestIdInterceptor())
                .addPathPatterns("/**");

        // 注册自定义权限拦截器
        registry.addInterceptor(new PermissionInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(excludePathPatterns);

        // 注册Sa-Token拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
                .addPathPatterns("/**")
                .excludePathPatterns(excludePathPatterns);

        // 注册防重复提交拦截器
        registry.addInterceptor(repeatSubmitInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(excludePathPatterns);
    }
}
