package com.tth.framework.constant;

/**
 * 缓存常量
 *
 * <AUTHOR>
 */
public class CacheConstants {

    /**
     * 限流前缀
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 防重复提交前缀
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 系统配置缓存前缀
     */
    public static final String SYSTEM_CONFIG_KEY = "system_config:";

    /**
     * 系统配置单个值缓存前缀
     */
    public static final String SYSTEM_CONFIG_VALUE_KEY = "system_config:value:";

    /**
     * 系统配置提供商所有配置缓存前缀
     */
    public static final String SYSTEM_CONFIG_PROVIDER_KEY = "system_config:provider:";

    /**
     * 系统配置缓存过期时间（秒）- 30分钟
     */
    public static final long SYSTEM_CONFIG_CACHE_EXPIRE = 30 * 60;
}
