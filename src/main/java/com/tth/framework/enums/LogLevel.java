package com.tth.framework.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志级别枚举
 * 用于定义API日志的记录级别
 */
@Schema(description = "日志级别")
@Getter
@AllArgsConstructor
public enum LogLevel implements IEnum<Integer> {
    
    @Schema(description = "基础级别 - 只记录基本信息")
    BASIC(1, "基础级别"),
    
    @Schema(description = "标准级别 - 记录请求参数和简要响应")
    STANDARD(2, "标准级别"),
    
    @Schema(description = "详细级别 - 记录完整请求和响应信息")
    DETAILED(3, "详细级别");
    
    /**
     * 枚举值，用于数据库存储
     */
    private final int value;
    
    /**
     * 枚举描述，用于前端显示
     */
    @JsonValue
    private final String desc;
    
    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }
}
