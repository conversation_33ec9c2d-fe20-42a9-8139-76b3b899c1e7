package com.tth.framework.enums;

import lombok.Getter;

@Getter
public enum OperateEnum {
    EQ("="),
    NE("!="),
    GT(">"),
    GE(">="),
    LT("<"),
    LE("<="),
    LIKE("LIKE"),
    NOT_LIKE("NOT LIKE"),
    IN("IN"),
    NOT_IN("NOT IN"),
    ;

    private final String operate;

    OperateEnum(String operate) {
        this.operate = operate;
    }


    public static OperateEnum GetOperateEnum(String operate) {
        for (OperateEnum item : values()) {
            if (item.operate.equalsIgnoreCase(operate)) {
                return item;
            }
        }
        return null;
    }
}
