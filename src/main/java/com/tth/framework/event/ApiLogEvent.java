package com.tth.framework.event;

import com.tth.framework.annotation.ApiLog;
import com.tth.common.enums.SuccessStatusEnum;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * API日志事件
 * 统一处理正常请求和异常请求的日志记录
 * 使用单一构造函数处理所有情况
 */
@Getter
public class ApiLogEvent extends ApplicationEvent {

    /**
     * 请求对象
     */
    private final HttpServletRequest request;

    /**
     * 响应对象
     */
    private final HttpServletResponse response;

    /**
     * 响应内容
     */
    private final Object result;

    /**
     * 异常对象（如果有）
     */
    private final Exception exception;

    /**
     * 响应状态码
     */
    private final int responseStatus;

    /**
     * 业务成功状态
     */
    private final SuccessStatusEnum bizStatus;

    /**
     * 请求处理耗时（毫秒）
     */
    private final long duration;

    /**
     * 模块名称
     */
    private final String module;

    /**
     * 操作名称
     */
    private final String operation;

    /**
     * 日志级别
     */
    private final ApiLog apiLog;

    /**
     * 统一构造函数 - 处理所有请求类型
     *
     * @param source 事件源
     * @param request 请求对象
     * @param response 响应对象
     * @param result 响应内容
     * @param exception 异常对象（如果有）
     * @param responseStatus 响应状态码
     * @param duration 请求处理耗时（毫秒）
     * @param apiLog 日志注解（可能为null）
     */
    public ApiLogEvent(Object source, HttpServletRequest request, HttpServletResponse response,
                      Object result, Exception exception, int responseStatus, long duration, ApiLog apiLog) {
        super(source);
        this.request = request;
        this.response = response;
        this.result = result;
        this.exception = exception;
        this.responseStatus = responseStatus;
        this.bizStatus = exception != null ? SuccessStatusEnum.FAILURE : SuccessStatusEnum.SUCCESS;
        this.duration = duration;
        this.apiLog = apiLog;

        if(apiLog != null){
            this.module = apiLog.module();
            this.operation = apiLog.value();
        } else if(exception != null) {
            this.module = "异常处理";
            this.operation = exception.getClass().getName();
        } else {
            this.module = "";
            this.operation = "";
        }
    }

    /**
     * 判断是否为异常事件
     */
    public boolean isExceptionEvent() {
        return exception != null;
    }
}
