package com.tth.framework.exception.code;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 认证异常错误码
 * 用于定义认证相关的错误码
 */
@Getter
public enum AuthExceptionCode implements IResponseCode {

    // 认证错误：401xxx
    UNAUTHORIZED(401000, "未登录，请登录"),
    LOGIN_REQUIRED(401001, "需要登录"),
    LOGIN_FAILED(401002, "登录失败"),
    LOGOUT_FAILED(401003, "登出失败"),
    UNSUPPORTED_LOGIN_TYPE(401004, "不支持的登录类型"),
    ACCOUNT_NOT_FOUND(401005, "账号不存在"),

    // Token错误：401xxx
    TOKEN_MISSING(401100, "缺少令牌"),
    TOKEN_INVALID(401101, "无效的Token"),
    TOKEN_EXPIRED(401102, "令牌已过期"),
    TOKEN_REVOKED(401103, "令牌已撤销"),
    TOKEN_TYPE_ERROR(401104, "令牌类型错误"),
    TOKEN_SIGNATURE_ERROR(401105, "令牌签名错误"),
    TOKEN_ISSUER_ERROR(401106, "令牌颁发者错误"),
    TOKEN_AUDIENCE_ERROR(401107, "令牌受众错误"),
    TOKEN_SUBJECT_ERROR(401108, "令牌主题错误"),
    TOKEN_CLAIMS_ERROR(401109, "令牌声明错误"),

    // 账号错误：401xxx
    ACCOUNT_DISABLED(401200, "账号已禁用"),
    ACCOUNT_LOCKED(401201, "账号已锁定"),
    ACCOUNT_EXPIRED(401202, "账号已过期"),
    ACCOUNT_NOT_ACTIVATED(401203, "账号未激活"),
    ACCOUNT_STATUS_INVALID(401204, "账号状态异常"),
    ACCOUNT_ADMIN_DISABLED(401205, "账号已被管理员禁用"),

    // 密码错误：401xxx
    PASSWORD_ERROR(401300, "密码错误"),
    PASSWORD_EXPIRED(401301, "密码已过期"),
    PASSWORD_RESET_REQUIRED(401302, "需要重置密码"),
    PASSWORD_HISTORY_ERROR(401303, "密码历史错误"),

    // 多因素认证错误：401xxx
    MFA_REQUIRED(401400, "需要多因素认证"),
    MFA_FAILED(401401, "多因素认证失败"),
    MFA_CODE_INVALID(401402, "多因素认证码无效"),
    MFA_CODE_EXPIRED(401403, "多因素认证码已过期"),

    // 社交登录错误：401xxx
    SOCIAL_LOGIN_FAILED(401500, "社交登录失败"),
    SOCIAL_ACCOUNT_NOT_LINKED(401501, "社交账号未关联"),
    SOCIAL_ACCOUNT_ALREADY_LINKED(401502, "社交账号已关联"),

    // 会话错误：401xxx
    SESSION_INVALID(401600, "会话无效"),
    SESSION_EXPIRED(401601, "会话已过期"),
    SESSION_ALREADY_INVALIDATED(401602, "会话已失效"),

    // 认证服务错误：401xxx
    AUTH_SERVICE_ERROR(401700, "认证服务错误"),
    AUTH_PROVIDER_ERROR(401701, "认证提供者错误"),
    AUTH_CONFIG_ERROR(401702, "认证配置错误");

    private final int code;
    private final String message;

    AuthExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getFullCode() {
        return this.code;
    }
}
