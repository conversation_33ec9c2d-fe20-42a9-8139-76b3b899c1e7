package com.tth.framework.exception.code;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 权限异常错误码
 * 用于定义权限相关的错误码
 */
@Getter
public enum PermissionExceptionCode implements IResponseCode {

    // 权限错误：403xxx
    FORBIDDEN(403000, "您的权限不足"),
    ACCESS_DENIED(403001, "拒绝访问"),
    OPERATION_NOT_ALLOWED(403002, "不允许的操作"),
    MODULE_NAME_NOT_SPECIFIED(403003, "未指定模块名称，无法进行权限检查"),

    // 角色错误：403xxx
    ROLE_REQUIRED(403100, "需要角色"),
    ROLE_NOT_FOUND(403101, "角色不存在"),
    ROLE_ALREADY_EXISTS(403102, "角色已存在"),
    ROLE_ASSIGNMENT_ERROR(403103, "角色分配错误"),

    // 权限错误：403xxx
    PERMISSION_REQUIRED(403200, "需要权限"),
    PERMISSION_NOT_FOUND(403201, "权限不存在"),
    PERMISSION_ALREADY_EXISTS(403202, "权限已存在"),
    PERMISSION_ASSIGNMENT_ERROR(403203, "权限分配错误"),

    // 资源访问错误：403xxx
    RESOURCE_ACCESS_DENIED(403300, "资源访问被拒绝"),
    RESOURCE_OWNERSHIP_ERROR(403301, "资源所有权错误"),
    RESOURCE_SHARING_ERROR(403302, "资源共享错误"),

    // 数据访问错误：403xxx
    DATA_ACCESS_DENIED(403400, "数据访问被拒绝"),
    DATA_OWNERSHIP_ERROR(403401, "数据所有权错误"),
    DATA_SHARING_ERROR(403402, "数据共享错误"),

    // 功能访问错误：403xxx
    FEATURE_ACCESS_DENIED(403500, "功能访问被拒绝"),
    FEATURE_DISABLED(403501, "功能已禁用"),
    FEATURE_RESTRICTED(403502, "功能受限"),

    // API访问错误：403xxx
    API_ACCESS_DENIED(403600, "API访问被拒绝"),
    API_RATE_LIMIT_EXCEEDED(403601, "API速率限制已超出"),
    API_QUOTA_EXCEEDED(403602, "API配额已超出"),
    REPEAT_SUBMIT(403603, "重复提交"),

    // 租户错误：403xxx
    TENANT_ACCESS_DENIED(403700, "租户访问被拒绝"),
    TENANT_DISABLED(403701, "租户已禁用"),
    TENANT_EXPIRED(403702, "租户已过期"),

    // 组织错误：403xxx
    ORGANIZATION_ACCESS_DENIED(403800, "组织访问被拒绝"),
    ORGANIZATION_DISABLED(403801, "组织已禁用"),
    ORGANIZATION_EXPIRED(403802, "组织已过期"),

    // 权限服务错误：403xxx
    PERMISSION_SERVICE_ERROR(403900, "权限服务错误"),
    PERMISSION_CONFIG_ERROR(403901, "权限配置错误");

    private final int code;
    private final String message;

    PermissionExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getFullCode() {
        return this.code;
    }
}
