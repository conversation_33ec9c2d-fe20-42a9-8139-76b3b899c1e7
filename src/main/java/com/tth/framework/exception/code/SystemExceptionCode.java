package com.tth.framework.exception.code;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 系统异常错误码
 * 用于定义系统通用的错误码
 */
@Getter
public enum SystemExceptionCode implements IResponseCode {

    // 服务端错误：5xxxx
    INTERNAL_SERVER_ERROR(500000, "啊哦，发生了系统内部错误"),
    NOT_FOUND(500404, "未找到资源"),
    METHOD_NOT_ALLOWED(500405, "方法不允许"),
    FILE_UPLOAD_SIZE_EXCEEDED(500406, "文件上传大小超限"),
    NOT_MULTIPART(500407, "不是 MULTIPART"),
    CONTENT_TYPE_NOT_ALLOWED(500415, "方法不允许"),
    PARAM_ERROR(500011, "请求失败，请联系客服"),
    DATABASE_ERROR(500001, "数据库错误"),
    CACHE_ERROR(500002, "缓存服务错误"),
    IO_ERROR(500003, "IO操作错误"),
    CONCURRENT_ERROR(500004, "并发操作错误"),

    // 服务不可用
    SERVICE_UNAVAILABLE(503000, "服务不可用"),
    SERVICE_TIMEOUT(503001, "服务超时"),
    SERVICE_BUSY(503002, "服务繁忙"),

    // 第三方服务错误：6xxxx
    PAYMENT_SERVICE_ERROR(601000, "支付服务异常"),
    SMS_SERVICE_ERROR(602000, "短信服务异常"),
    FILE_SERVICE_ERROR(603000, "文件服务异常"),

    // 其他错误码（从 RCodeEnum 迁移）
    SYS_ERR(4001, "系统异常"),
    THREE_ERR(4100, "三方异常"),
    BUS_LOGIC_ERR(4200, "业务异常"),
    TOKEN_ERR(4300, "Token异常"),
    REQ_ERR(4400, "请求错误"),
    AUTH_ERR(4500, "权限异常");

    private final int code;
    private final String message;

    SystemExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getFullCode() {
        return this.code;
    }
}
