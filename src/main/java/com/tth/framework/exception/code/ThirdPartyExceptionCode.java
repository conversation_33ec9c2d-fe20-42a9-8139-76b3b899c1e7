package com.tth.framework.exception.code;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 第三方异常错误码
 * 用于定义与第三方服务交互相关的错误码
 */
@Getter
public enum ThirdPartyExceptionCode implements IResponseCode {

    // 通用第三方服务错误：6xxxx
    THIRD_PARTY_ERROR(600000, "第三方服务异常"),
    CONNECTION_FAILED(600001, "第三方服务连接失败"),
    TIMEOUT(600002, "第三方服务请求超时"),
    IO_ERROR(600003, "第三方服务IO异常"),
    RESPONSE_ERROR(600004, "第三方服务响应错误"),
    SERVICE_UNAVAILABLE(600005, "服务不可用"),

    // 阿里云OSS服务错误：61xxxx
    ALI_YUN_OSS_SERVICE_ERROR(601001, "计算HMAC-SHA256异常"),

    // 支付服务错误：61xxxx
    PAYMENT_SERVICE_ERROR(610000, "支付服务异常"),
    PAYMENT_FAILED(610001, "支付失败"),
    REFUND_FAILED(610002, "退款失败"),
    PAYMENT_QUERY_FAILED(610003, "支付查询失败"),
    PAYMENT_CANCEL_FAILED(610004, "支付取消失败"),
    PAYMENT_CLOSE_FAILED(610005, "支付关闭失败"),

    // 短信服务错误：62xxxx
    SMS_SERVICE_ERROR(620000, "短信服务异常"),
    SMS_SEND_FAILED(620001, "短信发送失败"),
    SMS_TEMPLATE_ERROR(620002, "短信模板错误"),
    SMS_PARAM_ERROR(620003, "短信参数错误"),
    SMS_RATE_LIMIT(620004, "短信发送频率限制"),

    // 邮件服务错误：63xxxx
    EMAIL_SERVICE_ERROR(630000, "邮件服务异常"),
    EMAIL_SEND_FAILED(630001, "邮件发送失败"),
    EMAIL_TEMPLATE_ERROR(630002, "邮件模板错误"),
    EMAIL_PARAM_ERROR(630003, "邮件参数错误"),

    // 存储服务错误：64xxxx
    STORAGE_SERVICE_ERROR(640000, "存储服务异常"),
    FILE_UPLOAD_FAILED(640001, "文件上传失败"),
    FILE_DOWNLOAD_FAILED(640002, "文件下载失败"),
    FILE_DELETE_FAILED(640003, "文件删除失败"),
    BUCKET_NOT_FOUND(640004, "存储桶不存在"),

    // 地图服务错误：65xxxx
    MAP_SERVICE_ERROR(650000, "地图服务异常"),
    GEOCODING_FAILED(650001, "地理编码失败"),
    REVERSE_GEOCODING_FAILED(650002, "逆地理编码失败"),
    ROUTE_PLANNING_FAILED(650003, "路线规划失败"),
    LOCATION_FAILED(650004, "定位失败"),

    // 社交媒体服务错误：66xxxx
    SOCIAL_SERVICE_ERROR(660000, "社交媒体服务异常"),
    SOCIAL_AUTH_FAILED(660001, "社交认证失败"),
    SOCIAL_SHARE_FAILED(660002, "社交分享失败"),
    SOCIAL_COMMENT_FAILED(660003, "社交评论失败"),

    // 推送服务错误：67xxxx
    PUSH_SERVICE_ERROR(670000, "推送服务异常"),
    PUSH_SEND_FAILED(670001, "推送发送失败"),
    PUSH_TEMPLATE_ERROR(670002, "推送模板错误"),
    PUSH_PARAM_ERROR(670003, "推送参数错误"),

    // 人脸识别服务错误：68xxxx
    FACE_SERVICE_ERROR(680000, "人脸识别服务异常"),
    FACE_DETECT_FAILED(680001, "人脸检测失败"),
    FACE_COMPARE_FAILED(680002, "人脸比对失败"),
    FACE_VERIFY_FAILED(680003, "人脸验证失败"),

    // OCR服务错误：69xxxx
    OCR_SERVICE_ERROR(690000, "OCR服务异常"),
    OCR_RECOGNIZE_FAILED(690001, "OCR识别失败"),
    OCR_PARAM_ERROR(690002, "OCR参数错误");

    private final int code;
    private final String message;

    ThirdPartyExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getFullCode() {
        return this.code;
    }
}
