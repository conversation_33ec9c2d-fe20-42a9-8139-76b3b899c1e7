package com.tth.framework.exception.code;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 参数验证异常错误码
 * 用于定义参数验证相关的错误码
 */
@Getter
public enum ValidationExceptionCode implements IResponseCode {

    // 参数验证错误：4xxxx
    PARAM_VALIDATION_ERROR(400000, "参数不能通过验证"),
    ID_REQUIRED(400001, "id不能为空"),
    PARAMS_REQUIRED(400002, "参数不能为空"),
    PARAM_NULL(400003, "参数不能为null"),
    NOT_FOUND(400404, "缺少参数"),
    TYPE_MISMATCH(400405, "类型不匹配"),
    NOT_JSON_FORMAT(400406, "不是json格式"),
    PARAM_FORMAT_ERROR(400407, "参数格式错误"),

    PARAM_LENGTH_ERROR(400004, "参数长度错误"),
    PARAM_RANGE_ERROR(400006, "参数范围错误"),
    PARAM_TYPE_ERROR(400007, "参数类型错误"),
    PARAM_MISSING(400008, "缺少必要参数"),

    // 请求验证错误：402xxx
    REQUEST_VALIDATION_ERROR(402000, "请求验证错误"),
    REQUEST_METHOD_ERROR(402001, "请求方法错误"),
    REQUEST_CONTENT_TYPE_ERROR(402002, "请求内容类型错误"),
    REQUEST_BODY_ERROR(402003, "请求体错误"),

    // 日期时间验证错误：404xxx
    DATE_VALIDATION_ERROR(404000, "日期验证错误"),
    DATE_FORMAT_ERROR(404001, "日期格式错误"),
    DATE_RANGE_ERROR(404002, "日期范围错误"),

    // 数字验证错误：405xxx
    NUMBER_VALIDATION_ERROR(405000, "数字验证错误"),
    NUMBER_FORMAT_ERROR(405001, "数字格式错误"),
    NUMBER_RANGE_ERROR(405002, "数字范围错误"),

    // 字符串验证错误：406xxx
    STRING_VALIDATION_ERROR(406000, "字符串验证错误"),
    STRING_FORMAT_ERROR(406001, "字符串格式错误"),
    STRING_LENGTH_ERROR(406002, "字符串长度错误"),

    // 枚举验证错误：407xxx
    ENUM_VALIDATION_ERROR(407000, "枚举验证错误"),
    ENUM_VALUE_ERROR(407001, "枚举值错误");

    private final int code;
    private final String message;

    ValidationExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getFullCode() {
        return this.code;
    }
}
