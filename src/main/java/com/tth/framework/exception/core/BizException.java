package com.tth.framework.exception.core;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 业务异常
 * 用于表示业务逻辑错误，如用户不存在、余额不足等
 */
@Getter
public class BizException extends RuntimeException {

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 错误码对象
     */
    private final IResponseCode errorCode;

    /**
     * 使用错误码构造业务异常
     *
     * @param errorCode 错误码
     */
    public BizException(IResponseCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = errorCode.getMessage();
    }

    /**
     * 使用错误码和原因构造业务异常
     *
     * @param errorCode 错误码
     * @param cause 原因
     */
    public BizException(IResponseCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = errorCode.getMessage();
    }

    /**
     * 使用错误码和自定义消息构造业务异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     */
    public BizException(IResponseCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
    }

    /**
     * 使用错误码、自定义消息和原因构造业务异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     * @param cause 原因
     */
    public BizException(IResponseCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
    }

    /**
     * 重写fillInStackTrace方法，提高性能
     * 业务异常不需要收集堆栈信息
     */
    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

}