package com.tth.framework.exception.core;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 系统异常
 * 用于表示系统内部错误，如数据库连接失败、缓存服务不可用等
 */
@Getter
public class SystemException extends RuntimeException {

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 错误码对象
     */
    private final IResponseCode errorCode;

    /**
     * 使用错误码构造系统异常
     *
     * @param errorCode 错误码
     */
    public SystemException(IResponseCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = errorCode.getMessage();
    }

    /**
     * 使用错误码和原因构造系统异常
     *
     * @param errorCode 错误码
     * @param cause 原因
     */
    public SystemException(IResponseCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = errorCode.getMessage();
    }

    /**
     * 使用错误码和自定义消息构造系统异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     */
    public SystemException(IResponseCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
    }

    /**
     * 使用错误码、自定义消息和原因构造系统异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     * @param cause 原因
     */
    public SystemException(IResponseCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
    }

    /**
     * 注意：系统异常保留完整堆栈信息
     * 因为系统异常通常代表程序bug或严重问题，需要详细的堆栈信息来排查
     * 所以不重写fillInStackTrace方法
     */
}