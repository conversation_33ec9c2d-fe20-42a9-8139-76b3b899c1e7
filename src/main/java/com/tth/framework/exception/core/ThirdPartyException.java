package com.tth.framework.exception.core;

import com.tth.framework.response.IResponseCode;
import lombok.Getter;

/**
 * 第三方异常
 * 用于表示与第三方服务交互时发生的错误，如API调用失败、超时等
 */
@Getter
public class ThirdPartyException extends RuntimeException {

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 错误码对象
     */
    private final IResponseCode errorCode;

    /**
     * 第三方响应数据
     */
    private final Object responseData;

    /**
     * 使用错误码构造第三方异常
     *
     * @param errorCode 错误码
     */
    public ThirdPartyException(IResponseCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = errorCode.getMessage();
        this.responseData = null;
    }

    /**
     * 使用错误码和原因构造第三方异常
     *
     * @param errorCode 错误码
     * @param cause 原因
     */
    public ThirdPartyException(IResponseCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = errorCode.getMessage();
        this.responseData = null;
    }

    /**
     * 使用错误码和自定义消息构造第三方异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     */
    public ThirdPartyException(IResponseCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
        this.responseData = null;
    }

    /**
     * 使用错误码、自定义消息和原因构造第三方异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     * @param cause 原因
     */
    public ThirdPartyException(IResponseCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
        this.responseData = null;
    }

    /**
     * 使用错误码、自定义消息和第三方响应数据构造第三方异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     * @param responseData 第三方响应数据
     */
    public ThirdPartyException(IResponseCode errorCode, String message, Object responseData) {
        super(message);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
        this.responseData = responseData;
    }

    /**
     * 使用错误码、自定义消息、原因和第三方响应数据构造第三方异常
     *
     * @param errorCode 错误码
     * @param message 自定义消息
     * @param cause 原因
     * @param responseData 第三方响应数据
     */
    public ThirdPartyException(IResponseCode errorCode, String message, Throwable cause, Object responseData) {
        super(message, cause);
        this.errorCode = errorCode;
        this.code = errorCode.getFullCode();
        this.message = message;
        this.responseData = responseData;
    }

    /**
     * 注意：第三方异常保留完整堆栈信息
     * 因为第三方服务异常通常需要详细的堆栈信息来排查问题
     * 所以不重写fillInStackTrace方法
     */
}
