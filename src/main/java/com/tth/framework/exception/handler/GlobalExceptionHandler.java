package com.tth.framework.exception.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.json.JSONUtil;
import com.tth.framework.event.ApiLogEvent;
import com.tth.framework.exception.code.AuthExceptionCode;
import com.tth.framework.exception.code.PermissionExceptionCode;
import com.tth.framework.exception.code.SystemExceptionCode;
import com.tth.framework.exception.code.ValidationExceptionCode;
import com.tth.framework.exception.core.AuthException;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.exception.core.PermissionException;
import com.tth.framework.exception.core.SystemException;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.framework.exception.core.ValidationException;
import com.tth.framework.response.IResponseCode;
import com.tth.framework.response.R;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.HandlerMethod;
import com.tth.framework.annotation.ApiLog;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.nio.file.AccessDeniedException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 处理所有系统中抛出的异常，并返回统一的响应格式
 * 异常处理器按照异常发生的阶段分类：
 * 1. 在Controller执行前触发的异常（参数验证、请求处理等）
 * 2. 在Controller执行过程中或之后触发的异常（业务逻辑、系统内部等）
 *
 * 有写处理方法不能带 HandlerMethod ,不然无法正常工作
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Value("${spring.servlet.multipart.max-file-size}")
    private String maxFileSize;

    @Value("${spring.servlet.multipart.max-request-size}")
    private String maxRequestSize;

    /**
     * 事件发布器，用于发布API日志事件
     */
    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 处理资源未找到异常 (Spring 6.0+)
     * 这个异常在请求URL存在但资源不存在时触发
     * 优先级最高，因为这是请求处理流程中最早的检查
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public R<String> handleNoResourceFoundException(NoResourceFoundException e, HttpServletRequest request, HttpServletResponse response) {
        // 创建响应对象
        R<String> result = R.custom(SystemExceptionCode.NOT_FOUND, "未找到资源: " + request.getRequestURI());

        // 发布API日志事件
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                SystemExceptionCode.NOT_FOUND.getCode(),
                0, // 异常情况下没有耗时信息
                null
        ));
        return result;
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R<String> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request, HttpServletResponse response) {
        // 创建响应对象
        R<String> result = R.custom(SystemExceptionCode.METHOD_NOT_ALLOWED, "不支持的请求方法: " + e.getMethod());

        // 发布API日志事件
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                SystemExceptionCode.METHOD_NOT_ALLOWED.getCode(),
                0, // 异常情况下没有耗时信息
                null
        ));
        return result;
    }

    /**
     * 处理Content-Type不支持异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public R<String> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(SystemExceptionCode.CONTENT_TYPE_NOT_ALLOWED, "不支持的Content-Type: " + e.getContentType() + "，支持的Content-Type为：" + e.getSupportedMediaTypes());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                SystemExceptionCode.CONTENT_TYPE_NOT_ALLOWED.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));
        return result;
    }

    /**
     * 处理 MultipartException 文件上传，接口需要上传文件，但是却不提供甚至不提供参数名：file
     */
    @ExceptionHandler(MultipartException.class)
    public R<String> handleMultipartException(MultipartException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(SystemExceptionCode.NOT_MULTIPART, e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                SystemExceptionCode.CONTENT_TYPE_NOT_ALLOWED.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));
        return result;
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public R<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request, HttpServletResponse response) {
        // 创建响应对象
        R<String> result = R.custom(SystemExceptionCode.FILE_UPLOAD_SIZE_EXCEEDED, "上传文件过大，单个文件不超过" + maxFileSize + "，总共不超过" + maxRequestSize);

        // 发布API日志事件
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                SystemExceptionCode.FILE_UPLOAD_SIZE_EXCEEDED.getCode(),
                0, // 异常情况下没有耗时信息
                null
        ));

        return result;
    }

    /**
     * 处理缺少请求参数异常
     * 只针对缺少必需的 @RequestParam 参数的情况。
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R<String> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.NOT_FOUND, "缺少必要参数: " + e.getParameterName());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.NOT_FOUND.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    //下面：请求参数处理异常
    // 不是使用@Valid 或 @Validated 注解的参数校验异常
    /**
     * 处理缺少请求参数异常
     * 只针对缺少必需的 @RequestParam 或 @RequestPart 参数的情况。
     */
    @ExceptionHandler(MissingServletRequestPartException.class)
    public R<String> handleMissingServletRequestPartException(MissingServletRequestPartException e, HttpServletRequest request, HttpServletResponse response) {
        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.NOT_FOUND, "缺少必要文件参数: " + e.getRequestPartName());

        // 发布API日志事件
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.NOT_FOUND.getCode(),
                0, // 异常情况下没有耗时信息
                null
        ));

        return result;
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R<String> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.TYPE_MISMATCH, "参数类型不匹配: " + e.getName() + "，你提供的值是：" + e.getValue());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.TYPE_MISMATCH.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理HTTP消息不可读异常，包括请求体格式错误、JSON解析错误、枚举反序列化错误等
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 构建错误消息
        String errorMessage;
        IResponseCode errorCode;

        // 检查是否是 InvalidFormatException 引起的
        Throwable cause = e.getCause();
        if (cause instanceof com.fasterxml.jackson.databind.exc.InvalidFormatException) {
            com.fasterxml.jackson.databind.exc.InvalidFormatException ife = (com.fasterxml.jackson.databind.exc.InvalidFormatException) cause;
            // 检查是否是枚举反序列化错误
            if (ife.getTargetType() != null && ife.getTargetType().isEnum()) {
                // 获取枚举类名
                String enumClassName = ife.getTargetType().getSimpleName();
                // 获取无效的值
                String invalidValue = ife.getValue() != null ? ife.getValue().toString() : "null";
                // 获取有效的枚举值列表
                String acceptedValues = ife.getMessage();
                if (acceptedValues.contains("not one of the values accepted for Enum class")) {
                    int startIndex = acceptedValues.indexOf("[");
                    int endIndex = acceptedValues.indexOf("]") + 1; // 包含右中括号
                    if (startIndex >= 0 && endIndex > startIndex) {
                        acceptedValues = acceptedValues.substring(startIndex, endIndex);
                    }
                }

                // 获取属性名
                String propertyName = "";
                if (ife.getPath() != null && ife.getPath().size() > 0) {
                    propertyName = ife.getPath().get(ife.getPath().size() - 1).getFieldName();
                }

                if (propertyName != null && !propertyName.isEmpty()) {
                    errorMessage = String.format("%s：%s 不是有效的值，支持的值为：%s",
                            propertyName, invalidValue, acceptedValues);
                } else {
                    errorMessage = String.format("%s 不是有效的值，支持的值为：%s",
                            invalidValue, acceptedValues);
                }
                errorCode = ValidationExceptionCode.PARAM_FORMAT_ERROR;
            } else {
                // 其他格式错误
                errorMessage = "请求体格式错误: " + ife.getMessage();
                errorCode = ValidationExceptionCode.PARAM_FORMAT_ERROR;
            }
        } else {
            // 其他类型的消息不可读异常
            errorMessage = "请求体格式错误，不是json格式";
            errorCode = ValidationExceptionCode.NOT_JSON_FORMAT;
        }

        // 创建响应对象
        R<String> result = R.custom(errorCode, errorMessage);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                errorCode.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    //下面：参数验证异常

    /**
     * 当使用 @Valid 或 @Validated 注解对方法参数进行校验时触发，不是请求体哦。而是 @RequestParam 或 @RequestPart 接收到的参数
     * 注意：想要触发，在Controller方法上，不要使用 @Validated 注解，否则不会触发，因为优先级没有 @Validated 高
     */
    @ExceptionHandler(HandlerMethodValidationException.class)
    public R<String> handleHandlerMethodValidationException(HandlerMethodValidationException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        Map<String, Object> errors = new HashMap<>();
        for (var error : e.getAllValidationResults()) {
            String field = error.getMethodParameter().getParameterName();
            List<String> messages = error.getResolvableErrors().stream()
                    .map(MessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList());

            errors.put(field, messages);
        }

        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.PARAM_VALIDATION_ERROR, errors.toString());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.PARAM_VALIDATION_ERROR.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理参数验证异常 (JSR-303)，
     * 这才是验证 @RequestBody 时触发
     * 方法参数一定要加 @Valid 或 @Validated 注解，不然不会触发
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .reduce((a, b) -> a + "; " + b)
                .orElse(ValidationExceptionCode.PARAM_VALIDATION_ERROR.getMessage());

        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.PARAM_VALIDATION_ERROR, message);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.PARAM_VALIDATION_ERROR.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理约束违反异常
     * 在 Controller 类上使用 @Validated，并且在方法参数上没有使用 @Valid 时触发，如果不用，就会触发上面另一个异常：HandlerMethodValidationException
     * 并且检验的是方法参数，而不是 请求体
     * 优先级比单纯使用 @Valid 注解要高
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R<String> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        String message = e.getConstraintViolations().stream()
                .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                .reduce((a, b) -> a + "; " + b)
                .orElse(ValidationExceptionCode.PARAM_VALIDATION_ERROR.getMessage());

        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.PARAM_VALIDATION_ERROR, message);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.PARAM_VALIDATION_ERROR.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 未验证
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public R<String> handleBindException(BindException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .reduce((a, b) -> a + "; " + b)
                .orElse(ValidationExceptionCode.PARAM_VALIDATION_ERROR.getMessage());

        // 创建响应对象
        R<String> result = R.custom(ValidationExceptionCode.PARAM_VALIDATION_ERROR, message);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                ValidationExceptionCode.PARAM_VALIDATION_ERROR.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    //下面是认证和授权的异常
    /**
     * 未验证
     * 处理访问拒绝异常
     * 访问文件或目录时权限不足的异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public R<String> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(PermissionExceptionCode.FORBIDDEN);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                PermissionExceptionCode.FORBIDDEN.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理Sa-Token异常
     * 注意：需要根据Sa-Token的具体异常类型进行调整
     */
    @ExceptionHandler(NotLoginException.class)
    public R<String> handleNotLoginException(cn.dev33.satoken.exception.NotLoginException e, HttpServletRequest request, HttpServletResponse response) {
        // 创建响应对象
        R<String> result = R.custom(AuthExceptionCode.UNAUTHORIZED);

        // 发布API日志事件
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                AuthExceptionCode.UNAUTHORIZED.getCode(),
                0, // 异常情况下没有耗时信息
                null
        ));

        return result;
    }

    /**
     * 将权限和角色一起处理
     */
    @ExceptionHandler({NotPermissionException.class, NotRoleException.class})
    public R<String> handleNotPermissionException(cn.dev33.satoken.exception.SaTokenException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(PermissionExceptionCode.FORBIDDEN);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                PermissionExceptionCode.FORBIDDEN.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    // 下面：处理自定义异常
    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthException.class)
    public R<String> handleAuthException(AuthException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(e.getErrorCode(), e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                e.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public R<String> handleBizException(BizException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(e.getErrorCode(), e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                e.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(PermissionException.class)
    public R<String> handlePermissionException(PermissionException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(e.getErrorCode(), e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                e.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public R<String> handleSystemException(SystemException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(e.getErrorCode(), e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                e.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理第三方异常
     */
    @ExceptionHandler(ThirdPartyException.class)
    public R<String> handleThirdPartyException(ThirdPartyException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(e.getErrorCode(), e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                e.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(ValidationException.class)
    public R<String> handleValidationException(ValidationException e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象
        R<String> result = R.custom(e.getErrorCode(), e.getMessage());

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                e.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }

    /**
     * 处理所有未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public R<String> handleException(Exception e, HttpServletRequest request, HttpServletResponse response, HandlerMethod handlerMethod) {
        // 创建响应对象，生产环境不返回具体错误信息
        R<String> result = R.custom(SystemExceptionCode.INTERNAL_SERVER_ERROR);

        // 发布API日志事件
        ApiLog apiLog = handlerMethod != null ? handlerMethod.getMethodAnnotation(ApiLog.class) : null;
        eventPublisher.publishEvent(new ApiLogEvent(
                this,
                request,
                response,
                JSONUtil.toJsonStr(result),
                e,
                SystemExceptionCode.INTERNAL_SERVER_ERROR.getCode(),
                0, // 异常情况下没有耗时信息
                apiLog
        ));

        return result;
    }
}
