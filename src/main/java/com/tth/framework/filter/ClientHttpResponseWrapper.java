package com.tth.framework.filter;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * ClientHttpResponse的包装类，用于缓存响应体，使其可以被多次读取
 * 这个类在第一次读取响应体时将其缓存到内存中，后续的读取操作将使用缓存的数据
 */
public class ClientHttpResponseWrapper implements ClientHttpResponse {

    private final ClientHttpResponse original;
    private byte[] body;
    private String bodyString;

    public ClientHttpResponseWrapper(ClientHttpResponse original) {
        this.original = original;
    }

    @Override
    public HttpStatusCode getStatusCode() throws IOException {
        return original.getStatusCode();
    }

    @Override
    public String getStatusText() throws IOException {
        return original.getStatusText();
    }

    @Override
    public void close() {
        original.close();
    }

    @Override
    public InputStream getBody() throws IOException {
        if (body == null) {
            body = StreamUtils.copyToByteArray(original.getBody());
        }
        return new ByteArrayInputStream(body);
    }

    @Override
    public HttpHeaders getHeaders() {
        return original.getHeaders();
    }

    /**
     * 获取响应体的字符串表示
     * 如果响应体尚未被读取，则先读取并缓存
     *
     * @return 响应体的字符串表示
     * @throws IOException 如果读取响应体时发生IO异常
     */
    public String getBodyAsString() throws IOException {
        if (bodyString == null) {
            if (body == null) {
                body = StreamUtils.copyToByteArray(original.getBody());
            }
            bodyString = new String(body, StandardCharsets.UTF_8);
        }
        return bodyString;
    }
}
