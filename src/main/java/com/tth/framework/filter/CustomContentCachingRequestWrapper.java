package com.tth.framework.filter;

import cn.hutool.http.ContentType;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.Getter;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Collection;

import cn.hutool.json.JSONObject;
import jakarta.servlet.http.Part;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义内容缓存请求包装器
 * 在创建时就读取并缓存请求体，而不是使用延迟加载策略
 */
@Slf4j
public class CustomContentCachingRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body;

    /**
     * -- GETTER --
     *  获取请求体的字符串表示
     */
    @Getter
    private final String bodyString;

    /**
     * 是否是多部分请求（包含文件上传）
     */
    @Getter
    private final boolean isMultipart;

    public CustomContentCachingRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);

        // 检查是否是多部分请求
        String contentType = request.getContentType();
        isMultipart = contentType != null && contentType.toLowerCase().contains(ContentType.MULTIPART.getValue());

        // 添加调试日志
        if (isMultipart) {
            log.debug("检测到多部分请求，Content-Type: {}", contentType);
            log.debug("请求类型: {}", request.getClass().getName());
        }

        // 如果是多部分请求，则直接处理
        if (isMultipart) {
            // 对于多部分请求，不缓存请求体，因为文件可能很大
            body = new byte[0];

            // 创建结果对象
            JSONObject result = new JSONObject();

            // 使用 Servlet 3.0+ 的 getParts() API 获取文件信息和表单参数
            try {
                Collection<Part> parts = request.getParts();
                if (parts != null && !parts.isEmpty()) {
                    for (Part part : parts) {
                        String name = part.getName();
                        String filename = part.getSubmittedFileName();
                        long size = part.getSize();
                        String partContentType = part.getContentType();

                        // 处理文件部分（有文件名的部分）
                        if (filename != null && !filename.isEmpty()) {
                            JSONObject fileInfo = new JSONObject();
                            fileInfo.set("filename", filename);
                            fileInfo.set("size", size);
                            if (partContentType != null) {
                                fileInfo.set("contentType", partContentType);
                            }
                            result.set(name, fileInfo);
                        }
                        // 处理普通表单参数（没有文件名的部分）
                        else {
                            // 读取参数值
                            try (InputStream is = part.getInputStream();
                                 ByteArrayOutputStream os = new ByteArrayOutputStream()) {
                                byte[] buffer = new byte[4096];
                                int bytesRead;
                                while ((bytesRead = is.read(buffer)) != -1) {
                                    os.write(buffer, 0, bytesRead);
                                }
                                String value = os.toString(StandardCharsets.UTF_8);
                                result.set(name, value);
                            }
                        }
                    }

                    // 如果没有找到任何参数，但是请求是 multipart/form-data 类型
                    if (result.isEmpty()) {
                        result.set("info", "[检测到多部分请求，但没有找到参数]");
                    }
                } else {
                    // 如果没有部分，但是请求是 multipart/form-data 类型
                    result.set("info", "[检测到多部分请求，但没有部分]");
                }
            } catch (Exception e) {
                log.debug("获取请求部分失败: {}", e.getMessage());
                result.set("error", "[检测到多部分请求，但获取部分失败]");
            }

            // 创建请求体字符串
            bodyString = result.toString();
        } else {
            // 对于非多部分请求，读取并缓存请求体
            InputStream inputStream = request.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            body = outputStream.toByteArray();

            // 将请求体转换为字符串
            String characterEncoding = request.getCharacterEncoding();
            if (characterEncoding == null) {
                characterEncoding = StandardCharsets.UTF_8.name();
            }

            try {
                //直接移除所有空白字符
                bodyString = new String(body, characterEncoding).replaceAll("\\s+", "");
            } catch (UnsupportedEncodingException e) {
                throw new IOException("Failed to parse request body", e);
            }
        }

        log.debug("Request body cached: {}", bodyString);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream inputStream = new ByteArrayInputStream(body);

        return new ServletInputStream() {
            @Override
            public int read() throws IOException {
                return inputStream.read();
            }

            @Override
            public boolean isFinished() {
                return inputStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                throw new UnsupportedOperationException("ReadListener is not supported");
            }
        };
    }
}
