package com.tth.framework.filter;

import cn.hutool.http.ContentType;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 请求体缓存过滤器
 * 用于包装请求，使请求体可以被多次读取
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RequestBodyCachingFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;

            // 只对可能包含请求体的方法进行包装
            String method = httpRequest.getMethod();
            if ("POST".equalsIgnoreCase(method) || "PUT".equalsIgnoreCase(method) ||
                "PATCH".equalsIgnoreCase(method) || "DELETE".equalsIgnoreCase(method)) {

                // 获取内容类型
                String contentType = httpRequest.getContentType();

                // 只对JSON和表单数据进行包装
                if(null != contentType && (contentType.toLowerCase().contains(ContentType.MULTIPART.getValue())
                    || contentType.toLowerCase().contains(ContentType.JSON.getValue())
                    || contentType.toLowerCase().contains(ContentType.FORM_URLENCODED.getValue()))){

                    try {
                        log.debug("开始包装请求 - 方法: {}, ContentType: {}", method, contentType);
                        // 包装请求，使用自定义的CustomContentCachingRequestWrapper
                        // 这个包装器在创建时就读取并缓存请求体
                        CustomContentCachingRequestWrapper requestWrapper = new CustomContentCachingRequestWrapper(httpRequest);
                        log.debug("请求包装成功 - isMultipart: {}, bodyString: {}", requestWrapper.isMultipart(),
                               requestWrapper.isMultipart() ? "多部分请求不显示请求体" : requestWrapper.getBodyString());

                        // 继续过滤器链
                        log.debug("继续过滤器链");
                        chain.doFilter(requestWrapper, response);
                        log.debug("过滤器链执行完成");
                        return;
                    } catch (Exception e) {
                        log.warn("包装请求失败，使用原始请求继续: {}", e.getMessage());
                        log.debug("异常详情", e);
                    }
                }
            }
        }

        // 如果不需要包装或包装失败，使用原始请求继续
        chain.doFilter(request, response);
    }
}
