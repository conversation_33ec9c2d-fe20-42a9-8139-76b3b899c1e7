package com.tth.framework.interceptor;

import cn.hutool.core.util.StrUtil;
import com.tth.framework.service.BatchFileUrlService;
import com.tth.framework.service.FileUrlFieldCache;
import com.tth.framework.service.FileUrlFieldCache.FileUrlFieldInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.sql.Statement;
import java.util.*;

/**
 * 文件URL自动填充拦截器
 * 在MyBatis查询结果返回后，自动批量填充文件URL
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
public class FileUrlInterceptor implements Interceptor {

    @Resource
    private BatchFileUrlService batchFileUrlService;

    @Resource
    private FileUrlFieldCache fileUrlFieldCache;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 执行原始查询
        Object result = invocation.proceed();

        // 处理查询结果
        if (result instanceof List<?> list) {
            if (!list.isEmpty()) {
                long startTime = System.currentTimeMillis();
                batchProcessFileUrls(list);
                long endTime = System.currentTimeMillis();
                
                log.debug("批量处理文件URL完成，处理{}条记录，耗时{}ms", list.size(), endTime - startTime);
            }
        }

        return result;
    }

    /**
     * 批量处理文件URL
     */
    private void batchProcessFileUrls(List<?> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 1. 收集所有需要处理的对象和字段信息
        Map<Object, List<FileUrlFieldInfo>> objectFieldMap = new HashMap<>();
        Set<String> singleFileIds = new HashSet<>();
        Set<String> multiFileIds = new HashSet<>();

        for (Object obj : list) {
            if (obj == null) continue;

            List<FileUrlFieldInfo> fileFields = fileUrlFieldCache.getFileUrlFields(obj.getClass());
            if (!fileFields.isEmpty()) {
                objectFieldMap.put(obj, fileFields);

                // 收集文件ID
                for (FileUrlFieldInfo fieldInfo : fileFields) {
                    String fileId = fieldInfo.getFieldValue(obj);
                    if (StrUtil.isNotBlank(fileId)) {
                        if (fieldInfo.isMultiFile()) {
                            multiFileIds.add(fileId);
                        } else {
                            singleFileIds.add(fileId);
                        }
                    }
                }
            }
        }

        // 2. 批量获取文件URL
        Map<String, String> singleUrlMap = new HashMap<>();
        Map<String, String> multiUrlMap = new HashMap<>();

        if (!singleFileIds.isEmpty()) {
            singleUrlMap = batchFileUrlService.batchGetFileUrls(singleFileIds);
        }

        if (!multiFileIds.isEmpty()) {
            multiUrlMap = batchFileUrlService.batchGetMultiFileUrls(multiFileIds);
        }

        // 3. 批量设置URL到对象中
        for (Map.Entry<Object, List<FileUrlFieldInfo>> entry : objectFieldMap.entrySet()) {
            Object obj = entry.getKey();
            List<FileUrlFieldInfo> fieldInfos = entry.getValue();

            for (FileUrlFieldInfo fieldInfo : fieldInfos) {
                setFileUrl(obj, fieldInfo, singleUrlMap, multiUrlMap);
            }
        }

        log.debug("批量处理文件URL统计：单文件{}个，多文件{}个，对象{}个", 
            singleFileIds.size(), multiFileIds.size(), objectFieldMap.size());
    }

    /**
     * 设置单个字段的文件URL
     */
    private void setFileUrl(Object obj, FileUrlFieldInfo fieldInfo, 
                           Map<String, String> singleUrlMap, Map<String, String> multiUrlMap) {
        try {
            String fileId = fieldInfo.getFieldValue(obj);
            if (StrUtil.isBlank(fileId)) {
                return;
            }

            String url;
            if (fieldInfo.isMultiFile()) {
                // 处理多文件URL
                url = multiUrlMap.get(fileId);
            } else {
                // 处理单文件URL
                url = singleUrlMap.get(fileId);
            }

            // 设置URL，如果没有找到URL则保持原值
            if (url != null) {
                fieldInfo.setFieldValue(obj, url);
            }

        } catch (Exception e) {
            log.error("设置文件URL失败，字段：{}，对象：{}", 
                fieldInfo.getField().getName(), obj.getClass().getSimpleName(), e);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以通过配置文件设置属性
    }
}
