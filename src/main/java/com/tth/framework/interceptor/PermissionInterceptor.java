package com.tth.framework.interceptor;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.stp.StpUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 权限拦截器
 * 用于处理请求的权限检查
 */
@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {

    public static final String USER_ID = "UserId";
    public static final String USER_TYPE = "UserType";
    public static final String REQUEST_ID = "RequestId";
    public static final String REQUEST_START_TIME = "RequestStartTime";
    public static final String CURRENT_IP = "CurrentIp";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取RequestIdInterceptor生成的请求ID
        String requestId = ThreadContext.get(REQUEST_ID);
        String clientIp = ThreadContext.get(CURRENT_IP);

        // 如果不是处理Controller方法的请求，则直接放行
        if (!(handler instanceof HandlerMethod)) {
            log.debug("请求ID: {}, 客户端IP: {}, 非Controller请求直接放行", requestId, clientIp);
            return true;
        }

        // 记录请求路径和方法
        String requestMethod = request.getMethod();
        String requestURI = request.getRequestURI();
        log.info("请求ID: {}, 客户端IP: {}, 请求方法: {}, 请求路径: {}",
                requestId, clientIp, requestMethod, requestURI);

        try {
            // 检查是否登录
            if (StpUtil.isLogin()) {
                // 获取当前登录用户ID
                Object loginId = StpUtil.getLoginId();
                // 将用户ID存入线程上下文，方便后续使用
                ThreadContext.put(USER_ID, loginId.toString());

                // 获取用户类型
                Object userType = StpUtil.getSession().get("userType");
                if (userType != null) {
                    ThreadContext.put(USER_TYPE, userType.toString());
                }

                log.debug("请求ID: {}, 当前登录用户ID: {}, 用户类型: {}", requestId, loginId, userType);
            }

            // 注意：这里不需要显式检查权限，因为Sa-Token的注解会自动处理
            // 如果需要在这里进行额外的权限检查，可以在这里添加代码

            return true;
        } catch (NotLoginException e) {
            // 未登录异常由全局异常处理器处理
            log.warn("请求ID: {}, 路径: {}, 未登录异常: {}",
                    ThreadContext.get(REQUEST_ID), request.getRequestURI(), e.getMessage());
            throw e;
        } catch (NotPermissionException | NotRoleException e) {
            // 权限不足异常由全局异常处理器处理
            log.warn("请求ID: {}, 路径: {}, 权限不足异常: {}",
                    ThreadContext.get(REQUEST_ID), request.getRequestURI(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("请求ID: {}, 路径: {}, 权限拦截器异常",
                    ThreadContext.get(REQUEST_ID), request.getRequestURI(), e);
            throw e;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 计算请求处理时间
        String requestId = ThreadContext.get(REQUEST_ID);
        String startTimeStr = ThreadContext.get(REQUEST_START_TIME);
        if (startTimeStr != null) {
            long startTime = Long.parseLong(startTimeStr);
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;

            // 将响应时间添加到响应头
            response.setHeader("X-Response-Time", costTime + "ms");

            // 记录请求处理时间
            log.info("请求ID: {}, 路径: {}, 处理时间: {}ms",
                    requestId, request.getRequestURI(), costTime);
        }

        // 请求完成后清理线程上下文
        ThreadContext.remove(USER_ID);
        ThreadContext.remove(USER_TYPE);
        ThreadContext.remove(REQUEST_ID);
        ThreadContext.remove(REQUEST_START_TIME);
        ThreadContext.remove(CURRENT_IP);
    }
}
