package com.tth.framework.interceptor;

import cn.hutool.core.util.IdUtil;
import com.tth.framework.utils.IpUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 请求ID拦截器
 * 用于为所有请求生成唯一的请求ID，包括被排除的路径
 */
@Slf4j
public class RequestIdInterceptor implements HandlerInterceptor {

    public static final String REQUEST_ID = "RequestId";
    public static final String REQUEST_START_TIME = "RequestStartTime";
    public static final String CURRENT_IP = "CurrentIp";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 生成请求ID并设置到线程上下文和响应头
        String requestId = request.getHeader(REQUEST_ID);
        if (!StringUtils.hasText(requestId)) {
            requestId = IdUtil.fastSimpleUUID();
        }
        ThreadContext.put(REQUEST_ID, requestId);
        response.setHeader(REQUEST_ID, requestId);

        // 记录请求开始时间
        ThreadContext.put(REQUEST_START_TIME, String.valueOf(System.currentTimeMillis()));

        // 获取并记录客户端IP
        String clientIp = IpUtil.getClientIp(request);
        ThreadContext.put(CURRENT_IP, clientIp);

        // 记录请求路径和方法
        String requestMethod = request.getMethod();
        String requestURI = request.getRequestURI();
        log.debug("请求ID: {}, 客户端IP: {}, 请求方法: {}, 请求路径: {}",
                requestId, clientIp, requestMethod, requestURI);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 计算请求处理时间
        String requestId = ThreadContext.get(REQUEST_ID);
        String startTimeStr = ThreadContext.get(REQUEST_START_TIME);
        if (startTimeStr != null) {
            long startTime = Long.parseLong(startTimeStr);
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;

            // 将响应时间添加到响应头
            response.setHeader("X-Response-Time", costTime + "ms");

            // 记录请求处理时间
            log.debug("请求ID: {}, 路径: {}, 处理时间: {}ms",
                    requestId, request.getRequestURI(), costTime);
        }

        // 注意：不要在这里清理ThreadContext，因为PermissionInterceptor负责清理
        // 如果请求被排除，不会经过PermissionInterceptor，所以这里需要检查是否是被排除的路径
        String requestURI = request.getRequestURI();
        boolean isExcludedPath = false;
        for (String pattern : new String[]{
                "/auth/login", "/auth/register", "/auth/token/refresh", "/userBase",
                "/doc.html", "/swagger-ui.html", "/swagger-resources", "/webjars",
                "/v3/api-docs", "/v2/api-docs", "/api-docs", "/configuration", "/error"
        }) {
            if (requestURI.startsWith(pattern)) {
                isExcludedPath = true;
                break;
            }
        }

        // 如果是被排除的路径，则在这里清理ThreadContext
        if (isExcludedPath) {
            ThreadContext.remove(REQUEST_ID);
            ThreadContext.remove(REQUEST_START_TIME);
            ThreadContext.remove(CURRENT_IP);
        }
    }
}
