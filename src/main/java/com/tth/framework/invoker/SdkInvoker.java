package com.tth.framework.invoker;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.tth.common.enums.SuccessStatusEnum;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.framework.interceptor.PermissionInterceptor;
import com.tth.framework.utils.IpUtil;
import com.tth.modules.system.entity.ApiRequestLog;
import com.tth.modules.system.enums.LogTypeEnum;
import com.tth.modules.system.service.ApiRequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.ThreadContext;

import com.tth.framework.utils.SpringContextHolder;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * SDK调用包装器
 * 用于记录SDK调用日志，包括请求参数、响应结果、异常信息等
 */
@Slf4j
public class SdkInvoker {

    /**
     * 调用SDK并记录日志（带结果检查器）
     *
     * @param module        模块名称
     * @param operation     操作描述
     * @param request       请求参数
     * @param sdkCall       实际的SDK调用代码
     * @param resultChecker 结果检查器，用于判断调用是否成功
     * @param <T>           返回结果类型
     * @return              SDK调用结果
     */
    public static <T> T invoke(String module, String operation, Object request,
                              Supplier<T> sdkCall, SdkResultChecker<T> resultChecker) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 生成请求ID
        String requestId = IdUtil.fastSimpleUUID();

        // 执行结果和异常
        T result = null;
        Exception exception = null;
        boolean isSuccess = false;
        String errorMessage = null;
        Integer responseStatus = null;
        SuccessStatusEnum bizStatus = null;
        Integer httpStatus = null;

        try {
            // 执行SDK调用
            result = sdkCall.get();

            // 使用结果检查器判断是否成功
            if (result != null && resultChecker != null) {
                SdkResultChecker.ResultCheckOutcome outcome = resultChecker.check(result);
                isSuccess = outcome.isSuccess();

                // 无论成功还是失败，都保存完整的状态信息
                errorMessage = outcome.getErrorMessage();
                responseStatus = outcome.getResponseStatus();
                bizStatus = outcome.getBizStatus();
                httpStatus = outcome.getHttpStatus();
            } else {
                // 如果没有提供结果检查器，默认成功
                isSuccess = true;
            }

            return result;
        } catch (Exception e) {
            // 捕获异常
            exception = e;
            errorMessage = e.getMessage();
            bizStatus = SuccessStatusEnum.FAILURE;
            httpStatus = 500;

            // 包装为第三方异常
            if (!(e instanceof ThirdPartyException)) {
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, e);
            }
            throw e;
        } finally {
            // 无论成功还是失败，都记录日志
            long duration = System.currentTimeMillis() - startTime;
            logSdkInvocation(requestId, module, operation, request, result,
                            exception, isSuccess, errorMessage, responseStatus, bizStatus, httpStatus, duration);
        }
    }

    /**
     * 调用SDK并记录日志（简化版，默认无异常即成功）
     *
     * @param module    模块名称
     * @param operation 操作描述
     * @param request   请求参数
     * @param sdkCall   实际的SDK调用代码
     * @param <T>       返回结果类型
     * @return          SDK调用结果
     */
    public static <T> T invoke(String module, String operation, Object request, Supplier<T> sdkCall) {
        return invoke(module, operation, request, sdkCall, result -> SdkResultChecker.ResultCheckOutcome.success());
    }

    /**
     * 调用SDK并记录日志（无返回值版本）
     *
     * @param module    模块名称
     * @param operation 操作描述
     * @param request   请求参数
     * @param sdkCall   实际的SDK调用代码
     */
    public static void invoke(String module, String operation, Object request, Runnable sdkCall) {
        long startTime = System.currentTimeMillis();
        String requestId = IdUtil.fastSimpleUUID();
        Exception exception = null;
        boolean isSuccess = false;
        String errorMessage = null;
        Integer responseStatus = null;
        SuccessStatusEnum bizStatus = null;
        Integer httpStatus = null;

        try {
            sdkCall.run();
            isSuccess = true;
        } catch (Exception e) {
            exception = e;
            errorMessage = e.getMessage();
            responseStatus = 500;
            bizStatus = SuccessStatusEnum.FAILURE;
            httpStatus = 500;

            // 包装为第三方异常
            if (!(e instanceof ThirdPartyException)) {
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, e);
            }
            throw e;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            logSdkInvocation(requestId, module, operation, request, null,
                            exception, isSuccess, errorMessage, responseStatus, bizStatus, httpStatus, duration);
        }
    }

    /**
     * 统一的SDK调用日志记录方法
     *
     * @param requestId    请求ID
     * @param module       模块名称
     * @param operation    操作描述
     * @param request      请求参数
     * @param result       响应结果
     * @param exception    异常信息
     * @param isSuccess    是否成功
     * @param errorMessage   错误消息
     * @param responseStatus 响应状态码
     * @param bizStatus      业务状态
     * @param httpStatus     HTTP状态码
     * @param duration       调用耗时
     */
    private static void logSdkInvocation(String requestId, String module, String operation,
                                        Object request, Object result, Exception exception,
                                        boolean isSuccess, String errorMessage,
                                        Integer responseStatus, SuccessStatusEnum bizStatus, Integer httpStatus, long duration) {
        try {
            // 创建日志对象
            ApiRequestLog apiRequestLog = new ApiRequestLog();
            apiRequestLog.setLogType(LogTypeEnum.THIRD_PARTY_API);
            apiRequestLog.setRequestId(requestId);
            // 设置URL为request对象的全类名
            if (request != null) {
                apiRequestLog.setUrl(request.getClass().getName());
            }
            apiRequestLog.setMethod("SDK");
            apiRequestLog.setModule(module);
            apiRequestLog.setOperation(operation);

            // 设置请求参数
            if (request != null) {
                try {
                    String requestStr = JSONUtil.toJsonStr(request);
                    // 如果请求体过长，只保留前2000个字符
                    if (requestStr.length() > 2000) {
                        requestStr = requestStr.substring(0, 2000) + "...";
                    }
                    apiRequestLog.setRequestBody(requestStr);
                } catch (Exception e) {
                    log.warn("无法序列化请求参数: {}", e.getMessage());
                    apiRequestLog.setRequestBody("无法序列化的请求参数");
                }
            }

            // 设置状态和相关信息
            apiRequestLog.setResponseStatus(responseStatus);
            apiRequestLog.setBizStatus(bizStatus != null ? bizStatus : (isSuccess ? SuccessStatusEnum.SUCCESS : SuccessStatusEnum.FAILURE));
            apiRequestLog.setHttpStatus(httpStatus);

            // 记录响应结果
            if (result != null) {
                try {
                    String responseStr = JSONUtil.toJsonStr(result);
                    if (responseStr.length() > 2000) {
                        responseStr = responseStr.substring(0, 2000) + "...";
                    }
                    apiRequestLog.setResponseBody(responseStr);
                } catch (Exception e) {
                    log.warn("无法序列化响应结果: {}", e.getMessage());
                    apiRequestLog.setResponseBody("无法序列化的响应结果");
                }
            }

            // 记录错误信息
            if (!isSuccess) {
                apiRequestLog.setErrorMsg(ExceptionUtils.getStackTrace(exception));
            }

            // 设置耗时
            apiRequestLog.setDuration(duration);

            // 设置用户ID
            String userId = ThreadContext.get(PermissionInterceptor.USER_ID);
            if (userId != null) {
                try {
                    apiRequestLog.setUserId(Long.parseLong(userId));
                } catch (NumberFormatException e) {
                    log.warn("无法解析用户ID: {}", userId);
                }
            }

            // 设置IP地址
            String ip = IpUtil.getCurrentIp();
            apiRequestLog.setIp(ip);

            // 异步保存到数据库
            saveLogAsync(apiRequestLog);

            // 记录日志
            if (isSuccess) {
                log.debug("SDK调用成功 - 请求ID: {}, 模块: {}, 操作: {}, 耗时: {}ms",
                        requestId, module, operation, duration);
            } else {
                log.error("SDK调用失败 - 请求ID: {}, 模块: {}, 操作: {}, 错误: {}, 耗时: {}ms",
                        requestId, module, operation, errorMessage, duration);
            }
        } catch (Exception e) {
            log.error("记录SDK调用日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步保存日志到数据库
     *
     * @param apiRequestLog 日志对象
     */
    private static void saveLogAsync(ApiRequestLog apiRequestLog) {
        Executor commonExecutor = SpringContextHolder.getBean("commonExecutor", Executor.class);
        CompletableFuture.runAsync(() -> {
            try {
                // 获取当前用户ID
                String userId = ThreadContext.get(PermissionInterceptor.USER_ID);

                // 在新线程中恢复ThreadContext数据
                if (userId != null) {
                    ThreadContext.put(PermissionInterceptor.USER_ID, userId);
                }

                try {
                    // 在异步线程中获取IP位置信息
                    if (apiRequestLog.getIp() != null) {
                        apiRequestLog.setIpPosition(IpUtil.getRealAddressByIP(apiRequestLog.getIp()));
                    }

                    // 从Spring上下文中获取ApiRequestLogService
                    ApiRequestLogService apiRequestLogService = SpringContextHolder.getBean(ApiRequestLogService.class);

                    // 保存日志
                    apiRequestLogService.save(apiRequestLog);
                    log.debug("SDK调用日志保存成功，请求ID: {}", apiRequestLog.getRequestId());
                } catch (Exception e) {
                    log.error("保存SDK调用日志失败: {}", e.getMessage(), e);
                } finally {
                    // 清理新线程中的ThreadContext数据
                    ThreadContext.remove(PermissionInterceptor.USER_ID);
                }
            } catch (Exception e) {
                log.error("在异步线程中处理ThreadContext失败: {}", e.getMessage(), e);
            }
        }, commonExecutor);
    }
}
