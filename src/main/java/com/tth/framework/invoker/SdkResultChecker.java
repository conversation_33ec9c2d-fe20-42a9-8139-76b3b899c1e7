package com.tth.framework.invoker;

import com.tth.common.enums.SuccessStatusEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SDK调用结果检查器接口
 * 用于判断SDK调用结果是否成功
 *
 * @param <T> SDK调用结果类型
 */
@FunctionalInterface
public interface SdkResultChecker<T> {
    /**
     * 判断SDK调用结果是否成功
     *
     * @param result SDK调用结果
     * @return 结果检查结果
     */
    ResultCheckOutcome check(T result);

    /**
     * 结果检查结果
     * 包含成功标志、响应码和错误消息
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    class ResultCheckOutcome {
        private final boolean success;
        private final Integer responseStatus;
        private final SuccessStatusEnum bizStatus;
        private final Integer httpStatus;
        private final String errorMessage;

        /**
         * 创建成功结果
         *
         * @return 成功结果
         */
        public static ResultCheckOutcome success() {
            return new ResultCheckOutcome(true, 200, SuccessStatusEnum.SUCCESS, 200, null);
        }

        /**
         * 创建成功结果，并指定完整的状态信息
         *
         * @param responseStatus 响应状态码
         * @param httpStatus HTTP状态码
         * @return 成功结果
         */
        public static ResultCheckOutcome success(Integer responseStatus, Integer httpStatus) {
            return new ResultCheckOutcome(true, responseStatus, SuccessStatusEnum.SUCCESS, httpStatus, null);
        }

        /**
         * 创建失败结果
         *
         * @param errorMessage 错误消息
         * @return 失败结果
         */
        public static ResultCheckOutcome failure(String errorMessage) {
            return new ResultCheckOutcome(false, 500, SuccessStatusEnum.FAILURE, 500, errorMessage);
        }

        /**
         * 创建失败结果，并指定完整的状态信息
         *
         * @param errorMessage 错误消息
         * @param responseStatus 响应状态码
         * @param httpStatus HTTP状态码
         * @return 失败结果
         */
        public static ResultCheckOutcome failure(String errorMessage, Integer responseStatus, Integer httpStatus) {
            return new ResultCheckOutcome(false, responseStatus, SuccessStatusEnum.FAILURE, httpStatus, errorMessage);
        }


    }
}
