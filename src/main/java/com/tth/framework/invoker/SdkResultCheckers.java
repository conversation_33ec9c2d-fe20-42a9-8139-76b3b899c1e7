package com.tth.framework.invoker;

/**
 * 预定义的SDK结果检查器
 * 提供常用SDK的结果检查逻辑
 */
public class SdkResultCheckers {

    /**
     * 通用的成功检查器，始终返回成功
     */
    public static final SdkResultChecker<Object> ALWAYS_SUCCESS =
        result -> SdkResultChecker.ResultCheckOutcome.success();

    /**
     * 通用的布尔值检查器，检查返回结果是否为true
     */
    public static final SdkResultChecker<Boolean> BOOLEAN =
        result -> result ?
            SdkResultChecker.ResultCheckOutcome.success() :
            SdkResultChecker.ResultCheckOutcome.failure("操作返回false");

    /**
     * 检查对象是否为空
     */
    public static final SdkResultChecker<Object> NOT_NULL =
        result -> result != null ?
            SdkResultChecker.ResultCheckOutcome.success() :
            SdkResultChecker.ResultCheckOutcome.failure("返回结果为空");

    /**
     * 创建一个检查指定字段值的检查器
     *
     * @param fieldName     字段名称
     * @param expectedValue 期望的字段值
     * @param <T>           对象类型
     * @return              结果检查器
     */
    public static <T> SdkResultChecker<T> fieldEquals(String fieldName, String expectedValue) {
        return result -> {
            try {
                // 使用反射获取字段值
                java.lang.reflect.Field field = result.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                Object fieldValue = field.get(result);

                if (expectedValue.equals(String.valueOf(fieldValue))) {
                    return SdkResultChecker.ResultCheckOutcome.success();
                } else {
                    return SdkResultChecker.ResultCheckOutcome.failure(
                        fieldName + "字段值不符合预期，期望: " + expectedValue + ", 实际: " + fieldValue
                    );
                }
            } catch (Exception e) {
                return SdkResultChecker.ResultCheckOutcome.failure(
                    "反射获取字段值失败: " + e.getMessage()
                );
            }
        };
    }

    /**
     * 创建一个检查对象方法返回值的检查器
     *
     * @param methodName    方法名称
     * @param expectedValue 期望的返回值
     * @param <T>           对象类型
     * @return              结果检查器
     */
    public static <T> SdkResultChecker<T> methodEquals(String methodName, String expectedValue) {
        return result -> {
            try {
                // 使用反射调用方法
                java.lang.reflect.Method method = result.getClass().getMethod(methodName);
                Object returnValue = method.invoke(result);

                if (expectedValue.equals(String.valueOf(returnValue))) {
                    return SdkResultChecker.ResultCheckOutcome.success();
                } else {
                    return SdkResultChecker.ResultCheckOutcome.failure(
                        methodName + "方法返回值不符合预期，期望: " + expectedValue + ", 实际: " + returnValue
                    );
                }
            } catch (Exception e) {
                return SdkResultChecker.ResultCheckOutcome.failure(
                    "反射调用方法失败: " + e.getMessage()
                );
            }
        };
    }

    /**
     * 创建一个组合检查器，所有检查器都通过才算成功
     *
     * @param checkers 检查器列表
     * @param <T>      对象类型
     * @return         结果检查器
     */
    @SafeVarargs
    public static <T> SdkResultChecker<T> all(SdkResultChecker<T>... checkers) {
        return result -> {
            for (SdkResultChecker<T> checker : checkers) {
                SdkResultChecker.ResultCheckOutcome outcome = checker.check(result);
                if (!outcome.isSuccess()) {
                    return outcome;
                }
            }
            return SdkResultChecker.ResultCheckOutcome.success();
        };
    }

    /**
     * 创建一个组合检查器，任一检查器通过就算成功
     *
     * @param checkers 检查器列表
     * @param <T>      对象类型
     * @return         结果检查器
     */
    @SafeVarargs
    public static <T> SdkResultChecker<T> any(SdkResultChecker<T>... checkers) {
        return result -> {
            String errorMessage = null;

            for (SdkResultChecker<T> checker : checkers) {
                SdkResultChecker.ResultCheckOutcome outcome = checker.check(result);
                if (outcome.isSuccess()) {
                    return outcome;
                }
                errorMessage = outcome.getErrorMessage();
            }

            return SdkResultChecker.ResultCheckOutcome.failure(errorMessage);
        };
    }
}
