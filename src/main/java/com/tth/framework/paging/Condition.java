package com.tth.framework.paging;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "条件入参")
@Data
public class Condition {
    @Schema(description = "属性名")
    private String property;

    @Schema(description = "操作条件")
    private String operate;

    @Schema(description = "条件值")
    private Object value;


    public Condition() {
    }

    public Condition(String property, String operate, Object value) {
        this.property = property;
        this.operate = operate;
        this.value = value;
    }

    public Condition(String property, Object value) {
        this.property = property;
        this.operate = "=";
        this.value = value;
    }
}
