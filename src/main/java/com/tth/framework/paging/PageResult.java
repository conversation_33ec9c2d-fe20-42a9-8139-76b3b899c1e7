package com.tth.framework.paging;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Schema(description = "分页响应数据对象")
@NoArgsConstructor
@Data
public class PageResult<T> {
    @Schema(description = "当前页")
    private Integer pageNum;

    @Schema(description = "总页数")
    private Integer pageSize;

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "是否分页查询")
    private Boolean pageFlag = true;

    @Schema(description = "响应记录")
    private List<T> list;


    /**
     * 构造函数
     * 不分页
     *
     * @param list     实际列表
     */
    public PageResult(List<T> list) {
        this.pageNum = 1;
        this.pageSize = list != null ? list.size() : 0;
        this.list = list != null ? list : new ArrayList<>(); // 确保 list 不为 null
        this.total = list != null ? list.size() : 0L;
        this.pageFlag = false;
    }


    /**
     * 构造函数
     *
     * @param pageNum  查询的当前页
     * @param pageSize 每页行数
     * @param total    总记录数
     * @param list     实际列表
     */
    public PageResult(Integer pageNum, Integer pageSize, Long total, List<T> list) {
        this.pageNum = pageNum != null ? pageNum : 1; // 默认当前页为 1
        this.pageSize = pageSize != null ? pageSize : 10; // 默认每页行数为 10
        this.list = list != null ? list : new ArrayList<>(); // 确保 list 不为 null
        this.total = total;
    }


    /**
     * 构造函数
     *
     * @param pageNum  查询的当前页
     * @param pageSize 每页行数
     * @param list     实际列表
     */
    public PageResult(Integer pageNum, Integer pageSize, List<T> list) {
        this.pageNum = pageNum != null ? pageNum : 1; // 默认当前页为 1
        this.pageSize = pageSize != null ? pageSize : 10; // 默认每页行数为 10
        this.list = list != null ? list : new ArrayList<>(); // 确保 list 不为 null
        this.total = calculateTotal();
    }

    // 计算总行数
    private Long calculateTotal() {
        int totalRecords = list.size() + (pageSize * (pageNum - 1));
        if (list.size() >= pageSize) {
            totalRecords++; // 如果当前页的记录数量达到每页行数，增加总数
        }
        return Long.parseLong(totalRecords + "");
    }
}
