package com.tth.framework.paging;

import com.tth.framework.enums.DataAuth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "分页入参数")
@Data
public class Paging {

    @Schema(description = "是否分页查询")
    private Boolean pageFlag = true;

    @Schema(description = "当前页")
    private Integer pageNum = 1;

    @Schema(description = "查询记录数")
    private Integer pageSize = 10;

    @Schema(description = "是否查询总数")
    private Boolean countFlag = true;

    @Schema(description = "条件集")
    private List<Condition> conditions;

    @Schema(description = "排序集")
    private List<Order> orders;

    @Schema(description = "数据权限,数据字典：COMMON_DATA_AUTH")
    private DataAuth auth;
}
