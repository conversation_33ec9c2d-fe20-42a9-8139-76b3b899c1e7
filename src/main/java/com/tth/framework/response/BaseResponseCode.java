package com.tth.framework.response;

import lombok.Getter;

@Getter
public enum BaseResponseCode implements IResponseCode {

    SUCCESS(200000, "操作成功"),
    FAIL(200001, "操作失败"),
    RECORD_NOT_FOUND(200002, "数据不存在"),
    NULL(200003, "没有数据"),
    ;

    private final int code;
    private final String message;

    BaseResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
