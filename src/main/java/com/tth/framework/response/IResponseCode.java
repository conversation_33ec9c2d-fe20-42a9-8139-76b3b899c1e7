package com.tth.framework.response;

public interface IResponseCode {

    int getCode();
    String getMessage();

    /**
     * 获取完整错误码
     * 对于模块响应码，格式为: MODULE_CODE * 1000 + ERROR_CODE
     * 对于全局响应码，直接返回错误码
     *
     * 模块编号范围: 700-799
     * 模块错误码范围: 0-999
     * 模块完整错误码范围: 700000-799999
     *
     * 全局错误码范围: 100000-699999
     */
    default int getFullCode() {
        ModuleResponseCode annotation = this.getClass().getAnnotation(ModuleResponseCode.class);
        if (annotation != null) {
            // 模块响应码，使用模块编号计算完整错误码
            return annotation.module() * 1000 + getCode();
        } else {
            // 全局响应码，直接返回错误码
            return getCode();
        }
    }
}

