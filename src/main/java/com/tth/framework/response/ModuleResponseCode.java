package com.tth.framework.response;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModuleResponseCode {
    /**
     * 模块代码 (600-699)
     * 模块错误码必须在600-699范围内
     * 完整错误码格式为: MODULE_CODE * 1000 + EXCEPTION_CODE
     * 例如：模块编号600，错误码123，则完整错误码为600123
     */
    int module();

    /**
     * 模块描述
     */
    String desc() default "";
}