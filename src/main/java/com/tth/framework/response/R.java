package com.tth.framework.response;

import com.tth.framework.interceptor.PermissionInterceptor;
import lombok.Data;
import org.apache.logging.log4j.ThreadContext;

/**
 * 统一响应类
 * 用于封装API响应数据，包含状态码、消息、数据和请求ID
 *
 * @param <T> 响应数据类型
 */
@Data
public class R<T> {

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应状态码
     */
    private int code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 请求ID，用于跟踪和调试
     */
    private String requestId;

    /**
     * 使用IResponseCode构造响应对象
     *
     * @param code 错误码
     */
    public R(IResponseCode code) {
        this.code = code.getFullCode();
        this.message = code.getMessage();
        this.requestId = obtainCurrentRequestId();
    }

    /**
     * 使用IResponseCode和自定义消息构造响应对象
     *
     * @param code 错误码
     * @param message 消息
     */
    public R(IResponseCode code, String message) {
        this.code = code.getFullCode();
        this.message = message;
        this.requestId = obtainCurrentRequestId();
    }

    /**
     * 使用IResponseCode和自定义消息构造响应对象
     *
     * @param code 错误码
     * @param data 消息
     */
    public R(IResponseCode code, T data) {
        this.code = code.getFullCode();
        this.message = code.getMessage();
        this.data = data;
        this.requestId = obtainCurrentRequestId();
    }

    /**
     * 使用IResponseCode、自定义消息和数据构造响应对象
     *
     * @param code 错误码
     * @param message 消息
     * @param data 数据
     */
    public R(IResponseCode code, String message, T data) {
        this.code = code.getFullCode();
        this.message = message;
        this.data = data;
        this.requestId = obtainCurrentRequestId();
    }

    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 响应数据类型
     * @return 成功响应对象
     */
    public static <T> R<T> success() {
        return new R<>(BaseResponseCode.SUCCESS);
    }

    /**
     * 创建成功响应（带数据）
     *
     * @param <T> 响应数据类型
     * @param data 响应数据
     * @return 成功响应对象
     */
    public static <T> R<T> success(T data) {
        return new R<>(BaseResponseCode.SUCCESS, data);
    }

    /**
     * 创建成功响应（带自定义消息）
     * 消息将放在message字段中，data字段为null
     *
     * @param <T> 响应数据类型
     * @param message 成功消息
     * @return 成功响应对象
     */
    public static <T> R<T> successWithMessage(String message) {
        return new R<>(BaseResponseCode.SUCCESS, message);
    }

    /**
     * 创建失败响应（带消息）
     *
     * @param <T> 响应数据类型
     * @param message 失败消息
     * @return 失败响应对象
     */
    public static <T> R<T> fail(String message) {
        return new R<>(BaseResponseCode.FAIL, message);
    }

    public static <T> R<T> fail(T data) {
        return new R<>(BaseResponseCode.FAIL, data);
    }

    /**
     * 创建自定义响应（无数据）
     *
     * @param <T> 响应数据类型
     * @param code 响应码
     * @return 自定义响应对象
     */
    public static <T> R<T> custom(IResponseCode code) {
        return custom(code, code.getMessage());
    }

    /**
     * 创建自定义响应（无数据）
     *
     * @param <T> 响应数据类型
     * @param code 响应码
     * @param message 自定义消息
     * @return 自定义响应对象
     */
    public static <T> R<T> custom(IResponseCode code, String message) {
        return new R<>(code, message);
    }

    /**
     * 创建自定义响应（带数据）
     *
     * @param <T> 响应数据类型
     * @param code 响应码
     * @param message 自定义消息
     * @param data 响应数据
     * @return 自定义响应对象
     */
    public static <T> R<T> custom(IResponseCode code, String message, T data) {
        return new R<>(code, message, data);
    }

    /**
     * 获取当前请求ID
     * 如果线程上下文中没有请求ID，则返回“NO_REQUEST_ID”
     *
     * @return 当前请求ID
     */
    private String obtainCurrentRequestId() {
        String requestId = ThreadContext.get(PermissionInterceptor.REQUEST_ID);
        return requestId != null ? requestId : "NO_REQUEST_ID";
    }
}
