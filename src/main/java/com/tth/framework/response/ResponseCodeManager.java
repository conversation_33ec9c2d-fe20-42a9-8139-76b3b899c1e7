package com.tth.framework.response;

import com.tth.framework.exception.code.AuthExceptionCode;
import com.tth.framework.exception.code.PermissionExceptionCode;
import com.tth.framework.exception.code.SystemExceptionCode;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.code.ValidationExceptionCode;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class ResponseCodeManager {
    private static final Map<Integer, String> MODULE_MAP = new HashMap<>();

    @Resource
    private Environment environment;

    @PostConstruct
    public void init() {
        // 收集所有错误码，并检查是否有重复
        Set<Integer> allCodes = new HashSet<>();
        Map<Integer, String> allCodesMap = new HashMap<>();

        // 收集SystemExceptionCode中的错误码
        collectErrorCodes(SystemExceptionCode.class, allCodes, allCodesMap);

        // 收集ValidationExceptionCode中的错误码
        collectErrorCodes(ValidationExceptionCode.class, allCodes, allCodesMap);

        // 收集AuthExceptionCode中的错误码
        collectErrorCodes(AuthExceptionCode.class, allCodes, allCodesMap);

        // 收集PermissionExceptionCode中的错误码
        collectErrorCodes(PermissionExceptionCode.class, allCodes, allCodesMap);

        // 收集ThirdPartyExceptionCode中的错误码
        collectErrorCodes(ThirdPartyExceptionCode.class, allCodes, allCodesMap);

        log.info("Collected {} error codes from all exception code enums", allCodes.size());

        // RCodeEnum已经被移除，不再收集其错误码
        log.info("RCodeEnum has been removed, no error codes collected from it");

        // 扫描所有错误码枚举类
        Reflections reflections = new Reflections("com.tth");
        Set<Class<?>> errorCodeClasses = reflections.getTypesAnnotatedWith(ModuleResponseCode.class);
        log.info("Found {} module error code classes", errorCodeClasses.size());

        // 检查模块编号是否重复
        for (Class<?> clazz : errorCodeClasses) {
            ModuleResponseCode annotation = clazz.getAnnotation(ModuleResponseCode.class);
            int moduleCode = annotation.module();

            // 检查模块编号是否在指定范围内 (700-799)
            if (moduleCode < 700 || moduleCode > 799) {
                throw new IllegalStateException(
                        String.format("Invalid module code %d in %s. Module code must be between 700 and 799",
                                moduleCode, clazz.getName()));
            }

            // 检查模块编号是否与其他模块重复
            if (MODULE_MAP.containsKey(moduleCode)) {
                throw new IllegalStateException(
                        String.format("Duplicate module code %d found in %s and %s",
                                moduleCode, MODULE_MAP.get(moduleCode), clazz.getName()));
            }

            // 检查模块错误码是否与系统错误码冲突，以及模块内部是否有重复
            try {
                // 获取所有枚举常量
                Map<Integer, String> moduleInternalCodes = new HashMap<>(); // 用于检测模块内部错误码重复
                Map<Integer, String> moduleFullCodes = new HashMap<>(); // 用于检测完整错误码重复

                for (Object enumConstant : clazz.getEnumConstants()) {
                    IResponseCode errorCode = (IResponseCode) enumConstant;
                    int code = errorCode.getCode();
                    int fullCode = errorCode.getFullCode();
                    String enumName = ((Enum<?>) enumConstant).name();

                    // 检查错误码是否在指定范围内 (0-999)
                    if (code < 0 || code > 999) {
                        throw new IllegalStateException(
                                String.format("Invalid error code %d in %s.%s. Error code must be between 0 and 999",
                                        code, clazz.getName(), enumName));
                    }

                    // 检查模块内部错误码是否重复
                    if (moduleInternalCodes.containsKey(code)) {
                        throw new IllegalStateException(
                                String.format("Duplicate error code %d found in %s: %s and %s",
                                        code, clazz.getName(), moduleInternalCodes.get(code), enumName));
                    }
                    moduleInternalCodes.put(code, enumName);

                    // 检查完整错误码是否重复
                    if (moduleFullCodes.containsKey(fullCode)) {
                        throw new IllegalStateException(
                                String.format("Duplicate full error code %d found in %s: %s and %s",
                                        fullCode, clazz.getName(), moduleFullCodes.get(fullCode), enumName));
                    }
                    moduleFullCodes.put(fullCode, enumName);

                    // 检查是否与其他错误码冲突
                    if (allCodes.contains(fullCode)) {
                        throw new IllegalStateException(
                                String.format("Error code %d (%s) in %s conflicts with system error code",
                                        fullCode, enumName, clazz.getName()));
                    }
                }

                log.info("Module {} contains {} error codes", moduleCode, moduleInternalCodes.size());
            } catch (Exception e) {
                log.error("Failed to check error codes in {}: {}", clazz.getName(), e.getMessage());
                throw new IllegalStateException("Error checking module error codes", e);
            }

            MODULE_MAP.put(moduleCode, clazz.getName());
            log.info("Registered module {} with code {}: {}", moduleCode, annotation.desc(), clazz.getName());
        }

        log.info("Error code initialization completed. {} modules registered.", MODULE_MAP.size());

        // 只在local环境下输出错误码信息
        String[] activeProfiles = environment.getActiveProfiles();
        boolean isLocalEnv = false;
        for (String profile : activeProfiles) {
            if ("local".equals(profile)) {
                isLocalEnv = true;
                break;
            }
        }

        if (isLocalEnv) {
            log.info("\n====== Error Code Information ======\n{}", getErrorCodeInfo());
        }
    }

    // 已在init方法中输出错误码信息，不需要在应用启动后再次输出

    /**
     * 收集错误码并检查重复
     *
     * @param enumClass 错误码枚举类
     * @param allCodes 所有错误码集合
     * @param allCodesMap 错误码与枚举名称的映射
     */
    private <T extends Enum<T> & IResponseCode> void collectErrorCodes(Class<T> enumClass, Set<Integer> allCodes, Map<Integer, String> allCodesMap) {
        try {
            Map<Integer, String> enumCodesMap = new HashMap<>();
            for (T codeEnum : enumClass.getEnumConstants()) {
                int code = codeEnum.getFullCode();
                String enumName = codeEnum.name();
                String className = enumClass.getSimpleName();

                // 检查枚举内部是否有重复的错误码
                if (enumCodesMap.containsKey(code)) {
                    throw new IllegalStateException(
                            String.format("Duplicate error code %d found in %s: %s and %s",
                                    code, className, enumCodesMap.get(code), enumName));
                }
                enumCodesMap.put(code, enumName);

                // 检查是否与其他枚举类的错误码冲突
                if (allCodesMap.containsKey(code)) {
                    log.warn("Error code {} in {} ({}) conflicts with {} ({})",
                            code, className, enumName, allCodesMap.get(code).split(":")[0], allCodesMap.get(code).split(":")[1]);
                }

                allCodesMap.put(code, className + ":" + enumName);
                allCodes.add(code);
            }
            log.info("Collected {} error codes from {}", enumCodesMap.size(), enumClass.getSimpleName());
        } catch (Exception e) {
            log.error("Failed to collect error codes from {}: {}", enumClass.getSimpleName(), e.getMessage(), e);
        }
    }

    /**
     * 将错误码枚举类的错误码添加到字符串构建器中
     *
     * @param sb 字符串构建器
     * @param title 标题
     * @param enumClass 错误码枚举类
     */
    private static <T extends Enum<T> & IResponseCode> void appendErrorCodes(StringBuilder sb, String title, Class<T> enumClass) {
        sb.append(title).append(" (").append(enumClass.getSimpleName()).append("):\n");
        try {
            // 获取所有枚举常量并按照错误码从小到大排序
            T[] enumConstants = enumClass.getEnumConstants();
            Arrays.sort(enumConstants, Comparator.comparingInt(IResponseCode::getFullCode));

            for (T codeEnum : enumConstants) {
                sb.append(String.format("  %d: %s (%s)\n",
                        codeEnum.getFullCode(), codeEnum.getMessage(), codeEnum.name()));
            }
        } catch (Exception e) {
            sb.append("  Error getting error codes: ").append(e.getMessage()).append("\n");
        }
        sb.append("\n");
    }

    /**
     * 获取错误码信息
     */
    public static String getErrorCodeInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("Module Error Codes:\n\n");

        // 将模块按照模块编号从小到大排序
        List<Map.Entry<Integer, String>> sortedModules = new ArrayList<>(MODULE_MAP.entrySet());
        sortedModules.sort(Map.Entry.comparingByKey());

        for (Map.Entry<Integer, String> entry : sortedModules) {
            try {
                int code = entry.getKey();
                String className = entry.getValue();
                Class<?> clazz = Class.forName(className);
                ModuleResponseCode annotation = clazz.getAnnotation(ModuleResponseCode.class);
                sb.append(String.format("Module %d: %s (%s)\n",
                        code, annotation.desc(), className));

                // 获取所有错误码并按照错误码从小到大排序
                Object[] enumConstants = clazz.getEnumConstants();
                Arrays.sort(enumConstants, Comparator.comparingInt(o -> ((IResponseCode) o).getFullCode()));

                for (Object enumConstant : enumConstants) {
                    IResponseCode errorCode = (IResponseCode) enumConstant;
                    sb.append(String.format("  %d: %s (%s)\n",
                            errorCode.getFullCode(), errorCode.getMessage(), ((Enum<?>) enumConstant).name()));
                }
                sb.append("\n");
            } catch (ClassNotFoundException e) {
                // ignore
            }
        }
        return sb.toString();
    }
}
