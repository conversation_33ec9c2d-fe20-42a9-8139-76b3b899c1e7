package com.tth.framework.service;

import com.tth.common.constant.RedisKeyConstants;
import com.tth.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量文件URL服务
 * 用于批量获取文件URL，提升性能
 */
@Slf4j
@Service
public class BatchFileUrlService {

    /**
     * 批量获取文件URL
     *
     * @param fileIds 文件ID集合
     * @return 文件ID -> URL的映射
     */
    public Map<String, String> batchGetFileUrls(Set<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 构建Redis keys
            List<String> fileIdList = new ArrayList<>(fileIds);
            List<String> keys = fileIdList.stream()
                .map(id -> RedisKeyConstants.Business.OssFile.FILE_URL + id)
                .collect(Collectors.toList());

            // 批量查询Redis
            List<String> urls = RedisUtil.multiGet(keys);

            // 组装结果
            Map<String, String> result = new HashMap<>();
            for (int i = 0; i < fileIdList.size(); i++) {
                String fileId = fileIdList.get(i);
                String url = urls.get(i);
                if (url != null) {
                    result.put(fileId, url);
                }
            }

            log.debug("批量获取文件URL完成，查询{}个文件ID，获得{}个URL", fileIds.size(), result.size());
            return result;

        } catch (Exception e) {
            log.error("批量获取文件URL失败，文件ID数量：{}", fileIds.size(), e);
            return new HashMap<>();
        }
    }

    /**
     * 批量获取文件URL（支持逗号分隔的多文件ID）
     *
     * @param multiFileIds 多文件ID字符串集合（逗号分隔）
     * @return 多文件ID字符串 -> 多文件URL字符串的映射
     */
    public Map<String, String> batchGetMultiFileUrls(Set<String> multiFileIds) {
        if (multiFileIds == null || multiFileIds.isEmpty()) {
            return new HashMap<>();
        }

        // 收集所有单个文件ID
        Set<String> allSingleFileIds = new HashSet<>();
        for (String multiFileId : multiFileIds) {
            if (multiFileId != null && !multiFileId.trim().isEmpty()) {
                String[] ids = multiFileId.split(",");
                for (String id : ids) {
                    String trimmedId = id.trim();
                    if (!trimmedId.isEmpty()) {
                        allSingleFileIds.add(trimmedId);
                    }
                }
            }
        }

        // 批量获取单个文件URL
        Map<String, String> singleUrlMap = batchGetFileUrls(allSingleFileIds);

        // 组装多文件URL结果
        Map<String, String> result = new HashMap<>();
        for (String multiFileId : multiFileIds) {
            if (multiFileId == null || multiFileId.trim().isEmpty()) {
                result.put(multiFileId, multiFileId);
                continue;
            }

            String[] ids = multiFileId.split(",");
            List<String> urls = new ArrayList<>();
            
            for (String id : ids) {
                String trimmedId = id.trim();
                if (trimmedId.isEmpty()) {
                    urls.add(trimmedId);
                } else {
                    String url = singleUrlMap.get(trimmedId);
                    urls.add(url != null ? url : trimmedId);
                }
            }
            
            result.put(multiFileId, String.join(",", urls));
        }

        return result;
    }
}
