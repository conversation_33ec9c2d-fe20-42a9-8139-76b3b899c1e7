package com.tth.framework.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.tth.common.utils.RedisUtil;
import com.tth.framework.utils.SpringContextHolder;
import com.tth.modules.system.service.OssFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量文件URL服务（优化版）
 * 提供高性能的批量文件URL查询能力
 */
@Slf4j
@Service
public class BatchFileUrlServiceV2 {
    
    /**
     * 批量获取文件URL
     * 
     * @param fileIdExpireMap 文件ID和过期时间的映射 key: fileId:expireMinutes
     * @return 文件URL映射 key: fileId:expireMinutes, value: url
     */
    public Map<String, String> batchGetFileUrls(Map<String, Integer> fileIdExpireMap) {
        if (CollUtil.isEmpty(fileIdExpireMap)) {
            return new HashMap<>();
        }
        
        Map<String, String> result = new HashMap<>();
        
        try {
            // 1. 按过期时间分组
            Map<Integer, Set<Long>> groupedByExpire = groupByExpireTime(fileIdExpireMap);
            
            // 2. 分组批量查询
            OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
            
            for (Map.Entry<Integer, Set<Long>> entry : groupedByExpire.entrySet()) {
                int expireMinutes = entry.getKey();
                Set<Long> fileIds = entry.getValue();
                
                if (CollUtil.isNotEmpty(fileIds)) {
                    // 批量查询这组文件的URL
                    Map<Long, String> urlMap = ossFileService.batchGetFileUrls(fileIds, expireMinutes);
                    
                    // 转换为结果格式
                    for (Map.Entry<Long, String> urlEntry : urlMap.entrySet()) {
                        String key = urlEntry.getKey() + ":" + expireMinutes;
                        result.put(key, urlEntry.getValue());
                    }
                }
            }
            
            log.debug("批量获取文件URL完成，查询{}组文件，返回{}个URL", 
                groupedByExpire.size(), result.size());
            
        } catch (Exception e) {
            log.error("批量获取文件URL失败", e);
        }
        
        return result;
    }
    
    /**
     * 批量获取多文件URL（逗号分隔的文件ID）
     */
    public Map<String, String> batchGetMultiFileUrls(Map<String, Integer> multiFileMap) {
        if (CollUtil.isEmpty(multiFileMap)) {
            return new HashMap<>();
        }
        
        Map<String, String> result = new HashMap<>();
        
        try {
            // 1. 解析多文件ID字符串，收集所有单个文件ID
            Map<String, Integer> allSingleFileIds = new HashMap<>();
            
            for (Map.Entry<String, Integer> entry : multiFileMap.entrySet()) {
                String multiFileIds = entry.getKey();
                int expireMinutes = entry.getValue();
                
                if (StrUtil.isNotBlank(multiFileIds)) {
                    String[] idArray = multiFileIds.split(",");
                    for (String idStr : idArray) {
                        if (StrUtil.isNotBlank(idStr.trim())) {
                            String key = idStr.trim() + ":" + expireMinutes;
                            allSingleFileIds.put(key, expireMinutes);
                        }
                    }
                }
            }
            
            // 2. 批量查询所有单个文件URL
            Map<String, String> singleUrlMap = batchGetFileUrls(allSingleFileIds);
            
            // 3. 组装多文件URL结果
            for (Map.Entry<String, Integer> entry : multiFileMap.entrySet()) {
                String multiFileIds = entry.getKey();
                int expireMinutes = entry.getValue();
                
                String multiFileUrls = buildMultiFileUrls(multiFileIds, expireMinutes, singleUrlMap);
                result.put(multiFileIds, multiFileUrls);
            }
            
            log.debug("批量获取多文件URL完成，处理{}个多文件字段", multiFileMap.size());
            
        } catch (Exception e) {
            log.error("批量获取多文件URL失败", e);
        }
        
        return result;
    }
    
    /**
     * 按过期时间分组文件ID
     */
    private Map<Integer, Set<Long>> groupByExpireTime(Map<String, Integer> fileIdExpireMap) {
        Map<Integer, Set<Long>> grouped = new HashMap<>();
        
        for (Map.Entry<String, Integer> entry : fileIdExpireMap.entrySet()) {
            String key = entry.getKey();
            int expireMinutes = entry.getValue();
            
            // 解析文件ID（key格式：fileId:expireMinutes）
            String[] parts = key.split(":");
            if (parts.length >= 1) {
                try {
                    Long fileId = Long.parseLong(parts[0]);
                    grouped.computeIfAbsent(expireMinutes, k -> new HashSet<>()).add(fileId);
                } catch (NumberFormatException e) {
                    log.warn("无效的文件ID: {}", parts[0]);
                }
            }
        }
        
        return grouped;
    }
    
    /**
     * 构建多文件URL字符串
     */
    private String buildMultiFileUrls(String multiFileIds, int expireMinutes, Map<String, String> singleUrlMap) {
        if (StrUtil.isBlank(multiFileIds)) {
            return null;
        }
        
        List<String> urls = new ArrayList<>();
        String[] idArray = multiFileIds.split(",");
        
        for (String idStr : idArray) {
            if (StrUtil.isNotBlank(idStr.trim())) {
                String key = idStr.trim() + ":" + expireMinutes;
                String url = singleUrlMap.get(key);
                if (url != null) {
                    urls.add(url);
                }
            }
        }
        
        // 返回JSON数组格式或者逗号分隔格式
        return String.join(",", urls);
    }
    
    /**
     * 预热缓存：预先加载热点文件的URL
     */
    public void warmupCache(Set<Long> hotFileIds, int expireMinutes) {
        if (CollUtil.isEmpty(hotFileIds)) {
            return;
        }
        
        try {
            OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
            Map<Long, String> urlMap = ossFileService.batchGetFileUrls(hotFileIds, expireMinutes);
            
            log.info("预热文件URL缓存完成，预热{}个文件", urlMap.size());
            
        } catch (Exception e) {
            log.error("预热文件URL缓存失败", e);
        }
    }
}
