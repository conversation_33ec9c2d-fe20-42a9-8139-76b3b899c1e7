package com.tth.framework.service;

import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.annotation.MarkMultiFileUrl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 文件URL字段缓存
 * 缓存类的反射信息，避免重复反射操作
 */
@Slf4j
@Component
public class FileUrlFieldCache {

    /**
     * 类 -> 文件URL字段列表的缓存
     */
    private static final ConcurrentMap<Class<?>, List<FileUrlFieldInfo>> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取类中所有标注了文件URL注解的字段
     *
     * @param clazz 目标类
     * @return 文件URL字段信息列表
     */
    public List<FileUrlFieldInfo> getFileUrlFields(Class<?> clazz) {
        return FIELD_CACHE.computeIfAbsent(clazz, this::findFileUrlFields);
    }

    /**
     * 查找类中的文件URL字段
     */
    private List<FileUrlFieldInfo> findFileUrlFields(Class<?> clazz) {
        List<FileUrlFieldInfo> fields = new ArrayList<>();

        // 遍历所有字段（包括父类字段）
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                FileUrlFieldInfo fieldInfo = createFieldInfo(field);
                if (fieldInfo != null) {
                    fields.add(fieldInfo);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        log.debug("缓存类{}的文件URL字段，共{}个字段", clazz.getSimpleName(), fields.size());
        return fields;
    }

    /**
     * 创建字段信息
     */
    private FileUrlFieldInfo createFieldInfo(Field field) {
        MarkFileUrl markFileUrl = field.getAnnotation(MarkFileUrl.class);
        MarkMultiFileUrl markMultiFileUrl = field.getAnnotation(MarkMultiFileUrl.class);

        if (markFileUrl != null) {
            return new FileUrlFieldInfo(field, false, markFileUrl.expireMinutes());
        } else if (markMultiFileUrl != null) {
            return new FileUrlFieldInfo(field, true, markMultiFileUrl.expireMinutes());
        }

        return null;
    }

    /**
     * 文件URL字段信息
     */
    public static class FileUrlFieldInfo {
        private final Field field;
        private final boolean isMultiFile;
        private final int expireMinutes;

        public FileUrlFieldInfo(Field field, boolean isMultiFile, int expireMinutes) {
            this.field = field;
            this.isMultiFile = isMultiFile;
            this.expireMinutes = expireMinutes;
            // 设置字段可访问
            this.field.setAccessible(true);
        }

        public Field getField() {
            return field;
        }

        public boolean isMultiFile() {
            return isMultiFile;
        }

        public int getExpireMinutes() {
            return expireMinutes;
        }

        /**
         * 获取字段值
         */
        public String getFieldValue(Object obj) {
            try {
                Object value = field.get(obj);
                return value != null ? value.toString() : null;
            } catch (IllegalAccessException e) {
                log.error("获取字段值失败，字段：{}，对象：{}", field.getName(), obj.getClass().getSimpleName(), e);
                return null;
            }
        }

        /**
         * 设置字段值
         */
        public void setFieldValue(Object obj, String value) {
            try {
                field.set(obj, value);
            } catch (IllegalAccessException e) {
                log.error("设置字段值失败，字段：{}，对象：{}，值：{}", field.getName(), obj.getClass().getSimpleName(), value, e);
            }
        }
    }
}
