package com.tth.framework.service;

import cn.hutool.core.util.ReflectUtil;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.annotation.MarkMultiFileUrl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件URL字段缓存服务（优化版）
 * 缓存类的文件URL字段信息，避免重复反射
 */
@Slf4j
@Component
public class FileUrlFieldCacheV2 {
    
    /**
     * 类字段缓存：Class -> List<FileUrlFieldInfo>
     */
    private final Map<Class<?>, List<FileUrlFieldInfo>> classFieldCache = new ConcurrentHashMap<>();
    
    /**
     * 获取类的所有文件URL字段信息
     */
    public List<FileUrlFieldInfo> getFileUrlFields(Class<?> clazz) {
        return classFieldCache.computeIfAbsent(clazz, this::scanFileUrlFields);
    }
    
    /**
     * 扫描类的文件URL字段
     */
    private List<FileUrlFieldInfo> scanFileUrlFields(Class<?> clazz) {
        List<FileUrlFieldInfo> fieldInfos = new ArrayList<>();
        
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            // 检查单文件注解
            MarkFileUrl singleAnnotation = field.getAnnotation(MarkFileUrl.class);
            if (singleAnnotation != null) {
                fieldInfos.add(new FileUrlFieldInfo(
                    field, 
                    singleAnnotation.expireMinutes(),
                    singleAnnotation.nullWhenNotFound(),
                    false, // 不是多文件
                    ","
                ));
                continue;
            }
            
            // 检查多文件注解
            MarkMultiFileUrl multiAnnotation = field.getAnnotation(MarkMultiFileUrl.class);
            if (multiAnnotation != null) {
                fieldInfos.add(new FileUrlFieldInfo(
                    field,
                    multiAnnotation.expireMinutes(),
                    multiAnnotation.nullWhenNotFound(),
                    true, // 是多文件
                    multiAnnotation.separator()
                ));
            }
        }
        
        log.debug("扫描类{}的文件URL字段，找到{}个字段", clazz.getSimpleName(), fieldInfos.size());
        return fieldInfos;
    }
    
    /**
     * 文件URL字段信息
     */
    @Data
    public static class FileUrlFieldInfo {
        private final Field field;
        private final int expireMinutes;
        private final boolean nullWhenNotFound;
        private final boolean isMultiFile;
        private final String separator;
        
        public FileUrlFieldInfo(Field field, int expireMinutes, boolean nullWhenNotFound, 
                               boolean isMultiFile, String separator) {
            this.field = field;
            this.expireMinutes = expireMinutes;
            this.nullWhenNotFound = nullWhenNotFound;
            this.isMultiFile = isMultiFile;
            this.separator = separator;
            
            // 设置字段可访问
            field.setAccessible(true);
        }
        
        /**
         * 获取字段值（文件ID）
         */
        public String getFieldValue(Object obj) {
            try {
                Object value = field.get(obj);
                return value != null ? value.toString() : null;
            } catch (Exception e) {
                log.error("获取字段值失败: {}.{}", obj.getClass().getSimpleName(), field.getName(), e);
                return null;
            }
        }
        
        /**
         * 设置字段值（文件URL）
         */
        public void setFieldValue(Object obj, String url) {
            try {
                if (isMultiFile) {
                    // 多文件字段：设置为JSON数组或保持原格式
                    field.set(obj, url);
                } else {
                    // 单文件字段：根据原字段类型设置
                    if (field.getType() == Long.class || field.getType() == long.class) {
                        // 如果原字段是Long类型，需要特殊处理
                        // 可以考虑添加一个新的URL字段，或者改变字段类型
                        log.warn("尝试将URL设置到Long类型字段: {}.{}", obj.getClass().getSimpleName(), field.getName());
                    } else {
                        field.set(obj, url);
                    }
                }
            } catch (Exception e) {
                log.error("设置字段值失败: {}.{}", obj.getClass().getSimpleName(), field.getName(), e);
            }
        }
        
        /**
         * 生成缓存键
         */
        public String getCacheKey(String fileId) {
            return fileId + ":" + expireMinutes;
        }
    }
}
