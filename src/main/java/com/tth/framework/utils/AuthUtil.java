package com.tth.framework.utils;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.tth.framework.exception.code.AuthExceptionCode;
import com.tth.framework.exception.code.PermissionExceptionCode;
import com.tth.framework.exception.core.AuthException;
import com.tth.modules.user.enums.field.UserTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 权限工具类
 * 用于封装常用的权限检查方法
 */
@Slf4j
public class AuthUtil {

    private static final String USER_ID = "UserId";
    private static final String USER_TYPE = "UserType";

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     * @throws AuthException 如果用户未登录
     */
    public static Long getUserId() {
        // 优先从线程上下文中获取
        String userId = ThreadContext.get(USER_ID);
        if (StringUtils.hasText(userId)) {
            return Long.parseLong(userId);
        }

        // 如果线程上下文中没有，则从Sa-Token中获取
        if (StpUtil.isLogin()) {
            Object loginId = StpUtil.getLoginId();
            if (loginId != null) {
                // 存入线程上下文
                ThreadContext.put(USER_ID, loginId.toString());
                return Long.parseLong(loginId.toString());
            }
        }

        //获取不到，没有登录，返回Null
        return 0L;
    }

    /**
     * 获取当前登录用户类型
     *
     * @return 用户类型枚举
     * @throws AuthException 如果用户未登录或用户类型不存在
     */
    public static UserTypeEnum getUserType() {
        // 优先从线程上下文中获取
        String userTypeStr = ThreadContext.get(USER_TYPE);
        if (StringUtils.hasText(userTypeStr)) {
            try {
                return UserTypeEnum.valueOf(userTypeStr);
            } catch (IllegalArgumentException e) {
                // 如果不是枚举名称，尝试解析为整数值
                try {
                    int value = Integer.parseInt(userTypeStr);
                    for (UserTypeEnum type : UserTypeEnum.values()) {
                        if (type.getValue() == value) {
                            return type;
                        }
                    }
                } catch (NumberFormatException ignored) {
                    // 忽略解析异常
                }
            }
        }

        // 如果线程上下文中没有，则从Sa-Token中获取
        if (StpUtil.isLogin()) {
            SaSession session = StpUtil.getSession();
            Object userType = session.get("userType");
            if (userType != null) {
                // 存入线程上下文
                ThreadContext.put(USER_TYPE, userType.toString());

                // 尝试转换为枚举
                if (userType instanceof UserTypeEnum) {
                    return (UserTypeEnum) userType;
                } else if (userType instanceof String) {
                    try {
                        return UserTypeEnum.valueOf((String) userType);
                    } catch (IllegalArgumentException e) {
                        // 如果不是枚举名称，尝试解析为整数值
                        try {
                            int value = Integer.parseInt((String) userType);
                            for (UserTypeEnum type : UserTypeEnum.values()) {
                                if (type.getValue() == value) {
                                    return type;
                                }
                            }
                        } catch (NumberFormatException ignored) {
                            // 忽略解析异常
                        }
                    }
                } else if (userType instanceof Number) {
                    int value = ((Number) userType).intValue();
                    for (UserTypeEnum type : UserTypeEnum.values()) {
                        if (type.getValue() == value) {
                            return type;
                        }
                    }
                }
            }
        }

        throw new AuthException(AuthExceptionCode.UNAUTHORIZED);
    }

    /**
     * 检查当前用户是否有指定权限
     *
     * @param permission 权限标识
     * @return 是否有权限
     */
    public static boolean hasPermission(String permission) {
        return StpUtil.isLogin() && StpUtil.hasPermission(permission);
    }

    /**
     * 检查当前用户是否有指定角色
     *
     * @param role 角色标识
     * @return 是否有角色
     */
    public static boolean hasRole(String role) {
        return StpUtil.isLogin() && StpUtil.hasRole(role);
    }

    /**
     * 检查当前用户是否有指定权限，如果没有则抛出异常
     *
     * @param permission 权限标识
     * @throws AuthException 如果没有权限
     */
    public static void checkPermission(String permission) {
        if (!hasPermission(permission)) {
            throw new AuthException(PermissionExceptionCode.FORBIDDEN);
        }
    }

    /**
     * 检查当前用户是否有指定角色，如果没有则抛出异常
     *
     * @param role 角色标识
     * @throws AuthException 如果没有角色
     */
    public static void checkRole(String role) {
        if (!hasRole(role)) {
            throw new AuthException(PermissionExceptionCode.ROLE_REQUIRED);
        }
    }

    /**
     * 获取当前用户的所有权限
     *
     * @return 权限列表
     */
    public static List<String> getPermissionList() {
        return StpUtil.getPermissionList();
    }

    /**
     * 获取当前用户的所有角色
     *
     * @return 角色列表
     */
    public static List<String> getRoleList() {
        return StpUtil.getRoleList();
    }

    /**
     * 清理线程上下文中的用户信息
     */
    public static void clearThreadSession() {
        ThreadContext.remove(USER_ID);
        ThreadContext.remove(USER_TYPE);
    }
}
