package com.tth.framework.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.tth.framework.interceptor.PermissionInterceptor;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP工具类
 * 用于获取客户端IP地址
 */
@Slf4j
public class IpUtil {

    /**
     * 未知IP地址标识
     */
    private static final String UNKNOWN = "unknown";

    /**
     * 本地IP地址
     */
    private static final String LOCALHOST_IP = "127.0.0.1";

    /**
     * 本地IP地址完整形式
     */
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 多IP分隔符
     */
    private static final String IP_SEPARATOR = ",";

    /**
     * IP地址查询接口URL
     */
    private static final String IP_URL = "https://whois.pconline.com.cn/ipJson.jsp";

    /**
     * 未知地址
     */
    private static final String UNKNOWN_LOCATION = "未知地址";

    /**
     * 获取客户端IP地址
     * 从请求头中获取客户端IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        // 直接从请求头中获取IP，不依赖线程上下文
        String ip;

        // 从请求头中获取IP
        ip = request.getHeader("X-Forwarded-For");
        if (!isValidIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (!isValidIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (!isValidIp(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (!isValidIp(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (!isValidIp(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (!isValidIp(ip)) {
            ip = request.getRemoteAddr();
            // 如果是本地访问，则根据网卡获取本机真实IP
            if (LOCALHOST_IP.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
                try {
                    InetAddress inetAddress = InetAddress.getLocalHost();
                    ip = inetAddress.getHostAddress();
                } catch (UnknownHostException e) {
                    // 忽略异常，使用默认IP
                }
            }
        }

        // 多个代理的情况，第一个IP为客户端真实IP
        if (StringUtils.hasText(ip) && ip.contains(IP_SEPARATOR)) {
            ip = ip.substring(0, ip.indexOf(IP_SEPARATOR)).trim();
        }

        return ip;
    }

    /**
     * 判断IP是否有效
     *
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip);
    }

    /**
     * 获取当前请求的客户端IP
     * 从线程上下文中获取，如果没有则返回未知IP
     *
     * @return 客户端IP地址
     */
    public static String getCurrentIp() {
        String ip = ThreadContext.get(PermissionInterceptor.CURRENT_IP);
        return StringUtils.hasText(ip) ? ip : UNKNOWN;
    }

    /**
     * 判断IP是否是内网IP
     * 包含了所有常见的内网IP地址范围
     *
     * @param ip IP地址
     * @return 是否是内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }

        // 判断是否是本地回环地址 127.x.x.x
        if (ip.startsWith("127.")) {
            return true;
        }

        // 判断是否是IPv6本地地址
        if (ip.equals("::1") || ip.startsWith("fe80:") || ip.startsWith("fc00:") || ip.startsWith("fd")) {
            return true;
        }

        try {
            byte[] addr = textToNumericFormatV4(ip);
            if (addr == null) {
                return false;
            }

            // 将byte转换为无符号整数，解决byte类型溢出问题
            int b0 = addr[0] & 0xFF;
            int b1 = addr[1] & 0xFF;
            int b2 = addr[2] & 0xFF;

            // 判断是否是保留地址
            if (b0 == 0) {
                return true; // 0.0.0.0/8
            }

            // A类私有地址：10.0.0.0 - **************
            if (b0 == 10) {
                return true;
            }

            // B类私有地址：********** - **************
            if (b0 == 172 && (b1 >= 16 && b1 <= 31)) {
                return true;
            }

            // C类私有地址：*********** - ***************
            if (b0 == 192 && b1 == 168) {
                return true;
            }

            // 链路本地地址：*********** - ***************
            if (b0 == 169 && b1 == 254) {
                return true;
            }

            // CGNAT地址：100.64.0.0 - 100.127.255.255
            if (b0 == 100 && (b1 >= 64 && b1 <= 127)) {
                return true;
            }

            // 其他保留地址 - 分开判断以提高可读性
            // 192.0.0.0/24
            if (b0 == 192 && b1 == 0 && b2 == 0) {
                return true;
            }

            // 192.0.2.0/24
            if (b0 == 192 && b1 == 0 && b2 == 2) {
                return true;
            }

            // 198.18.0.0/15
            if (b0 == 198 && b1 >= 18 && b1 <= 19) {
                return true;
            }

            // 198.51.100.0/24
            if (b0 == 198 && b1 == 51 && b2 == 100) {
                return true;
            }

            // ***********/24
            if (b0 == 203 && b1 == 0 && b2 == 113) {
                return true;
            }

            // *********/4 - 多播地址
            if (b0 >= 224 && b0 <= 239) {
                return true;
            }

            // 240.0.0.0/4 - 保留地址
            if (b0 >= 240) {
                return true;
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将IPv4地址转换为字节数组
     *
     * @param text IPv4地址
     * @return 字节数组
     */
    private static byte[] textToNumericFormatV4(String text) {
        if (!StringUtils.hasText(text)) {
            return null;
        }

        byte[] bytes = new byte[4];
        String[] elements = text.split("\\.", -1);
        if (elements.length != 4) {
            return null;
        }

        try {
            for (int i = 0; i < elements.length; i++) {
                int value = Integer.parseInt(elements[i]);
                if (value < 0 || value > 255) {
                    return null;
                }
                bytes[i] = (byte) value;
            }
        } catch (NumberFormatException e) {
            return null;
        }

        return bytes;
    }

    /**
     * 根据IP地址获取真实地址
     *
     * @param ip IP地址
     * @return 真实地址
     */
    public static String getRealAddressByIP(String ip) {
        // 内网不查询
        if (isInternalIp(ip)) {
            return "内网IP";
        }

        // 使用Hutool的HttpUtil发送请求
        // 构建请求参数
        String url = IP_URL + "?ip=" + ip + "&json=true";
        log.info("请求URL：{}", url);
        try {
            // 只添加User-Agent头信息，模拟浏览器
            // 发送GET请求
            // 使用try-with-resources确保资源正确关闭
            String resStr;
            try (HttpResponse response = HttpUtil.createGet(url).timeout(5000).execute()) { // 设置超时时间为5秒
                resStr = response.body();
            }

            if (StrUtil.isEmpty(resStr)) {
                log.warn("获取地理位置异常，url：{}", url);
                return UNKNOWN_LOCATION;
            }

            JSONObject jsonObject = JSONUtil.toBean(resStr, JSONObject.class);
            String region = jsonObject.getStr("pro");
            String city = jsonObject.getStr("city");
            String isp = StrUtil.split(jsonObject.getStr("addr"), " ").get(1);
            return String.format("%s %s，%s", region, city, isp);
        } catch (Exception e) {
            log.warn("获取地理位置异常：{}", e.getMessage(), e);
            // 记录详细的请求信息以便调试
            log.debug("请求URL：{}，异常类型：{}", url, e.getClass().getName());
        }
        return UNKNOWN_LOCATION;
    }
}
