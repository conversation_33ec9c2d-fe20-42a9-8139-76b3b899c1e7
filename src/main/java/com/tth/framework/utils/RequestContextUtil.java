package com.tth.framework.utils;

import com.tth.framework.interceptor.PermissionInterceptor;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.util.StringUtils;

/**
 * 请求上下文工具类
 * 用于获取当前请求的相关信息
 */
public class RequestContextUtil {

    /**
     * 获取当前请求ID
     *
     * @return 请求ID，如果不存在则返回null
     */
    public static String getRequestId() {
        return ThreadContext.get(PermissionInterceptor.REQUEST_ID);
    }

    /**
     * 获取当前请求的客户端IP
     * 直接从线程上下文中获取
     *
     * @return 客户端IP，如果不存在则返回null
     */
    public static String getClientIp() {
        return ThreadContext.get(PermissionInterceptor.CURRENT_IP);
    }

    /**
     * 判断当前请求的客户端IP是否是内网IP
     *
     * @return 是否是内网IP
     */
    public static boolean isInternalIp() {
        String ip = getClientIp();
        if (ip == null) {
            return false;
        }
        return IpUtil.isInternalIp(ip);
    }

    /**
     * 获取当前请求的开始时间
     *
     * @return 开始时间的毫秒值，如果不存在则返回-1
     */
    public static long getRequestStartTime() {
        String startTimeStr = ThreadContext.get(PermissionInterceptor.REQUEST_START_TIME);
        if (startTimeStr != null) {
            try {
                return Long.parseLong(startTimeStr);
            } catch (NumberFormatException e) {
                return -1;
            }
        }
        return -1;
    }

    /**
     * 计算当前请求已经执行的时间
     *
     * @return 已执行时间的毫秒值，如果无法计算则返回-1
     */
    public static long getElapsedTime() {
        long startTime = getRequestStartTime();
        if (startTime > 0) {
            return System.currentTimeMillis() - startTime;
        }
        return -1;
    }

    /**
     * 获取当前请求的用户ID
     * 注意：这是一个便捷方法，实际上是调用AuthUtil.getUserId()
     *
     * @return 用户ID
     */
    public static Long getUserId() {
        return AuthUtil.getUserId();
    }

    /**
     * 将请求ID添加到日志消息前缀
     *
     * @param message 原始日志消息
     * @return 添加了请求ID前缀的日志消息
     */
    public static String addRequestIdPrefix(String message) {
        String requestId = getRequestId();
        if (requestId != null) {
            return "[" + requestId + "] " + message;
        }
        return message;
    }
}
