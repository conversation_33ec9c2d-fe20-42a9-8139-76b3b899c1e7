package com.tth.framework.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring上下文持有者
 * 用于在非Spring管理的类中获取Bean
 */
@Slf4j
@Component
public class SpringContextHolder implements ApplicationContextAware {

    /**
     * Spring应用上下文
     */
    private static ApplicationContext applicationContext;

    /**
     * 实现ApplicationContextAware接口的回调方法
     * Spring容器启动时会自动调用该方法，将ApplicationContext注入
     *
     * @param applicationContext Spring应用上下文
     * @throws BeansException 如果获取ApplicationContext失败
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextHolder.applicationContext = applicationContext;
        log.info("SpringContextHolder初始化成功，ApplicationContext已注入");
    }

    /**
     * 获取ApplicationContext
     *
     * @return ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        assertApplicationContext();
        return applicationContext;
    }

    /**
     * 根据Bean名称获取Bean
     *
     * @param beanName Bean名称
     * @return Bean实例
     */
    public static Object getBean(String beanName) {
        assertApplicationContext();
        return applicationContext.getBean(beanName);
    }

    /**
     * 根据Bean类型获取Bean
     *
     * @param beanClass Bean类型
     * @param <T>       Bean类型泛型
     * @return Bean实例
     */
    public static <T> T getBean(Class<T> beanClass) {
        assertApplicationContext();
        return applicationContext.getBean(beanClass);
    }

    /**
     * 根据Bean名称和类型获取Bean
     *
     * @param beanName  Bean名称
     * @param beanClass Bean类型
     * @param <T>       Bean类型泛型
     * @return Bean实例
     */
    public static <T> T getBean(String beanName, Class<T> beanClass) {
        assertApplicationContext();
        return applicationContext.getBean(beanName, beanClass);
    }

    /**
     * 断言ApplicationContext不为空
     * 如果为空则抛出异常
     */
    private static void assertApplicationContext() {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext未初始化，请确保SpringContextHolder已正确配置");
        }
    }
}
