package com.tth.framework.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tth.framework.enums.OperateEnum;
import com.tth.framework.exception.code.SystemExceptionCode;
import com.tth.framework.exception.core.SystemException;
import com.tth.framework.paging.Condition;
import com.tth.framework.paging.Order;
import com.tth.framework.paging.Paging;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
public class Tool {


    /**
     * 非like转换
     */
    private static final Pattern ESCAPE_STRING_PATTERN = Pattern.compile("['#$%\"\\\\]", Pattern.CASE_INSENSITIVE);

    /**
     * like 转换
     */
    private static final Pattern ESCAPE_STRING_PATTERN_LIKE = Pattern.compile("['%\\\\_]", Pattern.CASE_INSENSITIVE);

    public static String decodeNickName(String nickName) {
        return new String(nickName.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
    }


    /**
     * 获取查询表达式
     *
     * @param paging 数据
     * @param <T>    返回结果
     * @return 异常
     */
    public static <T> QueryWrapper<T> getLambdaQueryWrapper(Paging paging) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        // 数据权限
        if (paging.getAuth() != null) {
            switch (paging.getAuth()) {
                case ALL -> {
                    // 全部数据
                }
                case MY -> {
                    // 个人数据
                    if (StpUtil.isLogin()) {
                        Condition myCondition = new Condition(
                                "createdBy",
                                OperateEnum.EQ.getOperate(),
                                1L);
                        if (paging.getConditions() == null || paging.getConditions().isEmpty()) {
                            paging.setConditions(new ArrayList<>());
                        }
                        paging.getConditions().add(myCondition);
                    }
                }
            }
        }
        // 条件查询
        if (paging.getConditions() != null && !paging.getConditions().isEmpty()) {
            for (Condition condition : paging.getConditions()) {
                OperateEnum commonOperateEnum = OperateEnum.GetOperateEnum(condition.getOperate());
                if (commonOperateEnum != null) {
                    switch (commonOperateEnum) {
                        case EQ -> {
                            queryWrapper.eq(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case NE -> {
                            queryWrapper.ne(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case GT -> {
                            queryWrapper.gt(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case LT -> {
                            queryWrapper.lt(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case GE -> {
                            queryWrapper.ge(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case LE -> {
                            queryWrapper.le(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case LIKE -> {
                            queryWrapper.like(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case IN -> {
                            queryWrapper.in(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                        case NOT_IN -> {
                            queryWrapper.notIn(CamelToUnderscore(condition.getProperty()), condition.getValue());
                        }
                    }
                }
            }
        }
        // 排序
        if (paging.getOrders() != null && !paging.getOrders().isEmpty()) {
            for (Order item : paging.getOrders()) {
                if (StringUtils.isBlank(item.getProperty())) {
                    continue;
                }
                String columnName = Tool.CamelToUnderscore(item.getProperty());
                switch (item.getDirection()) {
                    case ASC -> {
                        queryWrapper.orderByAsc(columnName);
                    }
                    case DESC -> {
                        queryWrapper.orderByDesc(columnName);
                    }
                }
            }
        }
        return queryWrapper;
    }

    /**
     * 将驼峰命名法的字符串转换为下划线命名法
     *
     * @param camelCaseStr 驼峰命名法的字符串
     * @return 下划线命名法的字符串
     */
    public static String CamelToUnderscore(String camelCaseStr) {
        if (camelCaseStr == null || camelCaseStr.isEmpty()) {
            return camelCaseStr;
        }
        return camelCaseStr.replaceAll("([a-z])([A-Z]+)", "$1_$2").toLowerCase();
    }


    /**
     * 数据库操作符转义
     *
     * @param target   目标字符串
     * @param operator 转义
     */
    public static String EscapeString(String target, String operator) {
        // 1. 操作符判空
        if (StringUtils.isBlank(operator)) {
            throw new SystemException(SystemExceptionCode.INTERNAL_SERVER_ERROR, "【通用工具】转义方法需接收Filter操作符号");
        }
        // 2. 匹配like操作符
        Matcher match;
        if (operator.equalsIgnoreCase(OperateEnum.LIKE.getOperate())) {
            target = target.replace("\\", "\\\\");
            match = ESCAPE_STRING_PATTERN_LIKE.matcher(target);
        } else {
            match = ESCAPE_STRING_PATTERN.matcher(target);
        }
        // 3. 匹配并设置结果Map
        String temp;
        Map<String, String> resultMap = new HashMap<>(16);
        while (match.find()) {
            temp = match.group();
            resultMap.put(temp, null);
        }
        Set<String> keySet = resultMap.keySet();
        for (String item : keySet) {
            temp = item;
            target = target.replace(temp, "\\" + temp);
        }
        // 4. 返回结果Map
        return target;
    }


    /**
     * 将下划线命名法的字符串转换为驼峰命名法
     *
     * @param underscoreStr 下划线命名法的字符串
     * @return 驼峰命名法的字符串
     */
    public static String UnderscoreToCamel(String underscoreStr) {
        if (underscoreStr == null || underscoreStr.isEmpty()) {
            return underscoreStr;
        }
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (char ch : underscoreStr.toCharArray()) {
            if (ch == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(ch));
                    nextUpperCase = false;
                } else {
                    result.append(ch);
                }
            }
        }
        return result.toString();
    }

    /**
     * 首字母转大写
     *
     * @param target 目标
     * @return 返回字符串
     */
    public static String toUpperCaseFirstOne(String target) {
        if (Character.isUpperCase(target.charAt(0))) {
            return target;
        }
        return Character.toUpperCase(target.charAt(0)) + target.substring(1);
    }


    // 手机号码正则表达式
    private static final String PHONE_REGEX = "^\\+?[1-9]\\d{1,14}$"; // 适用于国际格式
    // 邮箱正则表达式
    private static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";


    /**
     * 是否是手机号
     *
     * @param target 目标
     * @return 返回布尔值
     */
    public static boolean isPhoneNumber(String target) {
        Pattern pattern = Pattern.compile(PHONE_REGEX);
        Matcher matcher = pattern.matcher(target);
        return matcher.matches();
    }


    /**
     * 是否是邮箱
     *
     * @param target 目标
     * @return 返回布尔值
     */
    public static boolean isEmail(String target) {
        Pattern pattern = Pattern.compile(EMAIL_REGEX);
        Matcher matcher = pattern.matcher(target);
        return matcher.matches();
    }


    /**
     * 判断当前设备类型
     */
//    public static UserLoginDeviceType GetDeviceType(String userAgent) {
//        log.info("当前userAgent:{} ", userAgent);
//        if (userAgent == null) {
//            return UserLoginDeviceType.UNKNOWN;
//        }
//
//        userAgent = userAgent.toLowerCase();
//
//        // 判断是否为微信小程序
//        if (userAgent.contains("micromessenger") && userAgent.contains("miniprogram")) {
//            return UserLoginDeviceType.WECHAT_MINI;
//        }
//
//        // 判断是否为移动设备（APP）
//        if (userAgent.contains("android")
//                || userAgent.contains("dart:io")
//                || userAgent.contains("iphone")
//                || userAgent.contains("ipad")
//                || userAgent.contains("ipod")) {
//            return UserLoginDeviceType.APP;
//        }
//
//        // 判断是否为桌面设备（WEB）
//        if (userAgent.contains("windows") || userAgent.contains("macintosh")) {
//            return UserLoginDeviceType.WEB;
//        }
//
//        // 默认返回WEB
//        return UserLoginDeviceType.WEB;
//    }


    /**
     * 使用序列化机制实现对象的深度拷贝
     *
     * @param target 需要拷贝的对象，必须实现Serializable接口
     * @param <T>    泛型参数，必须是Serializable的子类
     * @return 深度拷贝后的新对象
     * @throws IllegalArgumentException 如果拷贝过程中发生错误
     */
    public static <T extends Serializable> T deepCopy(T target) {
        Objects.requireNonNull(target, "Target object cannot be null");
        try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
             ObjectOutputStream out = new ObjectOutputStream(byteOut)) {
            out.writeObject(target);
            try (ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
                 ObjectInputStream in = new ObjectInputStream(byteIn)) {

                @SuppressWarnings("unchecked")
                T result = (T) in.readObject();
                return result;
            }
        } catch (IOException | ClassNotFoundException e) {
            log.error("Failed to deep copy object: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to deep copy object", e);
        }
    }


    /**
     * 复制选择字段
     *
     * @param source     源目标
     * @param target     目标
     * @param fieldNames 字段名称
     */
    public static void copySelectedProperties(Object source, Object target, String... fieldNames) {
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();
        for (String fieldName : fieldNames) {
            try {
                Field sourceField = sourceClass.getDeclaredField(fieldName);
                Field targetField = targetClass.getDeclaredField(fieldName);
                sourceField.setAccessible(true);
                targetField.setAccessible(true);
                Object value = sourceField.get(source);
                targetField.set(target, value);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException("复制字段失败: " + fieldName, e);
            }
        }
    }
}
