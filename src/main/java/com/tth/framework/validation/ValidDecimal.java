package com.tth.framework.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;

/**
 * BigDecimal 综合验证注解
 * 支持最小值、最大值、小数位数验证
 */
@Documented
@Constraint(validatedBy = ValidDecimal.ValidDecimalValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidDecimal {
    
    String message() default "数值不符合要求";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 最小值（包含）
     */
    String min() default "";
    
    /**
     * 最大值（包含）
     */
    String max() default "";
    
    /**
     * 最大小数位数，-1表示不限制
     */
    int scale() default -1;
    
    /**
     * 验证器实现类
     */
    class ValidDecimalValidator implements ConstraintValidator<ValidDecimal, BigDecimal> {
        
        private BigDecimal minValue;
        private BigDecimal maxValue;
        private int maxScale;
        
        @Override
        public void initialize(ValidDecimal constraintAnnotation) {
            String min = constraintAnnotation.min();
            String max = constraintAnnotation.max();
            this.maxScale = constraintAnnotation.scale();
            
            if (!min.isEmpty()) {
                this.minValue = new BigDecimal(min);
            }
            if (!max.isEmpty()) {
                this.maxValue = new BigDecimal(max);
            }
        }
        
        @Override
        public boolean isValid(BigDecimal value, ConstraintValidatorContext context) {
            if (value == null) {
                return true; // null值由其他注解处理
            }
            
            // 验证最小值
            if (minValue != null && value.compareTo(minValue) < 0) {
                updateMessage(context, String.format("数值不能小于%s", minValue));
                return false;
            }
            
            // 验证最大值
            if (maxValue != null && value.compareTo(maxValue) > 0) {
                updateMessage(context, String.format("数值不能大于%s", maxValue));
                return false;
            }
            
            // 验证小数位数
            if (maxScale >= 0 && value.scale() > maxScale) {
                updateMessage(context, String.format("小数位数不能超过%d位", maxScale));
                return false;
            }
            
            return true;
        }
        
        private void updateMessage(ConstraintValidatorContext context, String message) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
        }
    }
}
