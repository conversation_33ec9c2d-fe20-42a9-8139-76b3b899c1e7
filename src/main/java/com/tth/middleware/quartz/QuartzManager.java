package com.tth.middleware.quartz;

import com.tth.middleware.quartz.service.QuartzJobService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * Quartz中间件管理器
 * 提供统一的任务管理接口，封装常用操作
 */
@Slf4j
@Component
public class QuartzManager {

    @Resource
    private QuartzJobService quartzJobService;

    /**
     * 任务组常量
     */
    public static class JobGroups {
        public static final String TTS_GROUP = "TTS_GROUP";
        public static final String SYSTEM_GROUP = "SYSTEM_GROUP";
        public static final String BUSINESS_GROUP = "BUSINESS_GROUP";
    }

    /**
     * 任务名称常量
     */
    public static class JobNames {
        public static final String TTS_QUERY_JOB = "TtsQueryJob";
    }

    /**
     * 创建简单间隔任务
     *
     * @param jobName           任务名称
     * @param jobGroup          任务组
     * @param jobClass          任务类
     * @param intervalInSeconds 间隔秒数
     * @param description       任务描述
     * @return 是否创建成功
     */
    public boolean createSimpleJob(String jobName, String jobGroup, Class<? extends Job> jobClass,
                                  int intervalInSeconds, String description) {
        try {
            if (quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务已存在，跳过创建：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.createAndStartSimpleJob(jobName, jobGroup, jobClass, intervalInSeconds, description);
            return true;
        } catch (Exception e) {
            log.error("创建简单任务失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 创建Cron任务
     *
     * @param jobName        任务名称
     * @param jobGroup       任务组
     * @param jobClass       任务类
     * @param cronExpression Cron表达式
     * @param description    任务描述
     * @return 是否创建成功
     */
    public boolean createCronJob(String jobName, String jobGroup, Class<? extends Job> jobClass,
                                String cronExpression, String description) {
        try {
            if (quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务已存在，跳过创建：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.createAndStartJob(jobName, jobGroup, jobClass, cronExpression, description);
            return true;
        } catch (Exception e) {
            log.error("创建Cron任务失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 暂停任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     * @return 是否暂停成功
     */
    public boolean pauseJob(String jobName, String jobGroup) {
        try {
            if (!quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务不存在，无法暂停：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.pauseJob(jobName, jobGroup);
            return true;
        } catch (Exception e) {
            log.error("暂停任务失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 恢复任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     * @return 是否恢复成功
     */
    public boolean resumeJob(String jobName, String jobGroup) {
        try {
            if (!quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务不存在，无法恢复：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.resumeJob(jobName, jobGroup);
            return true;
        } catch (Exception e) {
            log.error("恢复任务失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 删除任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     * @return 是否删除成功
     */
    public boolean deleteJob(String jobName, String jobGroup) {
        try {
            if (!quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务不存在，无法删除：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.deleteJob(jobName, jobGroup);
            return true;
        } catch (Exception e) {
            log.error("删除任务失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 立即执行任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     * @return 是否执行成功
     */
    public boolean triggerJob(String jobName, String jobGroup) {
        try {
            if (!quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务不存在，无法执行：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.triggerJob(jobName, jobGroup);
            return true;
        } catch (Exception e) {
            log.error("执行任务失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 检查任务是否存在
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     * @return 是否存在
     */
    public boolean checkJobExists(String jobName, String jobGroup) {
        return quartzJobService.checkJobExists(jobName, jobGroup);
    }

    /**
     * 更新任务的Cron表达式
     *
     * @param jobName        任务名称
     * @param jobGroup       任务组
     * @param cronExpression 新的Cron表达式
     * @return 是否更新成功
     */
    public boolean updateJobCron(String jobName, String jobGroup, String cronExpression) {
        try {
            if (!quartzJobService.checkJobExists(jobName, jobGroup)) {
                log.warn("任务不存在，无法更新：{}.{}", jobGroup, jobName);
                return false;
            }
            
            quartzJobService.updateJobCron(jobName, jobGroup, cronExpression);
            return true;
        } catch (Exception e) {
            log.error("更新任务Cron表达式失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }
}
