# Quartz中间件

基于Spring Boot + Quartz的分布式定时任务中间件，支持集群模式和数据库持久化。

## 目录结构

```
middleware/quartz/
├── config/
│   └── QuartzConfig.java          # Quartz配置类
├── service/
│   └── QuartzJobService.java      # 任务管理服务
├── job/
│   └── TtsQueryJob.java           # TTS查询任务（包含初始化逻辑）
├── QuartzManager.java             # 统一管理器
└── README.md                      # 说明文档
```

## 核心组件

### 1. QuartzConfig
- 配置Quartz调度器
- 支持集群模式
- 数据库持久化存储

### 2. QuartzJobService
- 提供任务的CRUD操作
- 支持Cron和简单间隔任务
- 完善的异常处理

### 3. QuartzManager
- 统一的任务管理接口
- 封装常用操作
- 提供任务组和任务名常量

### 4. TtsQueryJob
- TTS任务状态查询
- 自动更新数据库状态
- 支持多种合成目标
- 包含自动初始化逻辑（@PostConstruct）

## 使用方式

### 创建简单间隔任务
```java
@Resource
private QuartzManager quartzManager;

// 创建每30秒执行一次的任务
quartzManager.createSimpleJob(
    "MyJob",
    QuartzManager.JobGroups.BUSINESS_GROUP,
    MyJobClass.class,
    30,
    "我的定时任务"
);
```

### 创建Cron任务
```java
// 创建每天凌晨2点执行的任务
quartzManager.createCronJob(
    "DailyJob",
    QuartzManager.JobGroups.SYSTEM_GROUP,
    DailyJobClass.class,
    "0 0 2 * * ?",
    "每日任务"
);
```

### 任务管理
```java
// 暂停任务
quartzManager.pauseJob("MyJob", QuartzManager.JobGroups.BUSINESS_GROUP);

// 恢复任务
quartzManager.resumeJob("MyJob", QuartzManager.JobGroups.BUSINESS_GROUP);

// 立即执行
quartzManager.triggerJob("MyJob", QuartzManager.JobGroups.BUSINESS_GROUP);

// 删除任务
quartzManager.deleteJob("MyJob", QuartzManager.JobGroups.BUSINESS_GROUP);
```

## 部署步骤

### 1. 创建数据库表
执行 `src/main/resources/sql/quartz_tables_mysql.sql` 脚本创建Quartz相关表。

### 2. 配置数据源
确保应用配置了正确的数据源，Quartz会自动使用该数据源。

### 3. 启动应用
应用启动时会自动初始化TTS查询任务。

## 手动管理任务

如果需要手动管理任务，可以直接注入 `QuartzManager`：

```java
@Resource
private QuartzManager quartzManager;

// 检查TTS查询任务是否存在
boolean exists = quartzManager.checkJobExists(
    QuartzManager.JobNames.TTS_QUERY_JOB,
    QuartzManager.JobGroups.TTS_GROUP
);

// 手动触发TTS查询任务
quartzManager.triggerJob(
    QuartzManager.JobNames.TTS_QUERY_JOB,
    QuartzManager.JobGroups.TTS_GROUP
);

// 暂停TTS查询任务
quartzManager.pauseJob(
    QuartzManager.JobNames.TTS_QUERY_JOB,
    QuartzManager.JobGroups.TTS_GROUP
);

// 恢复TTS查询任务
quartzManager.resumeJob(
    QuartzManager.JobNames.TTS_QUERY_JOB,
    QuartzManager.JobGroups.TTS_GROUP
);
```

## 特性

- ✅ **集群支持** - 多实例环境下避免重复执行
- ✅ **持久化存储** - 任务信息存储在数据库中
- ✅ **动态管理** - 支持运行时创建、修改、删除任务
- ✅ **异常处理** - 完善的错误处理和日志记录
- ✅ **统一管理** - 提供统一的管理接口和常量定义
- ✅ **自动初始化** - 应用启动时自动创建必要的任务

## 扩展指南

### 添加新的定时任务

1. **创建Job类（包含初始化逻辑）**
```java
@Slf4j
@Component
public class MyCustomJob implements Job {

    @Resource
    private QuartzManager quartzManager;

    @PostConstruct
    public void initJob() {
        try {
            boolean success = quartzManager.createSimpleJob(
                "MyCustomJob",
                QuartzManager.JobGroups.BUSINESS_GROUP,
                MyCustomJob.class,
                60, // 60秒间隔
                "我的自定义任务"
            );

            if (success) {
                log.info("自定义任务初始化完成");
            }
        } catch (Exception e) {
            log.error("自定义任务初始化失败", e);
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        // 任务执行逻辑
        log.info("执行自定义任务");
    }
}
```

2. **手动管理**（可选）
通过 `QuartzManager` 进行任务管理。

## 注意事项

- 任务类必须实现 `org.quartz.Job` 接口
- 任务类需要添加 `@Component` 注解以便Spring管理
- 集群环境下确保所有节点使用相同的数据库
- 任务执行时间较长时考虑使用 `@DisallowConcurrentExecution` 注解
- 定期清理过期的任务执行记录
