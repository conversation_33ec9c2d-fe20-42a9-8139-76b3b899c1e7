package com.tth.middleware.quartz.config;

import org.quartz.Scheduler;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SpringBeanJobFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

/**
 * Quartz配置类
 * 配置Quartz调度器，支持集群模式和数据库持久化
 */
@Configuration
public class QuartzConfig {

    /**
     * 配置JobFactory，支持Spring Bean注入
     */
    @Bean
    public SpringBeanJobFactory springBeanJobFactory(ApplicationContext applicationContext) {
        AutoWiringSpringBeanJobFactory jobFactory = new AutoWiringSpringBeanJobFactory();
        jobFactory.setApplicationContext(applicationContext);
        return jobFactory;
    }

    /**
     * 配置SchedulerFactoryBean
     *
     * @param dataSource 数据源
     * @param springBeanJobFactory Job工厂
     * @return SchedulerFactoryBean
     */
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean(DataSource dataSource, SpringBeanJobFactory springBeanJobFactory) {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();

        // 设置数据源
        factory.setDataSource(dataSource);

        // 设置JobFactory，支持Spring Bean注入
        factory.setJobFactory(springBeanJobFactory);

        // 设置Quartz属性
        Properties quartzProperties = new Properties();

        // 调度器实例名称
        quartzProperties.setProperty("org.quartz.scheduler.instanceName", "TTH-Scheduler");
        quartzProperties.setProperty("org.quartz.scheduler.instanceId", "AUTO");

        // 线程池配置
        quartzProperties.setProperty("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        quartzProperties.setProperty("org.quartz.threadPool.threadCount", "10");
        quartzProperties.setProperty("org.quartz.threadPool.threadPriority", "5");
        quartzProperties.setProperty("org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread", "true");

        // JobStore配置 - 使用数据库存储
        quartzProperties.setProperty("org.quartz.jobStore.class", "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
        quartzProperties.setProperty("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.StdJDBCDelegate");
        quartzProperties.setProperty("org.quartz.jobStore.tablePrefix", "QRTZ_");
        quartzProperties.setProperty("org.quartz.jobStore.isClustered", "true");
        quartzProperties.setProperty("org.quartz.jobStore.clusterCheckinInterval", "20000");
        quartzProperties.setProperty("org.quartz.jobStore.maxMisfiresToHandleAtATime", "1");
        quartzProperties.setProperty("org.quartz.jobStore.misfireThreshold", "120000");

        factory.setQuartzProperties(quartzProperties);

        // 延迟启动，等待Spring容器初始化完成
        factory.setStartupDelay(10);

        // 应用关闭时等待任务完成
        factory.setWaitForJobsToCompleteOnShutdown(true);

        // 覆盖已存在的任务
        factory.setOverwriteExistingJobs(true);

        // 自动启动
        factory.setAutoStartup(true);

        return factory;
    }

    /**
     * 获取Scheduler实例
     *
     * @param schedulerFactoryBean SchedulerFactoryBean
     * @return Scheduler
     */
    @Bean
    public Scheduler scheduler(SchedulerFactoryBean schedulerFactoryBean) {
        return schedulerFactoryBean.getScheduler();
    }

    /**
     * 自定义JobFactory，支持Spring Bean自动注入
     */
    public static class AutoWiringSpringBeanJobFactory extends SpringBeanJobFactory {
        private ApplicationContext applicationContext;

        public void setApplicationContext(ApplicationContext applicationContext) {
            this.applicationContext = applicationContext;
        }

        @Override
        protected Object createJobInstance(org.quartz.spi.TriggerFiredBundle bundle) throws Exception {
            Object job = super.createJobInstance(bundle);
            applicationContext.getAutowireCapableBeanFactory().autowireBean(job);
            return job;
        }
    }
}
