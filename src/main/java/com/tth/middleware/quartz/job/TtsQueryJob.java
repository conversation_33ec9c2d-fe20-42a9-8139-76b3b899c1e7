package com.tth.middleware.quartz.job;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tth.middleware.quartz.QuartzManager;
import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.modules.fairytale.enums.TtsStatusEnum;
import com.tth.modules.fairytale.service.FairyTaleService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

import java.util.Arrays;
import java.util.List;

/**
 * TTS任务查询Job
 * 定时查询火山引擎TTS任务状态并更新数据库
 */
@Slf4j
@Component
public class TtsQueryJob implements Job {

    @Resource
    private FairyTaleService fairyTaleService;

    @Resource
    private QuartzManager quartzManager;

    @Resource
    private Environment environment;

    /**
     * 初始化TTS查询任务
     * 应用启动时自动创建和启动定时任务
     */
    @PostConstruct
    public void initJob() {
        log.info("开始初始化TTS查询任务");

        // 检查当前环境，本地环境跳过创建任务
        String[] activeProfiles = environment.getActiveProfiles();
        boolean isLocalEnv;

        if (activeProfiles.length == 0) {
            // 没有激活的profile，使用默认profile
            String[] defaultProfiles = environment.getDefaultProfiles();
            isLocalEnv = Arrays.asList(defaultProfiles).contains("local");
        } else {
            isLocalEnv = Arrays.asList(activeProfiles).contains("local");
        }

        log.info("当前激活的环境：{}", Arrays.toString(activeProfiles));

        if (isLocalEnv) {
            log.info("当前为本地环境，跳过TTS查询任务创建");
            return;
        }

        // 先检查任务是否存在
        boolean exists = quartzManager.checkJobExists(
                QuartzManager.JobNames.TTS_QUERY_JOB,
                QuartzManager.JobGroups.TTS_GROUP
        );

        log.info("TTS查询任务存在状态：{}", exists);

        if (exists) {
            log.warn("TTS查询任务已存在，将删除后重新创建");
            // 删除已存在的任务
            quartzManager.deleteJob(
                    QuartzManager.JobNames.TTS_QUERY_JOB,
                    QuartzManager.JobGroups.TTS_GROUP
            );
        }

        // 创建TTS查询任务 - 每60秒（1分钟）执行一次
        boolean success = quartzManager.createSimpleJob(
                QuartzManager.JobNames.TTS_QUERY_JOB,
                QuartzManager.JobGroups.TTS_GROUP,
                TtsQueryJob.class,
                60, // 60秒间隔（1分钟）
                "TTS任务状态查询定时任务"
        );

        if (success) {
            log.info("TTS查询任务初始化完成");
        } else {
            log.error("TTS查询任务初始化失败");
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 检查当前环境，本地环境跳过执行
            String[] activeProfiles = environment.getActiveProfiles();
            boolean isLocalEnv;

            if (activeProfiles.length == 0) {
                // 没有激活的profile，使用默认profile
                String[] defaultProfiles = environment.getDefaultProfiles();
                isLocalEnv = Arrays.asList(defaultProfiles).contains("local");
            } else {
                isLocalEnv = Arrays.asList(activeProfiles).contains("local");
            }

            if (isLocalEnv) {
                log.info("当前为本地环境，跳过TTS查询任务执行");
                return;
            }

            // 查询所有"合成中"状态的任务
            List<FairyTale> pendingTasks = getPendingTtsTasks();

            if (pendingTasks.isEmpty()) {
                log.info("没有待查询的TTS任务，跳过执行");
                return;
            }

            log.info("开始执行TTS任务状态查询，找到 {} 个待查询任务", pendingTasks.size());

            for (FairyTale fairyTale : pendingTasks) {
                queryAndUpdateTaskStatus(fairyTale);
            }

            log.info("TTS任务状态查询完成");
        } catch (Exception e) {
            log.error("TTS查询任务执行异常", e);
            throw new JobExecutionException("TTS查询任务执行异常", e);
        }
    }

    /**
     * 获取所有待查询的TTS任务
     */
    private List<FairyTale> getPendingTtsTasks() {
        LambdaQueryWrapper<FairyTale> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(w -> w
                .eq(FairyTale::getChineseTitleTtsStatus, TtsStatusEnum.SYNTHESIZING)
                .or()
                .eq(FairyTale::getChineseContentTtsStatus, TtsStatusEnum.SYNTHESIZING)
                .or()
                .eq(FairyTale::getChineseTitleContentTtsStatus, TtsStatusEnum.SYNTHESIZING)
                .or()
                .eq(FairyTale::getEnglishTitleTtsStatus, TtsStatusEnum.SYNTHESIZING)
                .or()
                .eq(FairyTale::getEnglishContentTtsStatus, TtsStatusEnum.SYNTHESIZING)
                .or()
                .eq(FairyTale::getEnglishTitleContentTtsStatus, TtsStatusEnum.SYNTHESIZING)
        );

        return fairyTaleService.list(wrapper);
    }

    /**
     * 查询并更新任务状态
     */
    private void queryAndUpdateTaskStatus(FairyTale fairyTale) {
        // 查询中文标题TTS状态
        if (TtsStatusEnum.SYNTHESIZING.equals(fairyTale.getChineseTitleTtsStatus()) &&
            ObjUtil.isNotEmpty(fairyTale.getChineseTitleTaskId())) {
            queryTtsTaskStatusByTarget(fairyTale.getId(), 1);
        }

        // 查询中文内容TTS状态
        if (TtsStatusEnum.SYNTHESIZING.equals(fairyTale.getChineseContentTtsStatus()) &&
            ObjUtil.isNotEmpty(fairyTale.getChineseContentTaskId())) {
            queryTtsTaskStatusByTarget(fairyTale.getId(), 2);
        }

        // 查询中文标题+内容TTS状态
        if (TtsStatusEnum.SYNTHESIZING.equals(fairyTale.getChineseTitleContentTtsStatus()) &&
            ObjUtil.isNotEmpty(fairyTale.getChineseTitleContentTaskId())) {
            queryTtsTaskStatusByTarget(fairyTale.getId(), 3);
        }

        // 查询英文标题TTS状态
        if (TtsStatusEnum.SYNTHESIZING.equals(fairyTale.getEnglishTitleTtsStatus()) &&
            ObjUtil.isNotEmpty(fairyTale.getEnglishTitleTaskId())) {
            queryTtsTaskStatusByTarget(fairyTale.getId(), 4);
        }

        // 查询英文内容TTS状态
        if (TtsStatusEnum.SYNTHESIZING.equals(fairyTale.getEnglishContentTtsStatus()) &&
            ObjUtil.isNotEmpty(fairyTale.getEnglishContentTaskId())) {
            queryTtsTaskStatusByTarget(fairyTale.getId(), 5);
        }

        // 查询英文标题+内容TTS状态
        if (TtsStatusEnum.SYNTHESIZING.equals(fairyTale.getEnglishTitleContentTtsStatus()) &&
            ObjUtil.isNotEmpty(fairyTale.getEnglishTitleContentTaskId())) {
            queryTtsTaskStatusByTarget(fairyTale.getId(), 6);
        }
    }

    /**
     * 通过目标查询TTS任务状态
     * 复用FairyTaleService的querySynthesizeResult方法
     */
    private void queryTtsTaskStatusByTarget(Long fairyTaleId, Integer target) {
        try {
            log.debug("定时任务查询TTS状态，童话故事ID：{}，目标：{}", fairyTaleId, target);
            String result = fairyTaleService.querySynthesizeResult(fairyTaleId, target);
            log.debug("定时任务查询TTS状态完成，童话故事ID：{}，目标：{}，结果：{}", fairyTaleId, target, result);
        } catch (Exception e) {
            log.error("定时任务查询TTS状态失败，童话故事ID：{}，目标：{}，错误：{}", fairyTaleId, target, e.getMessage(), e);
        }
    }
}
