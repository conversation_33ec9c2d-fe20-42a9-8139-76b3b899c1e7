package com.tth.middleware.quartz.service;

import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * Quartz任务管理服务
 * 提供任务的创建、启动、停止、删除等功能
 */
@Slf4j
@Service
public class QuartzJobService {

    @Resource
    private Scheduler scheduler;

    /**
     * 创建并启动定时任务
     *
     * @param jobName     任务名称
     * @param jobGroup    任务组
     * @param jobClass    任务类
     * @param cronExpression Cron表达式
     * @param description 任务描述
     */
    public void createAndStartJob(String jobName, String jobGroup, Class<? extends Job> jobClass, 
                                 String cronExpression, String description) {
        try {
            // 创建JobDetail
            JobDetail jobDetail = JobBuilder.newJob(jobClass)
                    .withIdentity(jobName, jobGroup)
                    .withDescription(description)
                    .build();

            // 创建Trigger
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(jobName + "_trigger", jobGroup)
                    .withDescription(description)
                    .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                    .build();

            // 调度任务
            scheduler.scheduleJob(jobDetail, trigger);
            
            log.info("定时任务创建成功：{}，Cron表达式：{}", jobName, cronExpression);
            
        } catch (SchedulerException e) {
            log.error("创建定时任务失败：{}", jobName, e);
            throw new RuntimeException("创建定时任务失败", e);
        }
    }

    /**
     * 创建并启动简单间隔任务
     *
     * @param jobName     任务名称
     * @param jobGroup    任务组
     * @param jobClass    任务类
     * @param intervalInSeconds 间隔秒数
     * @param description 任务描述
     */
    public void createAndStartSimpleJob(String jobName, String jobGroup, Class<? extends Job> jobClass, 
                                       int intervalInSeconds, String description) {
        try {
            // 创建JobDetail
            JobDetail jobDetail = JobBuilder.newJob(jobClass)
                    .withIdentity(jobName, jobGroup)
                    .withDescription(description)
                    .build();

            // 创建SimpleTrigger
            SimpleTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(jobName + "_trigger", jobGroup)
                    .withDescription(description)
                    .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withIntervalInSeconds(intervalInSeconds)
                            .repeatForever())
                    .build();

            // 调度任务
            scheduler.scheduleJob(jobDetail, trigger);
            
            log.info("简单定时任务创建成功：{}，间隔：{}秒", jobName, intervalInSeconds);
            
        } catch (SchedulerException e) {
            log.error("创建简单定时任务失败：{}", jobName, e);
            throw new RuntimeException("创建简单定时任务失败", e);
        }
    }

    /**
     * 暂停任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     */
    public void pauseJob(String jobName, String jobGroup) {
        try {
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            scheduler.pauseJob(jobKey);
            log.info("任务暂停成功：{}.{}", jobGroup, jobName);
        } catch (SchedulerException e) {
            log.error("暂停任务失败：{}.{}", jobGroup, jobName, e);
            throw new RuntimeException("暂停任务失败", e);
        }
    }

    /**
     * 恢复任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     */
    public void resumeJob(String jobName, String jobGroup) {
        try {
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            scheduler.resumeJob(jobKey);
            log.info("任务恢复成功：{}.{}", jobGroup, jobName);
        } catch (SchedulerException e) {
            log.error("恢复任务失败：{}.{}", jobGroup, jobName, e);
            throw new RuntimeException("恢复任务失败", e);
        }
    }

    /**
     * 删除任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     */
    public void deleteJob(String jobName, String jobGroup) {
        try {
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            scheduler.deleteJob(jobKey);
            log.info("任务删除成功：{}.{}", jobGroup, jobName);
        } catch (SchedulerException e) {
            log.error("删除任务失败：{}.{}", jobGroup, jobName, e);
            throw new RuntimeException("删除任务失败", e);
        }
    }

    /**
     * 立即执行任务
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     */
    public void triggerJob(String jobName, String jobGroup) {
        try {
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            scheduler.triggerJob(jobKey);
            log.info("任务立即执行：{}.{}", jobGroup, jobName);
        } catch (SchedulerException e) {
            log.error("立即执行任务失败：{}.{}", jobGroup, jobName, e);
            throw new RuntimeException("立即执行任务失败", e);
        }
    }

    /**
     * 检查任务是否存在
     *
     * @param jobName  任务名称
     * @param jobGroup 任务组
     * @return 是否存在
     */
    public boolean checkJobExists(String jobName, String jobGroup) {
        try {
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            return scheduler.checkExists(jobKey);
        } catch (SchedulerException e) {
            log.error("检查任务是否存在失败：{}.{}", jobGroup, jobName, e);
            return false;
        }
    }

    /**
     * 更新任务的Cron表达式
     *
     * @param jobName        任务名称
     * @param jobGroup       任务组
     * @param cronExpression 新的Cron表达式
     */
    public void updateJobCron(String jobName, String jobGroup, String cronExpression) {
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(jobName + "_trigger", jobGroup);
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            
            if (trigger == null) {
                log.error("未找到触发器：{}.{}", jobGroup, jobName);
                return;
            }
            
            // 创建新的触发器
            CronTrigger newTrigger = trigger.getTriggerBuilder()
                    .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                    .build();
            
            // 重新调度
            scheduler.rescheduleJob(triggerKey, newTrigger);
            
            log.info("任务Cron表达式更新成功：{}.{}，新表达式：{}", jobGroup, jobName, cronExpression);
            
        } catch (SchedulerException e) {
            log.error("更新任务Cron表达式失败：{}.{}", jobGroup, jobName, e);
            throw new RuntimeException("更新任务Cron表达式失败", e);
        }
    }
}
