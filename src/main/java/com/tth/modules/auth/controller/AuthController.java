package com.tth.modules.auth.controller;

import com.tth.framework.annotation.ApiLog;
import com.tth.framework.response.R;
import com.tth.framework.utils.AuthUtil;
import com.tth.framework.utils.IpUtil;
import com.tth.modules.auth.model.dto.LoginDTO;
import com.tth.modules.auth.model.dto.RegisterDTO;
import com.tth.modules.auth.model.vo.TokenVO;
import com.tth.modules.auth.model.vo.UserInfoVO;
import com.tth.modules.auth.service.AuthService;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/auth")
@Tag(name = "认证接口", description = "用户登录、注册、登出等接口")
public class AuthController {

    @Resource
    private AuthService authService;

    @ApiLog(module = "认证模块", value = "调用统一登录接口")
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "统一登录接口，支持多种登录方式")
    public R<TokenVO> login(@RequestBody @Valid LoginDTO loginDTO, HttpServletRequest request) {
        // 如果前端没有传IP地址，从请求中获取
        if (loginDTO.getIpAddress() == null || loginDTO.getIpAddress().isEmpty()) {
            loginDTO.setIpAddress(IpUtil.getClientIp(request));
        }

        TokenVO tokenVO = authService.login(loginDTO);
        return R.success(tokenVO);
    }

    @PostMapping("/token/refresh")
    @Operation(summary = "刷新Token", description = "使用刷新令牌获取新的访问令牌")
    public R<TokenVO> refreshToken(@RequestParam String refreshToken) {
        TokenVO tokenVO = authService.refreshToken(refreshToken);
        return R.success(tokenVO);
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户注册接口", hidden = true)
    public R<Boolean> register(@RequestBody @Valid RegisterDTO registerDTO) {
        boolean result = authService.register(registerDTO);
        return R.success(result);
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出接口")
    public R<Boolean> logout(@RequestHeader(value = "Authorization") String accessToken) {
        authService.logout(accessToken);
        return R.success();
    }

    @ApiLog(module = "认证模块", value = "获取当前用户信息")
    @GetMapping("/userInfo")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的基本信息")
    public R<UserInfoVO> getCurrentUserInfo() {
        Long userId = AuthUtil.getUserId();
        UserTypeEnum userType = AuthUtil.getUserType();
        UserInfoVO userInfo = authService.getCurrentUserInfo(userId, userType);
        return R.success(userInfo);
    }
}