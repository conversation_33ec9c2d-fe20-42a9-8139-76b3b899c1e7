package com.tth.modules.auth.model.dto;

import com.tth.modules.user.enums.field.DeviceTypeEnum;
import com.tth.modules.user.enums.field.IdentityTypeEnum;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 登录请求DTO
 */
@Data
@Schema(description = "登录请求")
public class LoginDTO {

    @Schema(description = "认证类型", requiredMode = Schema.RequiredMode.REQUIRED, defaultValue = "USERNAME")
    @NotNull(message = "认证类型不能为空")
    private IdentityTypeEnum identityType = IdentityTypeEnum.USERNAME; // 默认为用户名登录

    @Schema(description = "认证标识（用户名/手机号/邮箱等，微信小程序登录，填写固定值：js_code/微信小程序授权手机号登录，填写固定值：phone_code）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "认证标识不能为空")
    private String identifier;

    @Schema(description = "凭证（密码/验证码/token/微信小程序的code/微信小程序授权手机号phoneCode等）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "凭证不能为空")
    private String credential;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, defaultValue = "ADMIN")
    @NotNull(message = "用户类型不能为空")
    private UserTypeEnum userType;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备类型", defaultValue = "PC")
    private DeviceTypeEnum deviceType;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "用户头像URL（微信小程序登录时使用）")
    private String avatarUrl;

    @Schema(description = "用户昵称（微信小程序登录时使用）")
    private String nickName;
}
