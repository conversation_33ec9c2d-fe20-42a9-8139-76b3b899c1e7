package com.tth.modules.auth.model.vo;

import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 双Token响应VO
 */
@Data
@Schema(description = "双Token响应")
public class TokenVO {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户类型（ADMIN/CUSTOMER/MERCHANT/PARTNER等）")
    private UserTypeEnum userType;

    @Schema(description = "访问令牌值")
    private String accessToken;

    @Schema(description = "访问令牌有效期（秒）")
    private Long accessTokenExpireIn;

    @Schema(description = "刷新令牌值")
    private String refreshToken;

    @Schema(description = "刷新令牌有效期（秒）")
    private Long refreshTokenExpireIn;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "登录时间")
    private LocalDateTime loginTime;
}
