package com.tth.modules.auth.model.vo;

import com.tth.common.enums.EnabledStatusEnum;
import com.tth.common.enums.GenderEnum;
import com.tth.common.enums.VerifyStatusEnum;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息VO
 * 统一的用户信息返回对象，支持客户用户和管理员用户
 */
@Data
@Schema(description = "用户信息")
public class UserInfoVO {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户类型")
    private UserTypeEnum userType;

    @Schema(description = "用户状态")
    private EnabledStatusEnum status;

    @Schema(description = "用户名（管理员用户才有）")
    private String username;

    @Schema(description = "昵称（客户用户才有）")
    private String nickname;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phone;

    // 客户用户特有字段
    @Schema(description = "生日（客户用户才有）")
    private LocalDate birthday;

    @Schema(description = "地区（客户用户才有）")
    private String region;

    @Schema(description = "用户等级（客户用户才有）")
    private Integer level;

    @Schema(description = "积分（客户用户才有）")
    private Integer points;

    @Schema(description = "实名认证状态（客户用户才有）")
    private VerifyStatusEnum verifyStatus;

    @Schema(description = "最后登录时间（客户用户才有）")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP（客户用户才有）")
    private String lastLoginIp;

    // 管理员用户特有字段
    @Schema(description = "部门ID（管理员用户才有）")
    private Long deptId;

    @Schema(description = "部门名称（管理员用户才有）")
    private String deptName;

    @Schema(description = "职位（管理员用户才有）")
    private String jobTitle;

    @Schema(description = "登录IP（管理员用户才有）")
    private String loginIp;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "备注")
    private String remark;
}
