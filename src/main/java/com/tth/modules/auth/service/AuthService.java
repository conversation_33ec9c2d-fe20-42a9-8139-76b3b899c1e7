package com.tth.modules.auth.service;

import com.tth.modules.auth.model.dto.LoginDTO;
import com.tth.modules.auth.model.dto.RegisterDTO;
import com.tth.modules.auth.model.vo.TokenVO;
import com.tth.modules.auth.model.vo.UserInfoVO;
import com.tth.modules.user.enums.field.UserTypeEnum;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    TokenVO login(LoginDTO loginDTO);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的双令牌信息
     */
    TokenVO refreshToken(String refreshToken);

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 是否成功
     */
    boolean register(RegisterDTO registerDTO);

    /**
     * 用户登出
     *
     */
    void logout(String accessToken);

    /**
     * 获取当前用户信息
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 用户信息
     */
    UserInfoVO getCurrentUserInfo(Long userId, UserTypeEnum userType);
}
