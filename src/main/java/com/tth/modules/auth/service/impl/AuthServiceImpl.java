package com.tth.modules.auth.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.common.enums.RedisEnum;
import com.tth.common.enums.YesNoEnum;
import com.tth.common.enums.PlatformSourceEnum;
import com.tth.framework.exception.code.AuthExceptionCode;
import com.tth.framework.exception.core.AuthException;
import com.tth.modules.auth.model.dto.LoginDTO;
import com.tth.modules.auth.model.dto.RegisterDTO;
import com.tth.modules.auth.model.vo.TokenVO;
import com.tth.modules.auth.model.vo.UserInfoVO;
import com.tth.modules.user.entity.AdminUser;
import com.tth.modules.auth.service.AuthService;
import com.tth.modules.user.entity.UserAuth;
import com.tth.modules.user.entity.UserBase;
import com.tth.modules.user.entity.UserDevice;
import com.tth.modules.user.entity.CustomerUser;
import com.tth.modules.user.enums.field.IdentityTypeEnum;
import com.tth.modules.user.enums.field.UserTypeEnum;
import com.tth.modules.user.service.AdminUserService;
import com.tth.modules.user.service.UserAuthService;
import com.tth.modules.user.service.UserBaseService;
import com.tth.modules.user.service.UserDeviceService;
import com.tth.modules.user.service.CustomerUserService;
import com.tth.thirdparty.wechat.miniprogram.service.MpAuthService;
import com.tth.thirdparty.wechat.miniprogram.service.MpUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    @Resource
    private UserBaseService userBaseService;

    @Resource
    private UserAuthService userAuthService;

    @Resource
    private UserDeviceService userDeviceService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MpAuthService mpAuthService;

    @Resource
    private MpUserService mpUserService;

    @Resource
    private CustomerUserService customerUserService;

    @Resource
    private AdminUserService adminUserService;

    @Value("${sa-token.timeout}")
    private long accessTokenTimeout;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TokenVO login(LoginDTO loginDTO) {
        // 根据登录方式分发到不同的处理逻辑
        return switch (loginDTO.getIdentityType()) {
            case USERNAME -> loginByUsername(loginDTO);
            case WECHAT_MINI_PROGRAM -> loginByWechatMiniProgram(loginDTO);
            case WECHAT_MINI_PROGRAM_PHONE -> loginByWechatMiniProgramPhone(loginDTO);
            case PHONE ->
                // 预留手机号登录逻辑
                throw new AuthException(AuthExceptionCode.UNSUPPORTED_LOGIN_TYPE, "手机号登录暂未实现");
            case EMAIL ->
                // 预留邮箱登录逻辑
                throw new AuthException(AuthExceptionCode.UNSUPPORTED_LOGIN_TYPE, "邮箱登录暂未实现");
            case WECHAT, ALIPAY, QQ, WEIBO ->
                // 预留第三方登录逻辑
                throw new AuthException(AuthExceptionCode.UNSUPPORTED_LOGIN_TYPE, "第三方登录暂未实现");
            default -> throw new AuthException(AuthExceptionCode.UNSUPPORTED_LOGIN_TYPE);
        };
    }

    /**
     * 用户名密码登录
     */
    private TokenVO loginByUsername(LoginDTO loginDTO) {
        // 1. 根据用户名和用户类型查询认证信息
        UserAuth userAuth = userAuthService.lambdaQuery()
                .eq(UserAuth::getIdentifier, loginDTO.getIdentifier())
                .eq(UserAuth::getUserType, loginDTO.getUserType())
                .eq(UserAuth::getIdentityType, IdentityTypeEnum.USERNAME.getValue())
                .one();

        if (userAuth == null) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_FOUND);
        }

        // 2. 验证密码
        if(!StrUtil.equals(SaSecureUtil.md5(loginDTO.getCredential()), userAuth.getCredential())) {
            throw new AuthException(AuthExceptionCode.PASSWORD_ERROR);
        }

        // 3. 检查用户状态并记录设备信息
        checkUserStatusAndRecordDevice(userAuth.getUserBaseId(), loginDTO);

        // 4. 执行登录并构建返回结果
        return performLogin(userAuth.getUserBaseId(), userAuth.getUserType());
    }

    /**
     * 微信小程序登录
     */
    private TokenVO loginByWechatMiniProgram(LoginDTO loginDTO) {
        if(UserTypeEnum.CUSTOMER != loginDTO.getUserType()) {
            throw new AuthException(AuthExceptionCode.LOGIN_FAILED, "微信小程序登录只支持顾客用户类型"); // 暂时只允许顾客使用微信小程序登录
        }

        // 1. 调用微信小程序API验证code并获取openid
        JSONObject response = mpAuthService.jscode2session(loginDTO.getCredential());

        // 2. 根据openid查询用户认证信息
        UserAuth userAuth = userAuthService.lambdaQuery()
                .eq(UserAuth::getIdentifier, response.getStr("openid"))
                .eq(UserAuth::getUserType, loginDTO.getUserType())
                .eq(UserAuth::getIdentityType, IdentityTypeEnum.WECHAT_MINI_PROGRAM)
                .one();

        Long userId;
        if (userAuth == null) {
            // 3. 用户不存在，自动创建新用户
            userId = createWechatMiniProgramUser(response, loginDTO);
        } else {
            // 4. 用户已存在，检查用户状态
            UserBase userBase = userBaseService.getById(userAuth.getUserBaseId());
            if (userBase == null) {
                throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_ACTIVATED);
            }

            // 检查用户状态
            if (userBase.getStatus() != null && userBase.getStatus() == EnabledStatusEnum.DISABLED) {
                throw new AuthException(AuthExceptionCode.ACCOUNT_DISABLED);
            }

            String sessionKey = response.getStr("session_key");
            // 更新session_key（如果有变化）
            if (StrUtil.isNotBlank(sessionKey) &&
                !StrUtil.equals(sessionKey, userAuth.getCredential())) {
                userAuth.setCredential(sessionKey);
                userAuthService.updateById(userAuth);
            }
            userId = userAuth.getUserBaseId();
        }

        // 5. 检查用户状态并记录设备信息
        checkUserStatusAndRecordDevice(userId, loginDTO);

        // 6. 执行登录并构建返回结果
        return performLogin(userId, loginDTO.getUserType());
    }

    /**
     * 微信小程序授权手机号登录
     */
    private TokenVO loginByWechatMiniProgramPhone(LoginDTO loginDTO) {
        if(UserTypeEnum.CUSTOMER != loginDTO.getUserType()) {
            throw new AuthException(AuthExceptionCode.LOGIN_FAILED, "微信小程序授权手机号登录只支持顾客用户类型");
        }

        // 1. 验证phoneCode并获取手机号
        if (StrUtil.isBlank(loginDTO.getCredential())) {
            throw new AuthException(AuthExceptionCode.LOGIN_FAILED, "手机号授权码不能为空");
        }

        String phone = mpUserService.getPurePhoneNumber(loginDTO.getCredential());

        if (StrUtil.isBlank(phone)) {
            throw new AuthException(AuthExceptionCode.LOGIN_FAILED, "获取手机号为空，请重新授权");
        }

        // 2. 根据手机号查询用户认证信息
        UserAuth userAuth = userAuthService.lambdaQuery()
                .eq(UserAuth::getIdentifier, phone)
                .eq(UserAuth::getUserType, loginDTO.getUserType())
                .eq(UserAuth::getIdentityType, IdentityTypeEnum.WECHAT_MINI_PROGRAM_PHONE)
                .one();

        Long userId;
        if (userAuth == null) {
            // 3. 用户不存在，自动创建新用户
            userId = createWechatMiniProgramPhoneUser(phone, loginDTO);
        } else {
            // 4. 用户已存在，检查用户状态
            UserBase userBase = userBaseService.getById(userAuth.getUserBaseId());
            if (userBase == null) {
                throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_ACTIVATED);
            }

            // 检查用户状态
            if (userBase.getStatus() != null && userBase.getStatus() == EnabledStatusEnum.DISABLED) {
                throw new AuthException(AuthExceptionCode.ACCOUNT_DISABLED);
            }

            userId = userAuth.getUserBaseId();
        }

        // 5. 检查用户状态并记录设备信息
        checkUserStatusAndRecordDevice(userId, loginDTO);

        // 6. 执行登录并构建返回结果
        return performLogin(userId, loginDTO.getUserType());
    }

    /**
     * 创建微信小程序用户
     */
    private Long createWechatMiniProgramUser(JSONObject response, LoginDTO loginDTO) {
        // 1. 创建 UserBase 记录
        UserBase userBase = new UserBase();
        userBase.setUserType(loginDTO.getUserType());
        userBase.setStatus(EnabledStatusEnum.ENABLED); // 默认启用
        userBase.setRemark("微信小程序自动注册用户");
        userBaseService.save(userBase);

        // 2. 创建 CustomerUser 记录（用户主表）
        CustomerUser customerUser = new CustomerUser();
        customerUser.setUserBaseId(userBase.getId());

        // 设置用户昵称和头像
        if (StrUtil.isNotBlank(loginDTO.getNickName())) {
            customerUser.setNickname(loginDTO.getNickName());
        } else {
            customerUser.setNickname("微信用户"); // 默认昵称
        }

        if (StrUtil.isNotBlank(loginDTO.getAvatarUrl())) {
            customerUser.setAvatar(loginDTO.getAvatarUrl());
        }

        customerUser.setRegisterSource(PlatformSourceEnum.WECHAT_MINI_PROGRAM);
        customerUser.setRegisterIp(loginDTO.getIpAddress());
        customerUser.setLastLoginTime(LocalDateTime.now());
        customerUser.setLastLoginIp(loginDTO.getIpAddress());
        customerUser.setLevel(1); // 默认等级
        customerUser.setPoints(0); // 默认积分
        customerUserService.save(customerUser);

        // 3. 创建 UserAuth 记录
        UserAuth userAuth = new UserAuth();
        userAuth.setUserBaseId(userBase.getId());
        userAuth.setUserType(loginDTO.getUserType());
        userAuth.setIdentityType(IdentityTypeEnum.WECHAT_MINI_PROGRAM);
        userAuth.setIdentifier(response.getStr("openid")); // 存储openid
        userAuth.setCredential(response.getStr("session_key")); // 存储session_key
        userAuth.setVerified(YesNoEnum.YES); // 微信认证默认已验证
        userAuthService.save(userAuth);

        return userBase.getId();
    }

    /**
     * 检查用户状态并记录设备信息
     * 用于登录接口的前置检查
     */
    private void checkUserStatusAndRecordDevice(Long userId, LoginDTO loginDTO) {
        // 1. 检查用户状态
        UserBase userBase = userBaseService.getById(userId);
        if (userBase == null) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_ACTIVATED);
        }

        // 检查用户状态
        if (userBase.getStatus() != null && userBase.getStatus() == EnabledStatusEnum.DISABLED) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_DISABLED);
        }

        // 2. 记录设备信息（如果提供了设备信息）
        if (loginDTO.getDeviceId() != null && !loginDTO.getDeviceId().isEmpty()) {
            saveOrUpdateUserDevice(userId, loginDTO);
        }
    }

    /**
     * 执行登录并构建返回结果
     * 通用的登录处理方法，登录和刷新token都可以使用
     */
    private TokenVO performLogin(Long userId, UserTypeEnum userType) {
        // 1. 执行登录
        StpUtil.login(userId);

        // 2. 在登录时存储用户类型信息
        SaSession session = StpUtil.getSessionByLoginId(userId);
        session.set("userType", userType);

        // 3. 构建返回结果
        TokenVO tokenVO = this.getRefreshToken(userId);
        tokenVO.setUserId(userId);
        tokenVO.setUserType(userType);
        tokenVO.setAccessToken(StpUtil.getTokenValue());
        tokenVO.setAccessTokenExpireIn(accessTokenTimeout);
        tokenVO.setLoginTime(LocalDateTime.now());
        return tokenVO;
    }



    /**
     * 保存或更新用户设备信息
     */
    private void saveOrUpdateUserDevice(Long userId, LoginDTO loginDTO) {
        // 查询是否已存在该设备记录
        UserDevice userDevice = userDeviceService.lambdaQuery()
                .eq(UserDevice::getUserId, userId)
                .eq(UserDevice::getDeviceId, loginDTO.getDeviceId())
                .one();

        if (userDevice == null) {
            // 创建新设备记录
            userDevice = new UserDevice();
            userDevice.setUserId(userId);
            userDevice.setDeviceId(loginDTO.getDeviceId());
            userDevice.setDeviceType(loginDTO.getDeviceType());
            userDevice.setDeviceName(loginDTO.getDeviceName());
        }

        // 更新登录信息
        userDevice.setLastLoginTime(LocalDateTime.now());
        userDevice.setLastLoginIp(loginDTO.getIpAddress());
        userDevice.setToken(StpUtil.getTokenValue());
        userDevice.setTokenExpireTime(LocalDateTime.now().plusSeconds(accessTokenTimeout));

        // 保存或更新设备信息
        userDeviceService.saveOrUpdate(userDevice);
    }

    private TokenVO getRefreshToken(Long userId){
        String userIdKey = RedisEnum.USER_ID.getKey() + userId;
        String oldRefreshToken = stringRedisTemplate.opsForValue().get(userIdKey);

        String refreshTokenValue;
        String refreshTokenKey;
        if(StrUtil.isBlank(oldRefreshToken)){
            refreshTokenValue =IdUtil.fastSimpleUUID() + IdUtil.fastSimpleUUID();
            refreshTokenKey = RedisEnum.REFRESH_TOKEN.getKey() + refreshTokenValue;
        } else {
            refreshTokenValue = oldRefreshToken;
            refreshTokenKey = RedisEnum.REFRESH_TOKEN.getKey() + refreshTokenValue;
            stringRedisTemplate.delete(userIdKey);
        }

        long timeout = 30L;
        stringRedisTemplate.opsForValue().set(userIdKey, refreshTokenValue, timeout, TimeUnit.DAYS);
        stringRedisTemplate.opsForValue().set(refreshTokenKey, userId.toString(), timeout, TimeUnit.DAYS);

        TokenVO tokenVO = new TokenVO();
        tokenVO.setRefreshToken(refreshTokenValue);
        tokenVO.setRefreshTokenExpireIn(NumberUtil.mul(timeout, 24L, 60L, 60L).longValue());
        return tokenVO;
    }

    @Override
    public TokenVO refreshToken(String refreshToken) {
        String refreshTokenKey = RedisEnum.REFRESH_TOKEN.getKey() + refreshToken;
        String userId = stringRedisTemplate.opsForValue().getAndDelete(refreshTokenKey);
        if(StrUtil.isBlank(userId)){
            throw new AuthException(AuthExceptionCode.TOKEN_INVALID);
        }

        // 获取用户信息和用户类型
        Long userIdLong = Long.parseLong(userId);
        UserBase userBase = userBaseService.getById(userIdLong);
        if (userBase == null) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_ACTIVATED);
        }

        // 检查用户认证信息是否存在
        boolean exists = userAuthService.lambdaQuery()
                .eq(UserAuth::getUserBaseId, userIdLong)
                .exists();

        if (!exists) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_ACTIVATED);
        }

        // 获取用户类型
        UserAuth userAuth = userAuthService.lambdaQuery()
                .select(UserAuth::getUserType)
                .eq(UserAuth::getUserBaseId, userIdLong)
                .last("LIMIT 1")
                .one();

        // 检查用户状态
        if (userBase.getStatus() != null && userBase.getStatus() == EnabledStatusEnum.DISABLED) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_DISABLED);
        }

        // 执行登录并构建返回结果（刷新token不需要设备信息记录）
        return performLogin(userIdLong, userAuth.getUserType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean register(RegisterDTO registerDTO) {
        // 创建用户
        UserBase userBase = new UserBase();
//        userBase.setUsername(registerDTO.getUsername());
//        userBase.setPassword(BCrypt.hashpw(registerDTO.getPassword()));
//        userBase.setNickname(registerDTO.getNickname());
//        userBase.setDisabled(false);

        return userBaseService.save(userBase);
    }

    @Override
    public void logout(String accessToken) {
        Object userId = StpUtil.getStpLogic().getLoginIdByToken(accessToken);
        if(ObjUtil.isNull(userId)) {
            throw new AuthException(AuthExceptionCode.LOGOUT_FAILED);
        }

        String userIdKey = RedisEnum.USER_ID.getKey() + userId;
        String refreshTokenKey = stringRedisTemplate.opsForValue().getAndDelete(userIdKey);
        if (StrUtil.isNotBlank(refreshTokenKey)) {
            stringRedisTemplate.delete(refreshTokenKey);
        }

        StpUtil.logout(userId);
    }

    /**
     * 创建微信小程序授权手机号用户
     */
    private Long createWechatMiniProgramPhoneUser(String phone, LoginDTO loginDTO) {
        // 1. 创建 UserBase 记录
        UserBase userBase = new UserBase();
        userBase.setUserType(loginDTO.getUserType());
        userBase.setStatus(EnabledStatusEnum.ENABLED);
        userBase.setRemark("微信小程序授权手机号自动注册用户");
        userBaseService.save(userBase);

        // 2. 创建 CustomerUser 记录（用户主表）
        CustomerUser customerUser = new CustomerUser();
        customerUser.setUserBaseId(userBase.getId());

        // 设置用户昵称和头像
        if (StrUtil.isNotBlank(loginDTO.getNickName())) {
            customerUser.setNickname(loginDTO.getNickName());
        } else {
            customerUser.setNickname("微信用户"); // 默认昵称
        }

        if (StrUtil.isNotBlank(loginDTO.getAvatarUrl())) {
            customerUser.setAvatar(loginDTO.getAvatarUrl());
        }

        customerUser.setPhone(phone);
        customerUser.setRegisterSource(PlatformSourceEnum.WECHAT_MINI_PROGRAM);
        customerUser.setRegisterIp(loginDTO.getIpAddress());
        customerUser.setLastLoginTime(LocalDateTime.now());
        customerUser.setLastLoginIp(loginDTO.getIpAddress());
        customerUser.setLevel(1); // 默认等级
        customerUser.setPoints(0); // 默认积分
        customerUserService.save(customerUser);

        // 3. 创建 UserAuth 记录
        UserAuth userAuth = new UserAuth();
        userAuth.setUserBaseId(userBase.getId());
        userAuth.setUserType(loginDTO.getUserType());
        userAuth.setIdentityType(IdentityTypeEnum.WECHAT_MINI_PROGRAM_PHONE);
        userAuth.setIdentifier(phone); // 存储手机号
        userAuth.setCredential(null); // 微信授权手机号不需要密码
        userAuth.setVerified(YesNoEnum.YES); // 微信授权的手机号默认已验证
        userAuthService.save(userAuth);

        return userBase.getId();
    }

    @Override
    public UserInfoVO getCurrentUserInfo(Long userId, UserTypeEnum userType) {
        // 1. 获取用户基础信息
        UserBase userBase = userBaseService.getById(userId);
        if (userBase == null) {
            throw new AuthException(AuthExceptionCode.ACCOUNT_NOT_FOUND);
        }

        // 2. 根据用户类型获取详细信息
        UserInfoVO userInfo = new UserInfoVO();
        userInfo.setUserId(userId);
        userInfo.setUserType(userType);
        userInfo.setStatus(userBase.getStatus());
        userInfo.setCreatedTime(userBase.getCreatedTime());
        userInfo.setRemark(userBase.getRemark());

        if (userType == UserTypeEnum.CUSTOMER) {
            // 获取客户用户信息
            CustomerUser customerUser = customerUserService.lambdaQuery()
                    .eq(CustomerUser::getUserBaseId, userId)
                    .one();

            if (customerUser != null) {
                userInfo.setNickname(customerUser.getNickname());
                userInfo.setRealName(customerUser.getRealName());
                userInfo.setAvatar(customerUser.getAvatar());
                userInfo.setGender(customerUser.getGender());
                userInfo.setEmail(customerUser.getEmail());
                userInfo.setPhone(customerUser.getPhone());
                userInfo.setBirthday(customerUser.getBirthday());
                userInfo.setRegion(customerUser.getRegion());
                userInfo.setLevel(customerUser.getLevel());
                userInfo.setPoints(customerUser.getPoints());
                userInfo.setVerifyStatus(customerUser.getVerifyStatus());
                userInfo.setLastLoginTime(customerUser.getLastLoginTime());
                userInfo.setLastLoginIp(customerUser.getLastLoginIp());
            }
        } else if (userType == UserTypeEnum.ADMIN) {
            // 获取管理员用户信息
            AdminUser adminUser = adminUserService.lambdaQuery()
                    .eq(AdminUser::getUserBaseId, userId)
                    .one();

            if (adminUser != null) {
                userInfo.setRealName(adminUser.getRealName());
                userInfo.setAvatar(adminUser.getAvatar());
                userInfo.setGender(adminUser.getGender());
                userInfo.setEmail(adminUser.getEmail());
                userInfo.setPhone(adminUser.getPhone());
                userInfo.setDeptId(adminUser.getDeptId());
                userInfo.setJobTitle(adminUser.getJobTitle());
                userInfo.setLoginIp(adminUser.getLoginIp());
            }

            // 获取管理员用户名
            UserAuth userAuth = userAuthService.lambdaQuery()
                    .eq(UserAuth::getUserBaseId, userId)
                    .eq(UserAuth::getIdentityType, IdentityTypeEnum.USERNAME)
                    .eq(UserAuth::getUserType, UserTypeEnum.ADMIN)
                    .one();

            if (userAuth != null) {
                userInfo.setUsername(userAuth.getIdentifier());
            }
        }

        return userInfo;
    }
}
