//package com.tth.modules.clothes.controller;
//
//import cn.dev33.satoken.annotation.SaIgnore;
//import com.tth.framework.annotation.ApiLog;
//import com.tth.framework.utils.AuthUtil;
//import com.tth.modules.clothes.entity.Clothes;
//import com.tth.modules.clothes.service.ClothesService;
//import com.tth.modules.clothes.model.dto.AddClothesDTO;
//import com.tth.modules.clothes.model.vo.ClothesDetailVO;
//import com.tth.modules.clothes.model.vo.ClothesListVO;
//import com.tth.framework.paging.PageResult;
//import com.tth.framework.paging.Paging;
//import com.tth.framework.response.R;
//import jakarta.annotation.Resource;
//import jakarta.validation.Valid;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Operation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.*;
//import com.tth.framework.base.BaseController;
//import com.tth.framework.base.BaseService;
//
///**
// * <p>
// * 衣服表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2025-05-25
// */
//@Slf4j
//@RestController
//@Tag(name = "衣服表接口【clothes】", description = "权限前缀：【clothes】")
//@RequestMapping("/clothes")
//public class ClothesController extends BaseController<Clothes> {
//
//    @Resource
//    private ClothesService clothesService;
//
//    @Override
//    protected String getModuleName() {
//        return "clothes";
//    }
//
//    @Override
//    public BaseService<Clothes> getBaseService() {
//        return clothesService;
//    }
//
//    @SaIgnore
//    @ApiLog(module = "衣服表", value = "添加衣服")
//    @PostMapping("/add")
//    @Operation(summary = "添加衣服", description = "APP用户添加衣服")
//    public R<Clothes> add(@RequestBody @Valid AddClothesDTO addClothesDTO) throws Exception {
//        log.info("用户添加衣服，参数：{}", addClothesDTO);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层添加方法
//        Clothes clothes = clothesService.add(addClothesDTO, 10L);
//
//        log.info("用户添加衣服成功，衣服ID：{}", clothes.getId());
//        return R.success(clothes);
//    }
//
//    @SaIgnore
//    @PostMapping("/remove/{id}")
//    @Operation(summary = "删除衣服", description = "APP用户删除衣服")
//    public R<Boolean> remove(@PathVariable Long id) throws Exception {
//        log.info("用户删除衣服，衣服ID：{}", id);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层删除方法
//        boolean deleted = clothesService.removeByUser(id, currentUserId);
//
//        if (!deleted) {
//            return R.fail("衣服不存在或无权删除");
//        }
//
//        log.info("用户删除衣服成功，衣服ID：{}", id);
//        return R.success();
//    }
//
//    @SaIgnore
//    @PostMapping("/toggleSofa/{id}")
//    @Operation(summary = "放到沙发/从沙发取回", description = "APP用户将衣服放到沙发或从沙发取回")
//    public R<Boolean> toggleSofa(@PathVariable Long id, @RequestParam boolean onSofa) throws Exception {
//        log.info("用户{}衣服，衣服ID：{}", onSofa ? "放到沙发" : "从沙发取回", id);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层操作方法
//        boolean success = clothesService.toggleSofa(id, currentUserId, onSofa);
//
//        if (!success) {
//            return R.fail("衣服不存在或无权操作");
//        }
//
//        log.info("用户{}衣服成功，衣服ID：{}", onSofa ? "放到沙发" : "从沙发取回", id);
//        return R.success();
//    }
//
//    @SaIgnore
//    @PostMapping("/list")
//    @Operation(summary = "分页查询衣服列表", description = "APP用户分页查询自己的衣服列表（包含图片URL）")
//    public R<PageResult<ClothesListVO>> list(@RequestBody Paging paging) throws Exception {
//        log.info("用户查询衣服列表，分页参数：{}", paging);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层查询方法
//        PageResult<ClothesListVO> result = clothesService.list(paging, currentUserId);
//
//        log.info("用户查询衣服列表成功，共{}条记录", result.getTotal());
//        return R.success(result);
//    }
//
//    @SaIgnore
//    @PostMapping("/listByWardrobe/{wardrobeId}")
//    @Operation(summary = "根据衣柜ID分页查询衣服列表", description = "APP用户根据衣柜ID分页查询自己的衣服列表（包含图片URL）")
//    public R<PageResult<ClothesListVO>> listByWardrobeId(@PathVariable Long wardrobeId, @RequestBody Paging paging) throws Exception {
//        log.info("用户根据衣柜ID查询衣服列表，衣柜ID：{}，分页参数：{}", wardrobeId, paging);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层查询方法
//        PageResult<ClothesListVO> result = clothesService.listByWardrobeId(wardrobeId, paging, 10L);
//
//        log.info("用户根据衣柜ID查询衣服列表成功，衣柜ID：{}，共{}条记录", wardrobeId, result.getTotal());
//        return R.success(result);
//    }
//
//    @SaIgnore
//    @GetMapping("/get/{id}")
//    @Operation(summary = "获取衣服详情", description = "APP用户获取衣服详情")
//    public R<ClothesDetailVO> get(@PathVariable Long id) throws Exception {
//        log.info("用户获取衣服详情，衣服ID：{}", id);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 获取衣服详情
//        ClothesDetailVO detailVO = clothesService.getClothesDetail(id, currentUserId);
//
//        log.info("用户获取衣服详情成功，衣服ID：{}", id);
//        return R.success(detailVO);
//    }
//}
