package com.tth.modules.clothes.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.base.BaseEntity;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import com.tth.modules.wardrobe.enums.ClothesTypeEnum;
import com.tth.modules.wardrobe.enums.SeasonEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 衣服表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_clothes")
@Schema(name = "Clothes", description = "衣服表")
public class Clothes extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userId;

    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣柜ID（关联tth_wardrobe）")
    private Long wardrobeId;

    @Schema(description = "衣柜区域ID（关联tth_wardrobe_area）")
    private Long wardrobeAreaId;

    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣服图片ID（关联tth_oss_file）")
    private Long imageId;

    @Schema(description = "衣服名称")
    private String clothesName;

    @Schema(description = "标签（JSON格式存储多个标签）")
    private String tags;

    @Schema(description = "购买价格")
    private BigDecimal purchasePrice;

    @Schema(description = "购买时间")
    private LocalDate purchaseTime;

    @Schema(description = "购买地点")
    private String purchaseLocation;

    @Schema(description = "衣服类型（1-裙子，2-衬衫，3-T恤，4-袜子，5-裤子，6-外套，7-毛衣，8-牛仔裤，9-短裤，10-内衣，11-睡衣，12-运动服，13-正装，14-鞋子，15-帽子，16-围巾，17-手套，18-腰带，99-其他）")
    private ClothesTypeEnum clothesType;

    @Schema(description = "衣服的故事")
    private String story;

    @Schema(description = "适合的季节（1-春季，2-夏季，3-秋季，4-冬季，5-四季）")
    private SeasonEnum season;

    @Schema(description = "颜色（中文显示）")
    private String color;

    @Schema(description = "是否在沙发（0-否，1-是）")
    private Boolean onSofa;

    @Schema(description = "开始在沙发的时间")
    private LocalDate sofaStartTime;

    @Schema(description = "排序")
    private Integer sort;
}
