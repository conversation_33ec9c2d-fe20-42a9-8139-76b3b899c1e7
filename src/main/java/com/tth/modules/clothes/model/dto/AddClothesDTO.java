package com.tth.modules.clothes.model.dto;

import com.tth.modules.wardrobe.enums.ClothesTypeEnum;
import com.tth.modules.wardrobe.enums.SeasonEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 添加衣服请求DTO
 */
@Data
@Schema(description = "添加衣服请求")
public class AddClothesDTO {

    @NotNull(message = "衣柜ID不能为空")
    @Schema(description = "衣柜ID（关联tth_wardrobe）", required = true)
    private Long wardrobeId;

    @Schema(description = "衣柜区域ID（关联tth_wardrobe_area）")
    private Long wardrobeAreaId;

    @NotNull(message = "衣服图片不能为空")
    @Schema(description = "衣服图片ID（关联tth_oss_file）", required = true)
    private Long imageId;

    @Schema(description = "衣服名称")
    private String clothesName;

    @Schema(description = "标签列表")
    private List<String> tags;

    @Schema(description = "购买价格")
    private BigDecimal purchasePrice;

    @Schema(description = "购买时间")
    private LocalDate purchaseTime;

    @Schema(description = "购买地点")
    private String purchaseLocation;

    @Schema(description = "衣服类型")
    private ClothesTypeEnum clothesType;

    @Schema(description = "衣服的故事")
    private String story;

    @Schema(description = "适合的季节")
    private SeasonEnum season;

    @Schema(description = "颜色（中文显示）")
    private String color;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;
}
