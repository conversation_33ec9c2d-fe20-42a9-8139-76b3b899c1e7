package com.tth.modules.clothes.model.vo;

import com.tth.framework.annotation.MarkFileUrl;
import com.tth.modules.wardrobe.enums.ClothesTypeEnum;
import com.tth.modules.wardrobe.enums.SeasonEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣服详情响应VO
 */
@Data
@Schema(description = "衣服详情响应")
public class ClothesDetailVO {

    @Schema(description = "衣服ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "衣柜ID")
    private Long wardrobeId;

    @Schema(description = "衣柜名称")
    private String wardrobeName;

    @Schema(description = "衣柜区域ID")
    private Long wardrobeAreaId;

    @Schema(description = "衣柜区域名称")
    private String wardrobeAreaName;

    /**
     * 衣服图片ID
     * 序列化结果: {"fileId": 123, "url": "https://..."}
     */
    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣服图片信息")
    private Long imageId;

    @Schema(description = "衣服名称")
    private String clothesName;

    @Schema(description = "标签列表")
    private List<String> tags;

    @Schema(description = "购买价格")
    private BigDecimal purchasePrice;

    @Schema(description = "购买时间")
    private LocalDate purchaseTime;

    @Schema(description = "购买地点")
    private String purchaseLocation;

    @Schema(description = "衣服类型")
    private ClothesTypeEnum clothesType;

    @Schema(description = "衣服的故事")
    private String story;

    @Schema(description = "适合的季节")
    private SeasonEnum season;

    @Schema(description = "颜色（中文显示）")
    private String color;

    @Schema(description = "是否在沙发")
    private Boolean isOnSofa;

    @Schema(description = "开始在沙发的时间")
    private LocalDateTime sofaStartTime;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;
}
