package com.tth.modules.clothes.service;

import com.tth.modules.clothes.entity.Clothes;
import com.tth.framework.base.BaseService;
import com.tth.modules.clothes.model.dto.AddClothesDTO;
import com.tth.modules.clothes.model.vo.ClothesDetailVO;
import com.tth.modules.clothes.model.vo.ClothesListVO;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;

/**
 * <p>
 * 衣服表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-25
 */
public interface ClothesService extends BaseService<Clothes> {

    /**
     * 添加用户的衣服
     * @param addClothesDTO 添加衣服请求DTO
     * @param userId 用户ID
     * @return 创建的衣服
     * @throws Exception 异常
     */
    Clothes add(AddClothesDTO addClothesDTO, Long userId) throws Exception;

    /**
     * 删除用户的衣服
     * @param id 衣服ID
     * @param userId 用户ID
     * @return 是否删除成功
     * @throws Exception 异常
     */
    boolean removeByUser(Long id, Long userId) throws Exception;

    /**
     * 将衣服放到沙发或从沙发取回
     * @param id 衣服ID
     * @param userId 用户ID
     * @param onSofa 是否放到沙发（true-放到沙发，false-从沙发取回）
     * @return 是否操作成功
     * @throws Exception 异常
     */
    boolean toggleSofa(Long id, Long userId, boolean onSofa) throws Exception;

    /**
     * 分页查询用户的衣服列表
     * @param paging 分页参数
     * @param userId 用户ID
     * @return 衣服列表
     * @throws Exception 异常
     */
    PageResult<ClothesListVO> list(Paging paging, Long userId) throws Exception;

    /**
     * 根据ID获取衣服详情
     * @param id 衣服ID
     * @param userId 用户ID（用于权限校验）
     * @return 衣服详情
     * @throws Exception 异常
     */
    ClothesDetailVO getClothesDetail(Long id, Long userId) throws Exception;

    /**
     * 根据衣柜ID分页查询衣服列表
     * @param wardrobeId 衣柜ID
     * @param paging 分页参数
     * @param userId 用户ID（用于权限校验）
     * @return 衣服列表
     * @throws Exception 异常
     */
    PageResult<ClothesListVO> listByWardrobeId(Long wardrobeId, Paging paging, Long userId) throws Exception;
}
