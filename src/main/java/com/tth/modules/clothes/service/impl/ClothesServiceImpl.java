package com.tth.modules.clothes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tth.common.utils.JacksonUtil;
import com.tth.framework.paging.Condition;
import com.tth.modules.clothes.entity.Clothes;
import com.tth.modules.clothes.mapper.ClothesMapper;
import com.tth.modules.clothes.service.ClothesService;
import com.tth.modules.clothes.model.dto.AddClothesDTO;
import com.tth.modules.clothes.model.vo.ClothesDetailVO;
import com.tth.modules.clothes.model.vo.ClothesListVO;
import com.tth.framework.base.BaseServiceImpl;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.exception.core.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 衣服表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-25
 */
@Slf4j
@Service
public class ClothesServiceImpl extends BaseServiceImpl<ClothesMapper, Clothes> implements ClothesService {

    @Override
    public Clothes add(AddClothesDTO addClothesDTO, Long userId) throws Exception {
        log.info("用户{}添加衣服，参数：{}", userId, addClothesDTO);

        // 创建衣服实体
        Clothes clothes = new Clothes();
        BeanUtil.copyProperties(addClothesDTO, clothes);

        // 设置用户ID
        clothes.setUserId(userId);

        // 处理标签：将List<String>转换为JSON字符串
        if (CollUtil.isNotEmpty(addClothesDTO.getTags())) {
            clothes.setTags(JSONUtil.toJsonStr(addClothesDTO.getTags()));
        }

        // 设置默认值
        if (clothes.getOnSofa() == null) {
            clothes.setOnSofa(false);
        }
        if (clothes.getSort() == null) {
            clothes.setSort(0);
        }

        // 保存到数据库
        this.save(clothes);

        log.info("用户{}添加衣服成功，衣服ID：{}", userId, clothes.getId());
        return clothes;
    }

    @Override
    public boolean removeByUser(Long id, Long userId) throws Exception {
        log.info("用户{}删除衣服，衣服ID：{}", userId, id);

        // 查询衣服是否存在且属于当前用户
        QueryWrapper<Clothes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id)
                   .eq("user_id", userId)
                   .eq("deleted", false);

        Clothes clothes = this.getOne(queryWrapper);
        if (clothes == null) {
            log.warn("衣服不存在或无权删除，衣服ID：{}，用户ID：{}", id, userId);
            return false;
        }

        // 逻辑删除
        this.deleteById(id);

        log.info("用户{}删除衣服成功，衣服ID：{}", userId, id);
        return true;
    }

    @Override
    public boolean toggleSofa(Long id, Long userId, boolean onSofa) throws Exception {
        log.info("用户{}{}衣服，衣服ID：{}", userId, onSofa ? "放到沙发" : "从沙发取回", id);

        // 查询衣服是否存在且属于当前用户
        QueryWrapper<Clothes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id)
                   .eq("user_id", userId)
                   .eq("deleted", false);

        Clothes clothes = this.getOne(queryWrapper);
        if (clothes == null) {
            log.warn("衣服不存在或无权操作，衣服ID：{}，用户ID：{}", id, userId);
            return false;
        }

        // 更新沙发状态
        UpdateWrapper<Clothes> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                    .eq("user_id", userId)
                    .set("is_on_sofa", onSofa);

        if (onSofa) {
            // 放到沙发时记录时间
            updateWrapper.set("sofa_start_time", LocalDateTime.now());
        } else {
            // 从沙发取回时清空时间
            updateWrapper.set("sofa_start_time", null);
        }

        boolean updated = this.update(updateWrapper);
        if (!updated) {
            throw new BizException(BaseResponseCode.FAIL, "操作失败");
        }

        log.info("用户{}{}衣服成功，衣服ID：{}", userId, onSofa ? "放到沙发" : "从沙发取回", id);
        return true;
    }

    @Override
    public PageResult<ClothesListVO> list(Paging paging, Long userId) throws Exception {
        log.info("用户{}查询衣服列表，分页参数：{}", userId, paging);

        // 添加用户ID查询条件到Paging对象
        if (paging.getConditions() == null) {
            paging.setConditions(new ArrayList<>());
        }

        // 添加用户ID条件
        com.tth.framework.paging.Condition userCondition = new com.tth.framework.paging.Condition();
        userCondition.setProperty("t1.userId");
        userCondition.setValue(userId);
        paging.getConditions().add(userCondition);

        // 执行分页查询
        PageResult<Clothes> pageResult = this.listOrPage(paging);

        // 转换为VO
        List<ClothesListVO> voList = pageResult.getList().stream()
                .map(this::convertToListVO)
                .collect(Collectors.toList());

        PageResult<ClothesListVO> result = new PageResult<>();
        result.setList(voList);
        result.setTotal(pageResult.getTotal());
        result.setPageNum(pageResult.getPageNum());
        result.setPageSize(pageResult.getPageSize());

        log.info("用户{}查询衣服列表成功，共{}条记录", userId, result.getTotal());
        return result;
    }

    @Override
    public ClothesDetailVO getClothesDetail(Long id, Long userId) throws Exception {
        log.info("用户{}获取衣服详情，衣服ID：{}", userId, id);

        // 查询衣服是否存在且属于当前用户
        QueryWrapper<Clothes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id)
                   .eq("user_id", userId)
                   .eq("deleted", false);

        Clothes clothes = this.getOne(queryWrapper);
        if (clothes == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "衣服不存在或无权查看");
        }

        // 转换为VO
        ClothesDetailVO detailVO = convertToDetailVO(clothes);

        log.info("用户{}获取衣服详情成功，衣服ID：{}", userId, id);
        return detailVO;
    }

    /**
     * 转换为列表VO
     */
    private ClothesListVO convertToListVO(Clothes clothes) {
        ClothesListVO vo = new ClothesListVO();
        BeanUtil.copyProperties(clothes, vo);

        // 处理标签：将JSON字符串转换为List<String>
        if (clothes.getTags() != null) {
            List<String> tags = JacksonUtil.toList(clothes.getTags(), String.class);
            if (tags.isEmpty()) {
                log.warn("解析标签失败，衣服ID：{}，标签内容：{}", clothes.getId(), clothes.getTags());
            }
            vo.setTags(tags);
        }

        return vo;
    }

    /**
     * 转换为详情VO
     */
    private ClothesDetailVO convertToDetailVO(Clothes clothes) {
        ClothesDetailVO vo = new ClothesDetailVO();
        BeanUtil.copyProperties(clothes, vo);

        // 处理标签：将JSON字符串转换为List<String>
        if (clothes.getTags() != null) {
            List<String> tags = JacksonUtil.toList(clothes.getTags(), String.class);
            if (tags.isEmpty()) {
                log.warn("解析标签失败，衣服ID：{}，标签内容：{}", clothes.getId(), clothes.getTags());
            }
            vo.setTags(tags);
        }

        return vo;
    }

    @Override
    public PageResult<ClothesListVO> listByWardrobeId(Long wardrobeId, Paging paging, Long userId) throws Exception {
        log.info("用户{}根据衣柜ID查询衣服列表，衣柜ID：{}，分页参数：{}", userId, wardrobeId, paging);

        // 添加查询条件到Paging对象
        if (paging.getConditions() == null) {
            paging.setConditions(new ArrayList<>());
        }

        // 添加用户ID条件
        Condition userCondition = new Condition("t1.userId", userId);
        paging.getConditions().add(userCondition);

        // 添加衣柜ID条件
        Condition wardrobeCondition = new Condition("t1.wardrobeId", wardrobeId);
        paging.getConditions().add(wardrobeCondition);

        // 执行分页查询
        PageResult<Clothes> pageResult = this.listOrPage(paging);

        // 转换为VO
        List<ClothesListVO> voList = pageResult.getList().stream()
                .map(this::convertToListVO)
                .collect(Collectors.toList());

        PageResult<ClothesListVO> result = new PageResult<>();
        result.setList(voList);
        result.setTotal(pageResult.getTotal());
        result.setPageNum(pageResult.getPageNum());
        result.setPageSize(pageResult.getPageSize());

        log.info("用户{}根据衣柜ID查询衣服列表成功，衣柜ID：{}，共{}条记录", userId, wardrobeId, result.getTotal());
        return result;
    }
}
