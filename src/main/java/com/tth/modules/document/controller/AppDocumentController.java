package com.tth.modules.document.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.common.enums.ActiveStatusEnum;
import com.tth.modules.document.service.AppDocumentService;
import com.tth.modules.document.entity.AppDocument;
import com.tth.modules.document.enums.AppDocumentTypeEnum;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.response.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;



/**
 * <p>
 * 应用文档表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Slf4j
@RestController
@Tag(name = "应用文档表接口【appDocument】", description = "权限前缀：【appDocument】")
@RequestMapping("/appDocument")
public class AppDocumentController extends BaseController<AppDocument> {

    @Resource
    private AppDocumentService appDocumentService;

    @Override
    protected String getModuleName() {
        return "appDocument";
    }

    @Override
    public BaseService<AppDocument> getBaseService() {
        return appDocumentService;
    }

    @SaIgnore
    @Operation(summary = "根据文档类型查询已启用的文档（公共接口）", description = "无需权限验证的公共接口")
    @GetMapping("/queryByDocumentType")
    public R<AppDocument> queryByDocumentType(@RequestParam AppDocumentTypeEnum documentType) {
        AppDocument document = appDocumentService.queryActiveByDocumentType(documentType);
        return R.success(document);
    }

    @ApiLog(module = "应用文档模块", value = "启用/取消启用文档")
    @Operation(summary = "启用/取消启用文档", description = "权限后缀：【:update】")
    @PostMapping("/activate")
    public R<String> activateDocument(@RequestParam Long id, @RequestParam ActiveStatusEnum isActive) {
        checkPermission("update");

        appDocumentService.activateDocument(id, isActive);

        String message = ActiveStatusEnum.ACTIVE.equals(isActive) ? "文档启用成功" : "文档取消启用成功";
        return R.success(message);
    }
}
