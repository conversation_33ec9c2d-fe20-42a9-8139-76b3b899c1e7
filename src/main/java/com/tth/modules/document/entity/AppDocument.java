package com.tth.modules.document.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.ActiveStatusEnum;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.document.enums.AppDocumentStorageTypeEnum;
import com.tth.modules.document.enums.AppDocumentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应用文档表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_app_document")
@Schema(name = "AppDocument", description = "应用文档表")
public class AppDocument extends BaseEntity {

    @Schema(description = "文档类型：1-用户协议,2-隐私政策,3-服务条款,4-关于我们,5-帮助中心,6-联系我们")
    private AppDocumentTypeEnum documentType;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "存储类型：1-富文本内容,2-文档文件")
    private AppDocumentStorageTypeEnum storageType;

    @Schema(description = "富文本内容（storage_type=1时使用）")
    private String content;

    @MarkFileUrl
    @Schema(description = "关联OSS文件ID（storage_type=2时使用）")
    private Long ossFileId;

    @Schema(description = "文档版本号")
    private String documentVersion;

    @Schema(description = "语言")
    private String language;

    @Schema(description = "是否激活：0-否,1-是")
    private ActiveStatusEnum isActive;
}
