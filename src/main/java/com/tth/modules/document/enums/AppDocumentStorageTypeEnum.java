package com.tth.modules.document.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文档存储类型枚举
 */
@Getter
@AllArgsConstructor
@MarkDictType("文档存储类型")
@Schema(description = "文档存储类型枚举")
public enum AppDocumentStorageTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "富文本内容")
    RICH_TEXT(1, "富文本内容"),

    @Schema(description = "文档文件")
    FILE_URL(2, "文档文件");

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
