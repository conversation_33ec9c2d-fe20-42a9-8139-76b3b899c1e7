package com.tth.modules.document.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应用文档类型枚举
 */
@Getter
@AllArgsConstructor
@MarkDictType("应用文档类型")
@Schema(description = "应用文档类型枚举")
public enum AppDocumentTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "用户协议")
    USER_AGREEMENT(1, "用户协议"),

    @Schema(description = "隐私政策")
    PRIVACY_POLICY(2, "隐私政策"),

    @Schema(description = "服务条款")
    TERMS_OF_SERVICE(3, "服务条款"),

    @Schema(description = "关于我们")
    ABOUT_US(4, "关于我们"),

    @Schema(description = "帮助中心")
    HELP_CENTER(5, "帮助中心"),

    @Schema(description = "联系我们")
    CONTACT_US(6, "联系我们");

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
