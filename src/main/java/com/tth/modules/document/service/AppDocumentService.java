package com.tth.modules.document.service;

import com.tth.common.enums.ActiveStatusEnum;
import com.tth.modules.document.entity.AppDocument;
import com.tth.modules.document.enums.AppDocumentTypeEnum;
import com.tth.framework.base.BaseService;

import java.util.List;

/**
 * <p>
 * 应用文档表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
public interface AppDocumentService extends BaseService<AppDocument> {

    /**
     * 根据文档类型查询已激活的文档（公共接口）
     * @param documentType 文档类型
     * @return 已激活的文档，如果没有则返回null
     */
    AppDocument queryActiveByDocumentType(AppDocumentTypeEnum documentType);

    /**
     * 激活文档
     * 如果激活，则将同类型的其他文档设为未激活
     * 如果取消激活，则仅将当前文档设为未激活
     * @param id 文档ID
     * @param isActive 是否激活
     */
    void activateDocument(Long id, ActiveStatusEnum isActive);

}
