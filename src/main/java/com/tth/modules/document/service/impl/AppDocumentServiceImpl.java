package com.tth.modules.document.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tth.common.enums.ActiveStatusEnum;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.modules.document.entity.AppDocument;
import com.tth.modules.document.enums.AppDocumentTypeEnum;
import com.tth.modules.document.mapper.AppDocumentMapper;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.document.service.AppDocumentService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 应用文档表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Slf4j
@Service
public class AppDocumentServiceImpl extends BaseServiceImpl<AppDocumentMapper, AppDocument> implements AppDocumentService {

    @Resource
    private AppDocumentMapper appDocumentMapper;

    @Override
    public AppDocument queryActiveByDocumentType(AppDocumentTypeEnum documentType) {
        LambdaQueryWrapper<AppDocument> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppDocument::getDocumentType, documentType)
                   .eq(AppDocument::getIsActive, ActiveStatusEnum.ACTIVE)
                   .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void activateDocument(Long id, ActiveStatusEnum isActive) {
        // 获取当前文档
        AppDocument document = this.getById(id);
        if (document == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "文档不存在");
        }

        if (ActiveStatusEnum.ACTIVE.equals(isActive)) {
            // 激活操作：先将同类型的其他文档设为未激活
            LambdaUpdateWrapper<AppDocument> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AppDocument::getDocumentType, document.getDocumentType())
                        .ne(AppDocument::getId, id)
                        .set(AppDocument::getIsActive, ActiveStatusEnum.UN_ACTIVE);

            this.update(new AppDocument(), updateWrapper);
        }

        // 更新当前文档的激活状态
        document.setIsActive(isActive);
        this.updateById(document);
    }
}
