package com.tth.modules.fairytale.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.annotation.RepeatSubmit;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.fairytale.service.FairyTaleService;
import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.modules.fairytale.model.dto.AddFairyTaleDTO;
import com.tth.modules.fairytale.model.dto.BatchSynthesizeDTO;
import com.tth.modules.fairytale.model.dto.PublishFairyTaleDTO;
import com.tth.modules.fairytale.model.dto.SynthesizeVoiceDTO;
import com.tth.modules.fairytale.model.dto.TtsCallbackDTO;
import com.tth.modules.fairytale.model.vo.RandomFairyTaleVO;
import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import lombok.extern.slf4j.Slf4j;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;
import com.tth.framework.constants.PermissionConstants;
import com.tth.framework.response.R;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.Valid;

/**
 * <p>
 * 童话故事表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@RestController
@Tag(name = "童话故事表接口【fairyTale】", description = "权限前缀：【fairyTale】")
@RequestMapping("/fairyTale")
public class FairyTaleController extends BaseController<FairyTale> {

    @Resource
    private FairyTaleService fairyTaleService;



    @Override
    protected String getModuleName() {
        return "fairyTale";
    }

    @Override
    public BaseService<FairyTale> getBaseService() {
        return fairyTaleService;
    }

    @RepeatSubmit
    @ApiLog(module = "童话故事模块", value = "添加童话故事")
    @Operation(summary = "添加童话故事", description = "权限后缀：【:" + PermissionConstants.Operation.INSERT + "】")
    @PostMapping("/add")
    public R<FairyTale> add(@RequestBody @Valid AddFairyTaleDTO addFairyTaleDTO) throws Exception {
        FairyTale fairyTale = fairyTaleService.add(addFairyTaleDTO);
        return R.success(fairyTale);
    }

    @RepeatSubmit
    @ApiLog(module = "童话故事模块", value = "翻译童话故事")
    @Operation(summary = "翻译童话故事", description = "权限后缀：【:" + PermissionConstants.Operation.UPDATE + "】")
    @PostMapping("/translate/{id}")
    public R<Void> translate(@PathVariable Long id) {
        fairyTaleService.translate(id);
        return R.successWithMessage("翻译任务已启动，请稍后查看翻译结果");
    }

    @RepeatSubmit
    @ApiLog(module = "童话故事模块", value = "合成语音童话故事")
    @Operation(summary = "语音合成", description = "权限后缀：【:" + PermissionConstants.Operation.UPDATE + "】")
    @PostMapping("/synthesizeVoice")
    public R<Void> synthesizeVoice(@RequestBody @Valid SynthesizeVoiceDTO synthesizeVoiceDTO) {
        fairyTaleService.synthesizeVoice(synthesizeVoiceDTO.getId(), synthesizeVoiceDTO.getTarget());
        return R.successWithMessage("语音合成任务已启动，请稍后查看合成结果");
    }

    @RepeatSubmit
    @ApiLog(module = "童话故事模块", value = "批量合成语音童话故事")
    @Operation(summary = "批量语音合成", description = "权限后缀：【:" + PermissionConstants.Operation.UPDATE + "】")
    @PostMapping("/batchSynthesize")
    public R<Void> batchSynthesize(@RequestBody @Valid BatchSynthesizeDTO batchSynthesizeDTO) {
        fairyTaleService.batchSynthesize(batchSynthesizeDTO);
        return R.successWithMessage("批量语音合成任务已启动，请稍后查看合成结果");
    }

    @RepeatSubmit
    @ApiLog(module = "童话故事模块", value = "查询语音合成结果")
    @Operation(summary = "查询语音合成结果", description = "权限后缀：【:" + PermissionConstants.Operation.QUERY + "】")
    @PostMapping("/querySynthesizeResult")
    public R<Void> querySynthesizeResult(@RequestBody @Valid SynthesizeVoiceDTO synthesizeVoiceDTO) {
        String resultMessage = fairyTaleService.querySynthesizeResult(synthesizeVoiceDTO.getId(), synthesizeVoiceDTO.getTarget());
        return R.successWithMessage(resultMessage);
    }

    @RepeatSubmit
    @ApiLog(module = "童话故事模块", value = "发布童话故事")
    @Operation(summary = "发布童话故事", description = "权限后缀：【:" + PermissionConstants.Operation.UPDATE + "】")
    @PostMapping("/publish")
    public R<Void> publish(@RequestBody @Valid PublishFairyTaleDTO publishFairyTaleDTO) {
        fairyTaleService.publish(publishFairyTaleDTO.getId(), publishFairyTaleDTO.getPublishStatus());
        return R.successWithMessage("发布状态更新成功");
    }

    @SaIgnore
    @ApiLog(module = "童话故事模块", value = "火山引擎语音合成回调")
    @Operation(summary = "火山引擎语音合成回调", description = "火山引擎语音合成回调接口", hidden = true)
    @PostMapping("/volcengine/tts/callback")
    public R<Void> chineseTitleTtsCallback(@RequestBody TtsCallbackDTO callbackDTO, @RequestParam String audioType) {
        log.info("收到火山引擎语音合成回调，参数：{}", callbackDTO);
        fairyTaleService.handleVolcengineTtsCallback(callbackDTO, audioType);
        return R.success();
    }

    @SaIgnore
    @ApiLog(module = "童话故事模块", value = "随机获取童话故事")
    @Operation(summary = "随机获取童话故事", description = "根据语言版本随机获取一个已发布的童话故事，登录用户会返回收藏状态")
    @GetMapping("/random")
    public R<RandomFairyTaleVO> getRandomFairyTale(@RequestParam @NotNull(message = "语言版本不能为空") FairyTaleLanguageVersionEnum languageVersion) {
        RandomFairyTaleVO randomFairyTaleVO = fairyTaleService.getRandomFairyTale(languageVersion);
        if (randomFairyTaleVO == null) {
            return R.successWithMessage("暂无已发布的童话故事");
        }
        return R.success(randomFairyTaleVO);
    }


}
