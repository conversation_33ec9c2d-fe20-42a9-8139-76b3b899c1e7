package com.tth.modules.fairytale.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.annotation.RepeatSubmit;
import com.tth.framework.response.R;
import com.tth.framework.utils.AuthUtil;
import com.tth.modules.fairytale.model.dto.AddFairyTaleFavoriteDTO;
import com.tth.modules.fairytale.model.vo.FairyTaleFavoriteListVO;
import com.tth.modules.fairytale.model.vo.FairyTaleFavoriteDetailVO;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.fairytale.service.FairyTaleFavoriteService;
import com.tth.modules.fairytale.entity.FairyTaleFavorite;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 童话故事收藏表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Slf4j
@RestController
@Tag(name = "童话故事收藏表接口【fairyTaleFavorite】", description = "权限前缀：【fairyTaleFavorite】")
@RequestMapping("/fairyTaleFavorite")
public class FairyTaleFavoriteController extends BaseController<FairyTaleFavorite> {

    @Resource
    private FairyTaleFavoriteService fairyTaleFavoriteService;

    @Override
    protected String getModuleName() {
        return "fairyTaleFavorite";
    }

    @Override
    public BaseService<FairyTaleFavorite> getBaseService() {
        return fairyTaleFavoriteService;
    }

    @RepeatSubmit(interval = 1000)
    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "童话故事收藏模块", value = "童话故事收藏操作")
    @Operation(summary = "童话故事收藏操作", description = "用户添加收藏或取消收藏童话故事")
    @PostMapping("/toggle")
    public R<Void> toggleFavorite(@RequestBody @Valid AddFairyTaleFavoriteDTO addFairyTaleFavoriteDTO) {
        log.info("用户童话故事收藏操作，参数：{}", addFairyTaleFavoriteDTO);

        // 获取当前登录客户用户ID
        Long userBaseId = AuthUtil.getUserId();

        // 调用service层操作方法
        String resultMessage = fairyTaleFavoriteService.toggleFavorite(addFairyTaleFavoriteDTO, userBaseId);

        log.info("用户童话故事收藏操作成功：{}", resultMessage);
        return R.successWithMessage(resultMessage);
    }

    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "童话故事收藏模块", value = "查询收藏列表")
    @Operation(summary = "查询收藏的童话故事列表", description = "用户查询自己收藏的童话故事列表，默认每页10条")
    @PostMapping("/list")
    public R<PageResult<FairyTaleFavoriteListVO>> getFavoriteList(@RequestBody Paging paging) {
        log.info("用户查询收藏的童话故事列表，分页参数：{}", paging);

        // 获取当前登录用户ID
        Long userBaseId = AuthUtil.getUserId();

        // 调用service层查询方法
        PageResult<FairyTaleFavoriteListVO> result = fairyTaleFavoriteService.getFavoriteList(paging, userBaseId);

        log.info("用户查询收藏的童话故事列表成功，共{}个故事", result.getList().size());
        return R.success(result);
    }

    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "童话故事收藏模块", value = "根据故事ID查询收藏记录详情")
    @Operation(summary = "根据故事ID查询收藏记录详情", description = "用户根据故事ID查询自己的收藏记录详情，包含故事内容和上下篇导航")
    @GetMapping("/getFavoriteById")
    public R<FairyTaleFavoriteDetailVO> getFavoriteById(@RequestParam Long fairyTaleId) {
        log.info("用户根据故事ID查询收藏记录详情，故事ID：{}", fairyTaleId);

        // 获取当前登录用户ID
        Long userBaseId = AuthUtil.getUserId();

        // 调用service层查询方法
        FairyTaleFavoriteDetailVO favoriteDetail = fairyTaleFavoriteService.getFavoriteDetailById(fairyTaleId, userBaseId);

        log.info("用户根据故事ID查询收藏记录详情成功，故事ID：{}", fairyTaleId);
        return R.success(favoriteDetail);
    }
}
