package com.tth.modules.fairytale.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.annotation.MarkMultiFileUrl;
import com.tth.framework.base.BaseEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.tth.modules.fairytale.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 童话故事表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_fairy_tale")
@Schema(name = "FairyTale", description = "童话故事表")
public class FairyTale extends BaseEntity {

    @Schema(description = "故事编号（格式：YYYYMMDD001）")
    private String storyNumber;

    @Schema(description = "中文童话名称")
    private String chineseTitle;

    @Schema(description = "中文童话内容")
    private String chineseContent;

    @Schema(description = "英文童话名称")
    private String englishTitle;

    @Schema(description = "英文童话内容")
    private String englishContent;

    @Schema(description = "发布状态（0-未发布，1-已发布）")
    private PublishStatusEnum publishStatus;

    @Schema(description = "是否需要英文版")
    private YesNoEnum needEnglishVersion;

    @Schema(description = "是否需要双语版")
    private YesNoEnum needBilingualVersion;

    @Schema(description = "火山引擎精品长文本语音合成接口版本（1-普通版，2-情感预测版）")
    private ApiVersionEnum apiVersion;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文标题音频文件ID（关联tth_oss_file）")
    private Long chineseTitleAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文内容音频文件ID（关联tth_oss_file）")
    private Long chineseContentAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文标题+内容合成音频文件ID（关联tth_oss_file）")
    private Long chineseTitleContentAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文标题音频文件ID（关联tth_oss_file）")
    private Long englishTitleAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文内容音频文件ID（关联tth_oss_file）")
    private Long englishContentAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文标题+内容合成音频文件ID（关联tth_oss_file）")
    private Long englishTitleContentAudioId;

    @Schema(description = "英文翻译状态（0-待翻译，1-翻译中，2-翻译成功，3-翻译失败）")
    private TranslationStatusEnum translationStatus;

    @Schema(description = "翻译开始时间")
    private LocalDateTime translationStartTime;

    @Schema(description = "翻译完成时间")
    private LocalDateTime translationCompleteTime;

    @Schema(description = "中文标题语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败）")
    private TtsStatusEnum chineseTitleTtsStatus;

    @Schema(description = "中文标题语音合成开始时间")
    private LocalDateTime chineseTitleTtsStartTime;

    @Schema(description = "中文标题语音合成完成时间")
    private LocalDateTime chineseTitleTtsCompleteTime;

    @Schema(description = "中文标题语音合成任务ID")
    private String chineseTitleTaskId;

    @Schema(description = "中文标题语音合成请求ID")
    private String chineseTitleRequestId;

    @Schema(description = "中文内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败）")
    private TtsStatusEnum chineseContentTtsStatus;

    @Schema(description = "中文内容语音合成开始时间")
    private LocalDateTime chineseContentTtsStartTime;

    @Schema(description = "中文内容语音合成完成时间")
    private LocalDateTime chineseContentTtsCompleteTime;

    @Schema(description = "中文内容语音合成任务ID")
    private String chineseContentTaskId;

    @Schema(description = "中文内容语音合成请求ID")
    private String chineseContentRequestId;

    @Schema(description = "中文标题+内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败）")
    private TtsStatusEnum chineseTitleContentTtsStatus;

    @Schema(description = "中文标题+内容语音合成开始时间")
    private LocalDateTime chineseTitleContentTtsStartTime;

    @Schema(description = "中文标题+内容语音合成完成时间")
    private LocalDateTime chineseTitleContentTtsCompleteTime;

    @Schema(description = "中文标题+内容语音合成任务ID")
    private String chineseTitleContentTaskId;

    @Schema(description = "中文标题+内容语音合成请求ID")
    private String chineseTitleContentRequestId;

    @Schema(description = "英文标题语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败）")
    private TtsStatusEnum englishTitleTtsStatus;

    @Schema(description = "英文标题语音合成开始时间")
    private LocalDateTime englishTitleTtsStartTime;

    @Schema(description = "英文标题语音合成完成时间")
    private LocalDateTime englishTitleTtsCompleteTime;

    @Schema(description = "英文标题语音合成任务ID")
    private String englishTitleTaskId;

    @Schema(description = "英文标题语音合成请求ID")
    private String englishTitleRequestId;

    @Schema(description = "英文内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败）")
    private TtsStatusEnum englishContentTtsStatus;

    @Schema(description = "英文内容语音合成开始时间")
    private LocalDateTime englishContentTtsStartTime;

    @Schema(description = "英文内容语音合成完成时间")
    private LocalDateTime englishContentTtsCompleteTime;

    @Schema(description = "英文内容语音合成任务ID")
    private String englishContentTaskId;

    @Schema(description = "英文内容语音合成请求ID")
    private String englishContentRequestId;

    @Schema(description = "英文标题+内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败）")
    private TtsStatusEnum englishTitleContentTtsStatus;

    @Schema(description = "英文标题+内容语音合成开始时间")
    private LocalDateTime englishTitleContentTtsStartTime;

    @Schema(description = "英文标题+内容语音合成完成时间")
    private LocalDateTime englishTitleContentTtsCompleteTime;

    @Schema(description = "英文标题+内容语音合成任务ID")
    private String englishTitleContentTaskId;

    @Schema(description = "英文标题+内容语音合成请求ID")
    private String englishTitleContentRequestId;

    @Schema(description = "中文标题语音合成结果（JSON格式）")
    private String chineseTitleTtsResult;

    @Schema(description = "中文内容语音合成结果（JSON格式）")
    private String chineseContentTtsResult;

    @Schema(description = "中文标题+内容语音合成结果（JSON格式）")
    private String chineseTitleContentTtsResult;

    @Schema(description = "英文标题语音合成结果（JSON格式）")
    private String englishTitleTtsResult;

    @Schema(description = "英文内容语音合成结果（JSON格式）")
    private String englishContentTtsResult;

    @Schema(description = "英文标题+内容语音合成结果（JSON格式）")
    private String englishTitleContentTtsResult;

    @Schema(description = "中文语音音色类型，标题和内容使用同一音色类型")
    private String chineseVoiceType;

    @Schema(description = "英文语音音色类型，标题和内容使用同一音色类型")
    private String englishVoiceType;

    @Schema(description = "音频格式（mp3、wav、pcm、flac），中英文统一音频格式")
    private AudioFormatEnum audioFormat;

    @Schema(description = "音频采样率，中英文统一音频采样率")
    private SampleRateEnum sampleRate;

    @Schema(description = "中文童话语音情感，标题和内容使用同一情感")
    private String chineseVoiceStyle;

    @Schema(description = "英文童话语音情感，标题和内容使用同一情感")
    private String englishVoiceStyle;

    @Schema(description = "中文语音音量（0.1-3.0）")
    private BigDecimal chineseVolume;

    @Schema(description = "中文语音语速（0.2-3.0）")
    private BigDecimal chineseSpeed;

    @Schema(description = "中文语音语调（0.1-3.0）")
    private BigDecimal chinesePitch;

    @Schema(description = "中文句间停顿时长（毫秒，0-3000）")
    private Integer chineseSentenceInterval;

    @Schema(description = "英文语音音量（0.1-3.0）")
    private BigDecimal englishVolume;

    @Schema(description = "英文语音语速（0.2-3.0）")
    private BigDecimal englishSpeed;

    @Schema(description = "英文语音语调（0.1-3.0）")
    private BigDecimal englishPitch;

    @Schema(description = "英文句间停顿时长（毫秒，0-3000）")
    private Integer englishSentenceInterval;

    @Schema(description = "中文版收藏数")
    private Integer chineseFavoriteCount;

    @Schema(description = "英文版收藏数")
    private Integer englishFavoriteCount;

    @Schema(description = "双语版收藏数")
    private Integer bilingualFavoriteCount;

    @Schema(description = "第三方服务应用ID")
    private String appId;

    @MarkMultiFileUrl(expireMinutes = 600)
    @Schema(description = "中文故事截图（多个图片ID，逗号分隔）")
    private String chineseScreenshots;

    @MarkMultiFileUrl(expireMinutes = 600)
    @Schema(description = "英文故事截图（多个图片ID，逗号分隔）")
    private String englishScreenshots;

    @Schema(description = "最近合成时间")
    private LocalDateTime lastSynthesizeTime;
}
