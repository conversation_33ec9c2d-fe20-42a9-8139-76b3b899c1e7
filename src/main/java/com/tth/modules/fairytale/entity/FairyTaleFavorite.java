package com.tth.modules.fairytale.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 童话故事收藏表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_fairy_tale_favorite")
@Schema(name = "FairyTaleFavorite", description = "童话故事收藏表")
public class FairyTaleFavorite extends BaseEntity {

    @Schema(description = "C端用户ID（关联tth_customer_user）")
    private Long userBaseId;

    @Schema(description = "童话故事ID（关联tth_fairy_tale）")
    private Long fairyTaleId;

    @Schema(description = "收藏的童话故事语言版本")
    private FairyTaleLanguageVersionEnum fairyTaleLanguageVersion;
}
