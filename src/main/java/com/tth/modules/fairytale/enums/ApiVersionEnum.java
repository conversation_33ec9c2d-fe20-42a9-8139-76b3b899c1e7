package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("火山引擎语音合成API版本")
@Schema(description = "火山引擎语音合成API版本")
public enum ApiVersionEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "普通版")
    NORMAL(1, "普通版"),

    @Schema(description = "情感预测版")
    EMOTION_PREDICTION(2, "情感预测版"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
