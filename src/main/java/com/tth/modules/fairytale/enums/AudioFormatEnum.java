package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("语音合成音频格式")
@Schema(description = "语音合成音频格式")
public enum AudioFormatEnum implements IEnum<String>, IEnumDesc {

    @Schema(description = "MP3")
    MP3("mp3", "MP3"),

    @Schema(description = "WAV")
    WAV("wav", "WAV"),

    @Schema(description = "PCM")
    PCM("pcm", "PCM"),

    @Schema(description = "OGG_OPUS")
    OGG_OPUS("ogg_opus", "OGG_OPUS"),
    ;

    private final String value;
    private final String desc;

    @Override
    public String getValue() {
        return value;
    }
}