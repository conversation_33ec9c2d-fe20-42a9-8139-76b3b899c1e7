package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 童话故事语言版本枚举
 * 用于标识收藏的童话故事语言版本
 */
@Getter
@AllArgsConstructor
@MarkDictType("童话故事语言版本")
@Schema(description = "童话故事语言版本")
public enum FairyTaleLanguageVersionEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "中文版")
    CHINESE(1, "中文版"),

    @Schema(description = "英文版")
    ENGLISH(2, "英文版"),

    @Schema(description = "双语版")
    BILINGUAL(3, "双语版"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
