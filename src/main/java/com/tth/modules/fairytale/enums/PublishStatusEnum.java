package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发布状态枚举
 * 用于标识童话故事的发布状态
 */
@Getter
@AllArgsConstructor
@MarkDictType("发布状态")
@Schema(description = "发布状态")
public enum PublishStatusEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "未发布")
    UNPUBLISHED(0, "未发布"),

    @Schema(description = "已发布")
    PUBLISHED(1, "已发布"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }

}
