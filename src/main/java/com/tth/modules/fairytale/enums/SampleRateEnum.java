package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("音频采样率")
@Schema(description = "音频采样率")
public enum SampleRateEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "8000Hz")
    RATE_8000(8000, "8000Hz"),

    @Schema(description = "16000Hz")
    RATE_16000(16000, "16000Hz"),

    @Schema(description = "22050Hz")
    RATE_22050(22050, "22050Hz"),

    @Schema(description = "24000Hz")
    RATE_24000(24000, "24000Hz"),

    @Schema(description = "32000Hz")
    RATE_32000(32000, "32000Hz"),

    @Schema(description = "44100Hz")
    RATE_44100(44100, "44100Hz"),

    @Schema(description = "48000Hz")
    RATE_48000(48000, "48000Hz"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
