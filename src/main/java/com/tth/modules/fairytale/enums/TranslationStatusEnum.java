package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("翻译状态")
@Schema(description = "翻译状态")
public enum TranslationStatusEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "待翻译")
    PENDING(0, "待翻译"),

    @Schema(description = "翻译中")
    TRANSLATING(1, "翻译中"),

    @Schema(description = "翻译成功")
    SUCCESS(2, "翻译成功"),

    @Schema(description = "翻译失败")
    FAILED(3, "翻译失败"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
