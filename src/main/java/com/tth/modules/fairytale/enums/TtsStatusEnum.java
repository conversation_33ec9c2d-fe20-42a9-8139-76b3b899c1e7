package com.tth.modules.fairytale.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("语音合成状态")
@Schema(description = "语音合成状态")
public enum TtsStatusEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "待合成")
    PENDING(0, "待合成"),

    @Schema(description = "合成中")
    SYNTHESIZING(1, "合成中"),

    @Schema(description = "上传中")
    UPLOADING(2, "上传中"),

    @Schema(description = "合成成功")
    SUCCESS(3, "合成成功"),

    @Schema(description = "合成失败")
    FAILED(4, "合成失败"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
