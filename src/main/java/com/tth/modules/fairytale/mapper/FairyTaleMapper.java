package com.tth.modules.fairytale.mapper;

import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.framework.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 童话故事表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
public interface FairyTaleMapper extends BaseMapper<FairyTale> {

    /**
     * 获取指定日期的最大故事编号
     * @param datePrefix 日期前缀，格式：YYYYMMDD
     * @return 最大编号，如果没有则返回null
     */
    String getMaxStoryNumberByDate(@Param("datePrefix") String datePrefix);

}
