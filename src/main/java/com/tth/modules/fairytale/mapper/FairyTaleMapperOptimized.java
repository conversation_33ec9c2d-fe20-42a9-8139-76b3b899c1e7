package com.tth.modules.fairytale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.modules.fairytale.model.dto.FairyTaleListDTO;
import com.tth.modules.fairytale.model.vo.FairyTaleListVOV3;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 童话故事Mapper（优化版）
 * 专门针对列表查询进行索引优化
 */
@Mapper
public interface FairyTaleMapperOptimized extends BaseMapper<FairyTale> {

    /**
     * 分页查询童话故事列表（索引优化版）
     * 使用覆盖索引避免回表查询
     */
    IPage<FairyTaleListVOV3> selectOptimizedPage(Page<FairyTaleListVOV3> page, @Param("dto") FairyTaleListDTO dto);

    /**
     * 查询已发布的故事列表（最常用查询）
     * 使用 idx_fairy_tale_status_created 索引
     */
    IPage<FairyTaleListVOV3> selectPublishedPage(Page<FairyTaleListVOV3> page);

    /**
     * 按收藏数排序查询（热门故事）
     * 使用收藏数索引
     */
    IPage<FairyTaleListVOV3> selectPopularPage(Page<FairyTaleListVOV3> page, @Param("languageVersion") String languageVersion);

    /**
     * 搜索故事（标题搜索）
     * 使用标题索引
     */
    IPage<FairyTaleListVOV3> searchByTitle(Page<FairyTaleListVOV3> page, @Param("keyword") String keyword);

    /**
     * 查询指定状态的故事数量（用于统计）
     * 使用状态索引
     */
    Long countByStatus(@Param("publishStatus") Integer publishStatus);

    /**
     * 批量查询故事（用于预加载）
     * 使用主键索引
     */
    List<FairyTaleListVOV3> selectBatchByIds(@Param("ids") List<Long> ids);
}
