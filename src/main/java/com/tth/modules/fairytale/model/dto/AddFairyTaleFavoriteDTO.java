package com.tth.modules.fairytale.model.dto;

import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 童话故事收藏操作请求DTO
 */
@Data
@Schema(description = "童话故事收藏操作请求")
public class AddFairyTaleFavoriteDTO {

    @NotNull(message = "童话故事ID不能为空")
    @Schema(description = "童话故事ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fairyTaleId;

    @NotNull(message = "语言版本不能为空")
    @Schema(description = "收藏的童话故事语言版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private FairyTaleLanguageVersionEnum fairyTaleLanguageVersion;

    @NotNull(message = "操作类型不能为空")
    @Schema(description = "操作类型（true-收藏，false-取消收藏）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isFavorite;
}
