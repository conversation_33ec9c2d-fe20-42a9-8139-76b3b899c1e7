package com.tth.modules.fairytale.model.dto;

import com.tth.modules.fairytale.enums.AudioFormatEnum;
import com.tth.modules.fairytale.enums.ApiVersionEnum;
import com.tth.modules.fairytale.enums.SampleRateEnum;
import com.tth.framework.validation.ValidDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量合成语音请求DTO
 */
@Data
@Schema(description = "批量合成语音请求")
public class BatchSynthesizeDTO {

    @NotEmpty(message = "故事ID列表不能为空")
    @Schema(description = "故事ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> storyIds;

    @NotNull(message = "API版本不能为空")
    @Schema(description = "火山引擎精品长文本语音合成接口版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private ApiVersionEnum apiVersion;

    @NotNull(message = "音频格式不能为空")
    @Schema(description = "音频格式", requiredMode = Schema.RequiredMode.REQUIRED)
    private AudioFormatEnum audioFormat;

    @NotBlank(message = "中文语音音色类型不能为空")
    @Schema(description = "中文语音音色类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String chineseVoiceType;

    @NotBlank(message = "英文语音音色类型不能为空")
    @Schema(description = "英文语音音色类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String englishVoiceType;

    @Schema(description = "音频采样率", defaultValue = "RATE_24000")
    private SampleRateEnum sampleRate = SampleRateEnum.RATE_24000;

    @Schema(description = "中文童话语音情感")
    private String chineseVoiceStyle;

    @Schema(description = "英文童话语音情感")
    private String englishVoiceStyle;

    @ValidDecimal(min = "0.1", max = "3.0", scale = 1)
    @Schema(description = "中文语音音量（0.1-3.0，最多1位小数）", defaultValue = "1.0")
    private BigDecimal chineseVolume = BigDecimal.ONE;

    @ValidDecimal(min = "0.2", max = "3.0", scale = 1)
    @Schema(description = "中文语音语速（0.2-3.0，最多1位小数）", defaultValue = "1.0")
    private BigDecimal chineseSpeed = BigDecimal.ONE;

    @ValidDecimal(min = "0.1", max = "3.0", scale = 1)
    @Schema(description = "中文语音语调（0.1-3.0，最多1位小数）", defaultValue = "1.0")
    private BigDecimal chinesePitch = BigDecimal.ONE;

    @Min(value = 0, message = "中文句间停顿时长必须在0-3000毫秒之间")
    @Max(value = 3000, message = "中文句间停顿时长必须在0-3000毫秒之间")
    @Schema(description = "中文句间停顿时长（毫秒，0-3000）", defaultValue = "0")
    private Integer chineseSentenceInterval = 0;

    @ValidDecimal(min = "0.1", max = "3.0", scale = 1)
    @Schema(description = "英文语音音量（0.1-3.0，最多1位小数）", defaultValue = "1.0")
    private BigDecimal englishVolume = BigDecimal.ONE;

    @ValidDecimal(min = "0.2", max = "3.0", scale = 1)
    @Schema(description = "英文语音语速（0.2-3.0，最多1位小数）", defaultValue = "1.0")
    private BigDecimal englishSpeed = BigDecimal.ONE;

    @ValidDecimal(min = "0.1", max = "3.0", scale = 1)
    @Schema(description = "英文语音语调（0.1-3.0，最多1位小数）", defaultValue = "1.0")
    private BigDecimal englishPitch = BigDecimal.ONE;

    @Min(value = 0, message = "英文句间停顿时长必须在0-3000毫秒之间")
    @Max(value = 3000, message = "英文句间停顿时长必须在0-3000毫秒之间")
    @Schema(description = "英文句间停顿时长（毫秒，0-3000）", defaultValue = "0")
    private Integer englishSentenceInterval = 0;
}
