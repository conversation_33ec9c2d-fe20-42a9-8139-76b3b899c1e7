package com.tth.modules.fairytale.model.dto;

import com.tth.modules.fairytale.enums.PublishStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 发布童话故事请求DTO
 */
@Data
@Schema(description = "发布童话故事请求")
public class PublishFairyTaleDTO {

    @NotNull(message = "童话故事ID不能为空")
    @Schema(description = "童话故事ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotNull(message = "发布状态不能为空")
    @Schema(description = "发布状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private PublishStatusEnum publishStatus;
}
