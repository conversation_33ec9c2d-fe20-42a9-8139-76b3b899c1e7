package com.tth.modules.fairytale.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;

/**
 * 语音合成请求DTO
 */
@Data
@Schema(description = "语音合成请求")
public class SynthesizeVoiceDTO {

    @NotNull(message = "童话故事ID不能为空")
    @Schema(description = "童话故事ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotNull(message = "语音合成目标不能为空")
    @Min(value = 1, message = "语音合成目标必须在1-6之间")
    @Max(value = 6, message = "语音合成目标必须在1-6之间")
    @Schema(description = "语音合成目标（1-中文标题，2-中文内容，3-中文标题+内容，4-英文标题，5-英文内容，6-英文标题+内容）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer target;
}
