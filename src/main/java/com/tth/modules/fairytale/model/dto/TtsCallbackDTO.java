package com.tth.modules.fairytale.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * TTS语音合成回调请求DTO
 */
@Data
@Schema(description = "TTS语音合成回调请求")
public class TtsCallbackDTO {

    @Schema(description = "状态码，0表示成功")
    private Integer code;

    @Schema(description = "状态消息")
    private String message;

    @Schema(description = "任务ID")
    private String task_id;

    @Schema(description = "任务状态，1表示成功，2表示失败")
    private Integer task_status;

    @Schema(description = "文本长度")
    private Integer text_length;

    @Schema(description = "音频URL，合成成功时返回")
    private String audio_url;

    @Schema(description = "URL过期时间，合成成功时返回")
    private Long url_expire_time;

    @Schema(description = "句子信息列表，合成成功时返回")
    private List<SentenceInfo> sentences;

    /**
     * 句子信息
     */
    @Data
    @Schema(description = "句子信息")
    public static class SentenceInfo {
        @Schema(description = "句子文本")
        private String text;

        @Schema(description = "原始文本")
        private String origin_text;

        @Schema(description = "段落编号")
        private Integer paragraph_no;

        @Schema(description = "开始时间")
        private Long begin_time;

        @Schema(description = "结束时间")
        private Long end_time;

        @Schema(description = "情感")
        private String emotion;

        @Schema(description = "单词信息列表")
        private List<WordInfo> words;
    }

    /**
     * 单词信息
     */
    @Data
    @Schema(description = "单词信息")
    public static class WordInfo {
        @Schema(description = "单词文本")
        private String text;

        @Schema(description = "开始时间")
        private Long begin;

        @Schema(description = "结束时间")
        private Long end;

        @Schema(description = "音素信息列表")
        private List<PhonemeInfo> phonemes;
    }

    /**
     * 音素信息
     */
    @Data
    @Schema(description = "音素信息")
    public static class PhonemeInfo {
        @Schema(description = "音素")
        private String ph;

        @Schema(description = "开始时间")
        private Long begin;

        @Schema(description = "结束时间")
        private Long end;
    }
}
