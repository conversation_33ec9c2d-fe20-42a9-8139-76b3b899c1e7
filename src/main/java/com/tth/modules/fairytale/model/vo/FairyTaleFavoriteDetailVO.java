package com.tth.modules.fairytale.model.vo;

import com.tth.common.enums.YesNoEnum;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import com.tth.modules.fairytale.enums.PublishStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 童话故事收藏详情响应VO
 */
@Data
@Schema(description = "童话故事收藏详情响应")
public class FairyTaleFavoriteDetailVO {

    @Schema(description = "童话故事ID")
    private Long fairyTaleId;

    @Schema(description = "故事编号（格式：YYYYMMDD001）")
    private String storyNumber;

    @Schema(description = "收藏的语言版本列表")
    private List<FairyTaleLanguageVersionEnum> favoriteLanguageVersions;

    @Schema(description = "上一个收藏故事ID（循环排序），只收藏一个故事时为null")
    private Long previousFairyTaleId;

    @Schema(description = "下一个收藏故事ID（循环排序），只收藏一个故事时为null")
    private Long nextFairyTaleId;

    @Schema(description = "发布状态")
    private PublishStatusEnum publishStatus;

    @Schema(description = "是否需要英文版")
    private YesNoEnum needEnglishVersion;

    @Schema(description = "是否需要双语版")
    private YesNoEnum needBilingualVersion;

    // 故事内容字段
    @Schema(description = "中文标题")
    private String chineseTitle;

    @Schema(description = "中文内容")
    private String chineseContent;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文标题内容音频文件ID")
    private Long chineseTitleContentAudioId;

    @Schema(description = "英文标题")
    private String englishTitle;

    @Schema(description = "英文内容")
    private String englishContent;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文标题内容音频文件ID")
    private Long englishTitleContentAudioId;
}
