package com.tth.modules.fairytale.model.vo;

import com.tth.common.enums.YesNoEnum;
import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import com.tth.modules.fairytale.enums.PublishStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 童话故事收藏列表响应VO
 */
@Data
@Schema(description = "童话故事收藏列表响应")
public class FairyTaleFavoriteListVO {

    @Schema(description = "童话故事ID")
    private Long fairyTaleId;

    @Schema(description = "故事编号（格式：YYYYMMDD001）")
    private String storyNumber;

    @Schema(description = "中文童话名称")
    private String chineseTitle;

    @Schema(description = "英文童话名称")
    private String englishTitle;

    @Schema(description = "中文童话内容")
    private String chineseContent;

    @Schema(description = "发布状态")
    private PublishStatusEnum publishStatus;

    @Schema(description = "是否需要英文版")
    private YesNoEnum needEnglishVersion;

    @Schema(description = "是否需要双语版")
    private YesNoEnum needBilingualVersion;

    @Schema(description = "收藏的语言版本列表")
    private List<FairyTaleLanguageVersionEnum> favoriteLanguageVersions;

    @Schema(description = "收藏时间")
    private LocalDateTime favoriteTime;
}
