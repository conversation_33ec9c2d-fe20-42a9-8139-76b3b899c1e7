package com.tth.modules.fairytale.model.vo;

import com.tth.common.enums.YesNoEnum;
import com.tth.modules.fairytale.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 童话故事列表VO（按需加载版本）
 * 只返回文件ID，前端按需获取文件URL
 */
@Data
@Schema(description = "童话故事列表响应（按需加载版本）")
public class FairyTaleListVOV3 {

    @Schema(description = "故事ID")
    private Long id;

    @Schema(description = "故事编号（格式：YYYYMMDD001）")
    private String storyNumber;

    @Schema(description = "中文童话名称")
    private String chineseTitle;

    @Schema(description = "英文童话名称")
    private String englishTitle;

    @Schema(description = "发布状态（0-未发布，1-已发布）")
    private PublishStatusEnum publishStatus;

    @Schema(description = "是否需要英文版")
    private YesNoEnum needEnglishVersion;

    @Schema(description = "是否需要双语版")
    private YesNoEnum needBilingualVersion;

    // ========== 音频文件ID（前端按需获取URL） ==========
    
    @Schema(description = "中文标题音频文件ID")
    private Long chineseTitleAudioId;

    @Schema(description = "中文内容音频文件ID")
    private Long chineseContentAudioId;

    @Schema(description = "中文标题+内容音频文件ID")
    private Long chineseTitleContentAudioId;

    @Schema(description = "英文标题音频文件ID")
    private Long englishTitleAudioId;

    @Schema(description = "英文内容音频文件ID")
    private Long englishContentAudioId;

    @Schema(description = "英文标题+内容音频文件ID")
    private Long englishTitleContentAudioId;

    // ========== 截图文件ID（逗号分隔） ==========
    
    @Schema(description = "中文故事截图文件ID列表（逗号分隔）")
    private String chineseScreenshots;

    @Schema(description = "英文故事截图文件ID列表（逗号分隔）")
    private String englishScreenshots;

    // ========== 其他业务字段 ==========
    
    @Schema(description = "中文版收藏数")
    private Integer chineseFavoriteCount;

    @Schema(description = "英文版收藏数")
    private Integer englishFavoriteCount;

    @Schema(description = "双语版收藏数")
    private Integer bilingualFavoriteCount;

    @Schema(description = "音频格式（mp3、wav、pcm、flac）")
    private AudioFormatEnum audioFormat;

    @Schema(description = "音频采样率")
    private SampleRateEnum sampleRate;

    @Schema(description = "中文语音音色类型")
    private String chineseVoiceType;

    @Schema(description = "英文语音音色类型")
    private String englishVoiceType;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;

    @Schema(description = "当前用户是否收藏了该版本的故事")
    private YesNoEnum isFavorite;
}
