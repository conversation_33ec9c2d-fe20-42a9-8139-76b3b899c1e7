package com.tth.modules.fairytale.model.vo;

import com.tth.common.enums.YesNoEnum;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.annotation.MarkMultiFileUrl;
import com.tth.modules.fairytale.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 随机获取童话故事响应VO
 */
@Data
@Schema(description = "随机获取童话故事响应")
public class RandomFairyTaleVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "故事编号（格式：YYYYMMDD001）")
    private String storyNumber;

    @Schema(description = "中文童话名称")
    private String chineseTitle;

    @Schema(description = "中文童话内容")
    private String chineseContent;

    @Schema(description = "英文童话名称")
    private String englishTitle;

    @Schema(description = "英文童话内容")
    private String englishContent;

    @Schema(description = "发布状态（0-未发布，1-已发布）")
    private PublishStatusEnum publishStatus;

    @Schema(description = "是否需要英文版")
    private YesNoEnum needEnglishVersion;

    @Schema(description = "是否需要双语版")
    private YesNoEnum needBilingualVersion;

    @Schema(description = "火山引擎精品长文本语音合成接口版本（1-普通版，2-情感预测版）")
    private ApiVersionEnum apiVersion;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文标题音频文件ID（关联tth_oss_file）")
    private Long chineseTitleAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文内容音频文件ID（关联tth_oss_file）")
    private Long chineseContentAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "中文标题+内容合成音频文件ID（关联tth_oss_file）")
    private Long chineseTitleContentAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文标题音频文件ID（关联tth_oss_file）")
    private Long englishTitleAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文内容音频文件ID（关联tth_oss_file）")
    private Long englishContentAudioId;

    @MarkFileUrl(expireMinutes = 480)
    @Schema(description = "英文标题+内容合成音频文件ID（关联tth_oss_file）")
    private Long englishTitleContentAudioId;

    @Schema(description = "中文版收藏数")
    private Integer chineseFavoriteCount;

    @Schema(description = "英文版收藏数")
    private Integer englishFavoriteCount;

    @Schema(description = "双语版收藏数")
    private Integer bilingualFavoriteCount;

    @Schema(description = "中文语音音色类型，标题和内容使用同一音色类型")
    private String chineseVoiceType;

    @Schema(description = "英文语音音色类型，标题和内容使用同一音色类型")
    private String englishVoiceType;

    @Schema(description = "音频格式（mp3、wav、pcm、flac），中英文统一音频格式")
    private AudioFormatEnum audioFormat;

    @Schema(description = "音频采样率，中英文统一音频采样率")
    private SampleRateEnum sampleRate;

    @Schema(description = "中文童话语音情感，标题和内容使用同一情感")
    private String chineseVoiceStyle;

    @Schema(description = "英文童话语音情感，标题和内容使用同一情感")
    private String englishVoiceStyle;

    @Schema(description = "中文语音音量（0.1-3.0）")
    private BigDecimal chineseVolume;

    @Schema(description = "中文语音语速（0.2-3.0）")
    private BigDecimal chineseSpeed;

    @Schema(description = "中文语音语调（0.1-3.0）")
    private BigDecimal chinesePitch;

    @Schema(description = "中文句间停顿时长（毫秒，0-3000）")
    private Integer chineseSentenceInterval;

    @Schema(description = "英文语音音量（0.1-3.0）")
    private BigDecimal englishVolume;

    @Schema(description = "英文语音语速（0.2-3.0）")
    private BigDecimal englishSpeed;

    @Schema(description = "英文语音语调（0.1-3.0）")
    private BigDecimal englishPitch;

    @Schema(description = "英文句间停顿时长（毫秒，0-3000）")
    private Integer englishSentenceInterval;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "当前用户是否收藏了该版本的故事")
    private YesNoEnum isFavorite;

    @MarkMultiFileUrl(expireMinutes = 600)
    @Schema(description = "中文故事截图（多个图片ID，逗号分隔）")
    private String chineseScreenshots;

    @MarkMultiFileUrl(expireMinutes = 600)
    @Schema(description = "英文故事截图（多个图片ID，逗号分隔）")
    private String englishScreenshots;
}
