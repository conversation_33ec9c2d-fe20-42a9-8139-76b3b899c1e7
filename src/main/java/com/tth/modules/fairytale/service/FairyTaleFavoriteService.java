package com.tth.modules.fairytale.service;

import com.tth.modules.fairytale.entity.FairyTaleFavorite;
import com.tth.modules.fairytale.model.dto.AddFairyTaleFavoriteDTO;
import com.tth.modules.fairytale.model.vo.FairyTaleFavoriteListVO;
import com.tth.modules.fairytale.model.vo.FairyTaleFavoriteDetailVO;
import com.tth.framework.base.BaseService;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;

/**
 * <p>
 * 童话故事收藏表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
public interface FairyTaleFavoriteService extends BaseService<FairyTaleFavorite> {

    /**
     * 童话故事收藏操作（添加收藏或取消收藏）
     *
     * @param addFairyTaleFavoriteDTO 收藏操作请求DTO
     * @param userBaseId 客户用户ID
     * @return 操作结果消息
     */
    String toggleFavorite(AddFairyTaleFavoriteDTO addFairyTaleFavoriteDTO, Long userBaseId);

    /**
     * 查询当前用户收藏的童话故事列表
     *
     * @param paging 分页参数
     * @param userBaseId 用户基础ID
     * @return 收藏的童话故事列表
     */
    PageResult<FairyTaleFavoriteListVO> getFavoriteList(Paging paging, Long userBaseId);

    /**
     * 根据收藏ID查询收藏记录
     *
     * @param favoriteId 收藏ID
     * @param userBaseId 用户基础ID
     * @return 收藏记录
     */
    FairyTaleFavorite getFavoriteById(Long favoriteId, Long userBaseId);

    /**
     * 根据故事ID查询收藏记录详情（包含故事内容和上下篇导航）
     *
     * @param fairyTaleId 故事ID
     * @param userBaseId 用户基础ID
     * @return 收藏记录详情
     */
    FairyTaleFavoriteDetailVO getFavoriteDetailById(Long fairyTaleId, Long userBaseId);
}
