package com.tth.modules.fairytale.service;

import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import com.tth.modules.fairytale.enums.PublishStatusEnum;
import com.tth.modules.fairytale.model.dto.AddFairyTaleDTO;
import com.tth.modules.fairytale.model.dto.BatchSynthesizeDTO;
import com.tth.modules.fairytale.model.dto.TtsCallbackDTO;
import com.tth.modules.fairytale.model.vo.RandomFairyTaleVO;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 童话故事表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface FairyTaleService extends BaseService<FairyTale> {

    /**
     * 添加童话故事
     *
     * @param addFairyTaleDTO 添加童话故事请求DTO
     * @return 创建的童话故事实体
     */
    FairyTale add(AddFairyTaleDTO addFairyTaleDTO);

    /**
     * 翻译童话故事
     *
     * @param id 童话故事ID
     */
    void translate(Long id);

    /**
     * 语音合成
     *
     * @param id 童话故事ID
     * @param target 语音合成目标（1-中文标题，2-中文内容，3-中文标题+内容，4-英文标题，5-英文内容，6-英文标题+内容）
     */
    void synthesizeVoice(Long id, Integer target);

    /**
     * 批量语音合成
     *
     * @param batchSynthesizeDTO 批量合成请求DTO
     */
    void batchSynthesize(BatchSynthesizeDTO batchSynthesizeDTO);

    /**
     * 处理火山引擎语音合成回调
     *
     */
    void handleVolcengineTtsCallback(TtsCallbackDTO callbackDTO, String audioType);

    /**
     * 发布童话故事
     *
     * @param id 童话故事ID
     * @param publishStatus 发布状态
     */
    void publish(Long id, PublishStatusEnum publishStatus);

    /**
     * 查询语音合成结果
     *
     * @param id 童话故事ID
     * @param target 语音合成目标（1-中文标题，2-中文内容，3-英文标题，4-英文内容）
     * @return 查询结果信息
     */
    String querySynthesizeResult(Long id, Integer target);

    /**
     * 根据语言版本随机获取一个已发布的童话故事，包含收藏状态
     *
     * @param languageVersion 语言版本
     * @return 随机的童话故事VO，包含收藏状态，如果没有已发布的故事则返回null
     */
    RandomFairyTaleVO getRandomFairyTale(FairyTaleLanguageVersionEnum languageVersion);
}
