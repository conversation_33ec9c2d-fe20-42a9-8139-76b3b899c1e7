package com.tth.modules.fairytale.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.modules.fairytale.entity.FairyTaleFavorite;
import com.tth.modules.fairytale.mapper.FairyTaleFavoriteMapper;
import com.tth.modules.fairytale.model.dto.AddFairyTaleFavoriteDTO;
import com.tth.modules.fairytale.model.vo.FairyTaleFavoriteListVO;
import com.tth.modules.fairytale.model.vo.FairyTaleFavoriteDetailVO;
import com.tth.modules.fairytale.service.FairyTaleService;
import com.tth.modules.fairytale.enums.FairyTaleLanguageVersionEnum;
import com.tth.framework.paging.Condition;
import com.tth.framework.paging.Order;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;
import com.tth.framework.enums.PagingOrderDirection;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.fairytale.service.FairyTaleFavoriteService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 童话故事收藏表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Slf4j
@Service
public class FairyTaleFavoriteServiceImpl extends BaseServiceImpl<FairyTaleFavoriteMapper, FairyTaleFavorite> implements FairyTaleFavoriteService {

    @Resource
    private FairyTaleFavoriteMapper fairyTaleFavoriteMapper;

    @Resource
    private FairyTaleService fairyTaleService;

    @Override
    @Transactional
    public String toggleFavorite(AddFairyTaleFavoriteDTO addFairyTaleFavoriteDTO, Long userBaseId) {
        log.info("童话故事收藏操作，参数：{}，客户用户ID：{}", addFairyTaleFavoriteDTO, userBaseId);

        // 1. 验证童话故事是否存在
        FairyTale fairyTale = fairyTaleService.getById(addFairyTaleFavoriteDTO.getFairyTaleId());
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "童话故事不存在");
        }

        // 2. 检查是否已经收藏过相同的童话故事和语言版本
        LambdaQueryWrapper<FairyTaleFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairyTaleFavorite::getUserBaseId, userBaseId)
               .eq(FairyTaleFavorite::getFairyTaleId, addFairyTaleFavoriteDTO.getFairyTaleId())
               .eq(FairyTaleFavorite::getFairyTaleLanguageVersion, addFairyTaleFavoriteDTO.getFairyTaleLanguageVersion());

        FairyTaleFavorite existingFavorite = getOne(wrapper);

        if (Boolean.TRUE.equals(addFairyTaleFavoriteDTO.getIsFavorite())) {
            // 添加收藏操作
            if (existingFavorite != null) {
                // 已经收藏过，直接返回成功
                log.info("用户已收藏该童话故事，直接返回成功");
                return "收藏成功";
            }

            // 3. 创建收藏记录
            FairyTaleFavorite fairyTaleFavorite = new FairyTaleFavorite();
            BeanUtil.copyProperties(addFairyTaleFavoriteDTO, fairyTaleFavorite);
            fairyTaleFavorite.setUserBaseId(userBaseId);

            // 4. 保存收藏记录
            boolean result = save(fairyTaleFavorite);
            if (!result) {
                throw new BizException(BaseResponseCode.FAIL, "添加收藏失败");
            }

            // 5. 更新童话故事表对应版本的收藏数 +1
            updateFavoriteCount(addFairyTaleFavoriteDTO.getFairyTaleId(),
                               addFairyTaleFavoriteDTO.getFairyTaleLanguageVersion(),
                               1);

            log.info("添加童话故事收藏成功，收藏ID：{}", fairyTaleFavorite.getId());
            return "收藏成功";
        } else {
            // 取消收藏操作
            if (existingFavorite == null) {
                // 没有收藏过，直接返回成功
                log.info("用户未收藏该童话故事，直接返回成功");
                return "取消收藏成功";
            }

            // 物理删除收藏记录
            LambdaQueryWrapper<FairyTaleFavorite> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(FairyTaleFavorite::getId, existingFavorite.getId());
            int deleteResult = fairyTaleFavoriteMapper.deleteCustom(deleteWrapper);
            if (deleteResult <= 0) {
                throw new BizException(BaseResponseCode.FAIL, "取消收藏失败");
            }

            // 更新童话故事表对应版本的收藏数 -1
            updateFavoriteCount(addFairyTaleFavoriteDTO.getFairyTaleId(),
                               addFairyTaleFavoriteDTO.getFairyTaleLanguageVersion(),
                               -1);

            log.info("取消童话故事收藏成功，收藏ID：{}", existingFavorite.getId());
            return "取消收藏成功";
        }
    }

    @Override
    public PageResult<FairyTaleFavoriteListVO> getFavoriteList(Paging paging, Long userBaseId) {
        log.info("查询用户收藏的童话故事列表，用户基础ID：{}，分页参数：{}", userBaseId, paging);

        // 添加用户ID查询条件到Paging对象
        if (paging.getConditions() == null) {
            paging.setConditions(new ArrayList<>());
        }

        // 添加客户用户ID条件
        Condition userCondition = new Condition("t1.userBaseId", userBaseId);
        paging.getConditions().add(userCondition);

        // 设置按收藏时间倒序排列
        if (paging.getOrders() == null) {
            paging.setOrders(new ArrayList<>());
        }
        Order order = new Order();
        order.setProperty("t1.createdTime");
        order.setDirection(PagingOrderDirection.DESC);
        paging.getOrders().add(order);

        // 执行分页查询收藏记录
        PageResult<FairyTaleFavorite> pageResult;
        try {
            pageResult = this.listOrPage(paging);
        } catch (Exception e) {
            log.error("查询收藏列表失败", e);
            throw new BizException(BaseResponseCode.FAIL, "查询收藏列表失败");
        }

        // 按童话故事ID分组，合并相同故事的不同语言版本
        Map<Long, List<FairyTaleFavorite>> groupedFavorites = pageResult.getList().stream()
                .collect(Collectors.groupingBy(FairyTaleFavorite::getFairyTaleId));

        // 批量查询所有相关的童话故事信息，避免N+1查询问题
        List<Long> fairyTaleIds = new ArrayList<>(groupedFavorites.keySet());
        List<FairyTale> fairyTales = fairyTaleService.listByIds(fairyTaleIds);
        Map<Long, FairyTale> fairyTaleMap = fairyTales.stream()
                .collect(Collectors.toMap(FairyTale::getId, fairyTale -> fairyTale));

        // 转换为VO列表
        List<FairyTaleFavoriteListVO> voList = new ArrayList<>();
        for (Map.Entry<Long, List<FairyTaleFavorite>> entry : groupedFavorites.entrySet()) {
            Long fairyTaleId = entry.getKey();
            List<FairyTaleFavorite> favorites = entry.getValue();

            // 从Map中获取童话故事信息
            FairyTale fairyTale = fairyTaleMap.get(fairyTaleId);
            if (fairyTale == null) {
                continue; // 跳过不存在的童话故事
            }

            // 创建VO对象
            FairyTaleFavoriteListVO vo = new FairyTaleFavoriteListVO();
            vo.setFairyTaleId(fairyTaleId);
            vo.setStoryNumber(fairyTale.getStoryNumber());
            vo.setChineseTitle(StrUtil.length(fairyTale.getChineseTitle()) <= 6 ? fairyTale.getChineseTitle() : fairyTale.getChineseTitle().substring(0, 6) + "...");
            vo.setEnglishTitle(fairyTale.getEnglishTitle());
            vo.setChineseContent(StrUtil.length(fairyTale.getChineseContent()) <= 300 ? fairyTale.getChineseContent() : fairyTale.getChineseContent().substring(0, 300) + "...");
            vo.setPublishStatus(fairyTale.getPublishStatus());
            vo.setNeedEnglishVersion(fairyTale.getNeedEnglishVersion());
            vo.setNeedBilingualVersion(fairyTale.getNeedBilingualVersion());

            // 收集该故事的所有收藏语言版本
            List<FairyTaleLanguageVersionEnum> languageVersions = favorites.stream()
                    .map(FairyTaleFavorite::getFairyTaleLanguageVersion)
                    .distinct()
                    .collect(Collectors.toList());
            vo.setFavoriteLanguageVersions(languageVersions);

            // 使用最早的收藏时间
            vo.setFavoriteTime(favorites.stream()
                    .map(FairyTaleFavorite::getCreatedTime)
                    .min(java.time.LocalDateTime::compareTo)
                    .orElse(null));

            voList.add(vo);
        }

        log.info("查询用户收藏的童话故事列表成功，共{}个故事", voList.size());
        return new PageResult<>(paging.getPageNum(), paging.getPageSize(), (long) voList.size(), voList);
    }

    @Override
    public FairyTaleFavorite getFavoriteById(Long favoriteId, Long userBaseId) {
        log.info("根据收藏ID查询收藏记录，收藏ID：{}，用户ID：{}", favoriteId, userBaseId);

        // 1. 查询收藏记录
        LambdaQueryWrapper<FairyTaleFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairyTaleFavorite::getId, favoriteId)
               .eq(FairyTaleFavorite::getUserBaseId, userBaseId);

        FairyTaleFavorite favorite = getOne(wrapper);
        if (favorite == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "收藏记录不存在或无权查看");
        }

        log.info("根据收藏ID查询收藏记录成功，收藏ID：{}", favoriteId);
        return favorite;
    }

    @Override
    public FairyTaleFavoriteDetailVO getFavoriteDetailById(Long fairyTaleId, Long userBaseId) {
        log.info("根据故事ID查询收藏记录详情，故事ID：{}，用户ID：{}", fairyTaleId, userBaseId);

        // 1. 验证故事是否存在
        FairyTale fairyTale = fairyTaleService.getById(fairyTaleId);
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "童话故事不存在");
        }

        // 2. 查询用户对该故事的收藏记录
        LambdaQueryWrapper<FairyTaleFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairyTaleFavorite::getUserBaseId, userBaseId)
               .eq(FairyTaleFavorite::getFairyTaleId, fairyTaleId);

        List<FairyTaleFavorite> favorites = list(wrapper);
        if (favorites.isEmpty()) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "该故事未收藏");
        }

        // 3. 获取用户所有收藏的故事，按中文版本收藏时间排序
        LambdaQueryWrapper<FairyTaleFavorite> allFavoritesWrapper = new LambdaQueryWrapper<>();
        allFavoritesWrapper.eq(FairyTaleFavorite::getUserBaseId, userBaseId)
                          .eq(FairyTaleFavorite::getFairyTaleLanguageVersion, FairyTaleLanguageVersionEnum.CHINESE)
                          .orderByAsc(FairyTaleFavorite::getCreatedTime);

        List<FairyTaleFavorite> allChineseFavorites = list(allFavoritesWrapper);

        // 4. 找到当前故事在列表中的位置，计算上一个和下一个故事ID
        Long previousFairyTaleId = null;
        Long nextFairyTaleId = null;

        if (allChineseFavorites.size() > 1) {
            for (int i = 0; i < allChineseFavorites.size(); i++) {
                if (allChineseFavorites.get(i).getFairyTaleId().equals(fairyTaleId)) {
                    // 找到当前故事，计算上一个和下一个（循环）
                    int previousIndex = (i - 1 + allChineseFavorites.size()) % allChineseFavorites.size();
                    int nextIndex = (i + 1) % allChineseFavorites.size();

                    previousFairyTaleId = allChineseFavorites.get(previousIndex).getFairyTaleId();
                    nextFairyTaleId = allChineseFavorites.get(nextIndex).getFairyTaleId();
                    break;
                }
            }
        }

        // 5. 构建VO
        FairyTaleFavoriteDetailVO detailVO = new FairyTaleFavoriteDetailVO();
        detailVO.setFairyTaleId(fairyTale.getId());
        detailVO.setStoryNumber(fairyTale.getStoryNumber());
        detailVO.setPreviousFairyTaleId(previousFairyTaleId);
        detailVO.setNextFairyTaleId(nextFairyTaleId);
        detailVO.setPublishStatus(fairyTale.getPublishStatus());
        detailVO.setNeedEnglishVersion(fairyTale.getNeedEnglishVersion());
        detailVO.setNeedBilingualVersion(fairyTale.getNeedBilingualVersion());

        // 6. 填充故事内容
        detailVO.setChineseTitle(fairyTale.getChineseTitle());
        detailVO.setChineseContent(fairyTale.getChineseContent());
        detailVO.setChineseTitleContentAudioId(fairyTale.getChineseTitleContentAudioId());

        detailVO.setEnglishTitle(fairyTale.getEnglishTitle());
        detailVO.setEnglishContent(fairyTale.getEnglishContent());
        detailVO.setEnglishTitleContentAudioId(fairyTale.getEnglishTitleContentAudioId());

        // 7. 收集该故事的所有收藏语言版本
        List<FairyTaleLanguageVersionEnum> languageVersions = favorites.stream()
                .map(FairyTaleFavorite::getFairyTaleLanguageVersion)
                .distinct()
                .collect(Collectors.toList());
        detailVO.setFavoriteLanguageVersions(languageVersions);

        log.info("根据故事ID查询收藏记录详情成功，故事ID：{}", fairyTaleId);
        return detailVO;
    }

    /**
     * 更新童话故事表对应版本的收藏数（使用乐观锁）
     *
     * @param fairyTaleId 童话故事ID
     * @param languageVersion 语言版本
     * @param increment 增量（+1 或 -1）
     */
    private void updateFavoriteCount(Long fairyTaleId, FairyTaleLanguageVersionEnum languageVersion, int increment) {
        // 使用乐观锁重试机制
        int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++) {
            // 查询当前记录
            FairyTale fairyTale = fairyTaleService.getById(fairyTaleId);
            if (fairyTale == null) {
                log.error("童话故事不存在，ID：{}", fairyTaleId);
                return;
            }

            // 计算新的收藏数
            int newCount;
            switch (languageVersion) {
                case CHINESE:
                    newCount = Math.max(0, (fairyTale.getChineseFavoriteCount() == null ? 0 : fairyTale.getChineseFavoriteCount()) + increment);
                    fairyTale.setChineseFavoriteCount(newCount);
                    break;
                case ENGLISH:
                    newCount = Math.max(0, (fairyTale.getEnglishFavoriteCount() == null ? 0 : fairyTale.getEnglishFavoriteCount()) + increment);
                    fairyTale.setEnglishFavoriteCount(newCount);
                    break;
                case BILINGUAL:
                    newCount = Math.max(0, (fairyTale.getBilingualFavoriteCount() == null ? 0 : fairyTale.getBilingualFavoriteCount()) + increment);
                    fairyTale.setBilingualFavoriteCount(newCount);
                    break;
                default:
                    log.error("不支持的语言版本：{}", languageVersion);
                    return;
            }

            // 使用乐观锁更新
            boolean result = fairyTaleService.updateById(fairyTale);
            if (result) {
                log.info("更新童话故事收藏数成功，童话故事ID：{}，语言版本：{}，增量：{}", fairyTaleId, languageVersion, increment);
                return;
            } else {
                log.warn("更新童话故事收藏数失败（乐观锁冲突），重试第{}次", i + 1);
            }
        }

        log.error("更新童话故事收藏数最终失败，童话故事ID：{}，语言版本：{}，增量：{}", fairyTaleId, languageVersion, increment);
    }
}
