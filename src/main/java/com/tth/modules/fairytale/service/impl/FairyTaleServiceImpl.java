package com.tth.modules.fairytale.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tth.common.enums.YesNoEnum;
import com.tth.common.utils.JacksonUtil;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.utils.AuthUtil;
import com.tth.framework.utils.SpringContextHolder;
import com.tth.modules.fairytale.entity.FairyTale;
import com.tth.modules.fairytale.enums.*;
import com.tth.modules.fairytale.mapper.FairyTaleMapper;
import com.tth.modules.fairytale.model.dto.AddFairyTaleDTO;
import com.tth.modules.fairytale.model.dto.BatchSynthesizeDTO;
import com.tth.modules.fairytale.model.dto.TtsCallbackDTO;
import com.tth.modules.fairytale.model.vo.RandomFairyTaleVO;
import com.tth.modules.fairytale.entity.FairyTaleFavorite;
import com.tth.modules.fairytale.service.FairyTaleFavoriteService;
import com.tth.modules.system.entity.OssFile;
import com.tth.modules.system.enums.FileTypeEnum;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.OssFileService;

import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.volcengine.enums.VolcengineProperty;
import com.tth.thirdparty.volcengine.service.TTSAsyncService;
import com.tth.thirdparty.volcengine.service.TranslateService;
import com.tth.thirdparty.aliyun.service.AliYunOssService;
import com.volcengine.translate20250301.model.TranslateTextResponse;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.fairytale.service.FairyTaleService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 * 童话故事表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@Service
public class FairyTaleServiceImpl extends BaseServiceImpl<FairyTaleMapper, FairyTale> implements FairyTaleService {

    @Resource
    private FairyTaleMapper fairyTaleMapper;

    @Resource
    private TTSAsyncService ttsAsyncService;

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private TranslateService translateService;

    @Resource
    private AliYunOssService aliYunOssService;

    @Resource
    private OssFileService ossFileService;

    @Resource(name = "commonExecutor")
    private Executor executor;



    @Override
    @Transactional
    public FairyTale add(AddFairyTaleDTO addFairyTaleDTO) {
        log.info("添加童话故事，参数：{}", addFairyTaleDTO);

        // 如果需要英文版或双语版，验证中文内容长度和英文音色
        if (YesNoEnum.YES.equals(addFairyTaleDTO.getNeedEnglishVersion()) ||
            YesNoEnum.YES.equals(addFairyTaleDTO.getNeedBilingualVersion())) {
            if (addFairyTaleDTO.getChineseContent().length() > 4800) {
                throw new BizException(BaseResponseCode.FAIL, "需要英文版或双语版时，中文内容长度不能超过4800个字符");
            }
            if (StrUtil.isBlank(addFairyTaleDTO.getEnglishVoiceType())){
                throw new BizException(BaseResponseCode.FAIL, "需要英文版或双语版时，英文语音音色类型不能为空");
            }
        }

        // 验证中文故事截图参数
        validateScreenshots(addFairyTaleDTO.getChineseScreenshots(), "中文故事截图");

        // 验证英文故事截图参数
        validateScreenshots(addFairyTaleDTO.getEnglishScreenshots(), "英文故事截图");

        // 创建童话故事实体
        FairyTale fairyTale = new FairyTale();

        // 复制基本属性
        BeanUtil.copyProperties(addFairyTaleDTO, fairyTale);

        // 生成故事编号
        String storyNumber = generateStoryNumber();
        fairyTale.setStoryNumber(storyNumber);

        // 根据是否需要英文版设置翻译状态
        if (YesNoEnum.YES.equals(addFairyTaleDTO.getNeedEnglishVersion()) ||
            YesNoEnum.YES.equals(addFairyTaleDTO.getNeedBilingualVersion())) {
            fairyTale.setTranslationStatus(TranslationStatusEnum.PENDING); // 待翻译
        }
        this.save(fairyTale);
        return fairyTale;
    }

    @Override
    @Transactional
    public void translate(Long id) {
        log.info("翻译童话故事，ID：{}", id);

        // 根据ID查询童话故事
        FairyTale fairyTale = getById(id);
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "童话故事不存在");
        }

        // 业务逻辑判断：可以翻译的判断是需要英文版或双语版，publishStatus=0，translationStatus不能等于1
        if (YesNoEnum.NO.equals(fairyTale.getNeedEnglishVersion()) &&
            YesNoEnum.NO.equals(fairyTale.getNeedBilingualVersion())) {
            throw new BizException(BaseResponseCode.FAIL, "只有需要英文版或双语版的童话故事才能进行翻译");
        }

        if (!PublishStatusEnum.UNPUBLISHED.equals(fairyTale.getPublishStatus())) {
            throw new BizException(BaseResponseCode.FAIL, "只有未发布的童话故事才能进行翻译");
        }

        if (TranslationStatusEnum.TRANSLATING.equals(fairyTale.getTranslationStatus())) {
            throw new BizException(BaseResponseCode.FAIL, "童话故事正在翻译中，请勿重复操作");
        }

        // 设置翻译状态为翻译中
        fairyTale.setTranslationStatus(TranslationStatusEnum.TRANSLATING);
        fairyTale.setTranslationStartTime(java.time.LocalDateTime.now());

        // 更新数据库
        updateById(fairyTale);

        // 启动异步翻译任务
        executeTranslateTaskAsync(id);

        log.info("童话故事翻译任务已启动，ID：{}", id);
    }

    @Override
    @Transactional
    public void synthesizeVoice(Long id, Integer target) {
        log.info("语音合成，ID：{}，目标：{}", id, target);

        // 根据ID查询童话故事
        FairyTale fairyTale = getById(id);
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "童话故事不存在");
        }

        // 基本判断：publishStatus不能等于1
        if (PublishStatusEnum.PUBLISHED.equals(fairyTale.getPublishStatus())) {
            throw new BizException(BaseResponseCode.FAIL, "已发布的童话故事不能进行语音合成");
        }

        fairyTale.setAppId(systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_APP_ID));
        // 根据不同的合成目标进行业务逻辑判断
        JSONObject jsonObject = null;
        String callbackHost = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_CALLBACK_HOST);
        String callbackUri = "/fairyTale/volcengine/tts/callback?audioType=";
        String callbackUrl;
        switch (target) {
            case 1:
                // 设置合成状态
                fairyTale.setChineseTitleTtsStartTime(LocalDateTime.now());
                fairyTale.setChineseTitleRequestId(IdUtil.fastSimpleUUID());
                callbackUrl = callbackHost + callbackUri + target;
                jsonObject = this.assembleChineseTitleTtsRequest(fairyTale, callbackUrl);
                fairyTale.setChineseTitleTaskId(jsonObject.getStr("task_id"));
                fairyTale.setChineseTitleTtsStatus(TtsStatusEnum.SYNTHESIZING);
                break;

            case 2:
                // 设置合成状态
                fairyTale.setChineseContentTtsStartTime(LocalDateTime.now());
                fairyTale.setChineseContentRequestId(IdUtil.fastSimpleUUID());
                callbackUrl = callbackHost + callbackUri + target;
                jsonObject = this.assembleChineseContentTtsRequest(fairyTale, callbackUrl);
                fairyTale.setChineseContentTaskId(jsonObject.getStr("task_id"));
                fairyTale.setChineseContentTtsStatus(TtsStatusEnum.SYNTHESIZING);
                break;

            case 3:
                // 中文标题+内容一起合成
                fairyTale.setChineseTitleContentTtsStartTime(LocalDateTime.now());
                fairyTale.setChineseTitleContentRequestId(IdUtil.fastSimpleUUID());
                callbackUrl = callbackHost + callbackUri + target;
                jsonObject = this.assembleChineseTitleContentTtsRequest(fairyTale, callbackUrl);
                fairyTale.setChineseTitleContentTaskId(jsonObject.getStr("task_id"));
                fairyTale.setChineseTitleContentTtsStatus(TtsStatusEnum.SYNTHESIZING);
                break;

            case 4:
                // 英文标题：需要英文版或双语版，translationStatus必须等于2，englishTitleTtsStatus不能等于1
                if (YesNoEnum.NO.equals(fairyTale.getNeedEnglishVersion()) &&
                    YesNoEnum.NO.equals(fairyTale.getNeedBilingualVersion())) {
                    throw new BizException(BaseResponseCode.FAIL, "只有需要英文版或双语版的童话故事才能进行英文标题语音合成");
                }
                if (!TranslationStatusEnum.SUCCESS.equals(fairyTale.getTranslationStatus())) {
                    throw new BizException(BaseResponseCode.FAIL, "英文标题语音合成需要翻译成功后才能进行");
                }
                // 设置合成状态
                fairyTale.setEnglishTitleTtsStartTime(LocalDateTime.now());
                fairyTale.setEnglishTitleRequestId(IdUtil.fastSimpleUUID());
                callbackUrl = callbackHost + callbackUri + target;
                jsonObject = this.assembleEnglishTitleTtsRequest(fairyTale, callbackUrl);
                fairyTale.setEnglishTitleTaskId(jsonObject.getStr("task_id"));
                fairyTale.setEnglishTitleTtsStatus(TtsStatusEnum.SYNTHESIZING);
                break;

            case 5:
                // 英文内容：需要英文版或双语版，translationStatus必须等于2，englishContentTtsStatus不能等于1
                if (YesNoEnum.NO.equals(fairyTale.getNeedEnglishVersion()) &&
                    YesNoEnum.NO.equals(fairyTale.getNeedBilingualVersion())) {
                    throw new BizException(BaseResponseCode.FAIL, "只有需要英文版或双语版的童话故事才能进行英文内容语音合成");
                }
                if (!TranslationStatusEnum.SUCCESS.equals(fairyTale.getTranslationStatus())) {
                    throw new BizException(BaseResponseCode.FAIL, "英文内容语音合成需要翻译成功后才能进行");
                }
                // 设置合成状态
                fairyTale.setEnglishContentTtsStartTime(LocalDateTime.now());
                fairyTale.setEnglishContentRequestId(IdUtil.fastSimpleUUID());
                callbackUrl = callbackHost + callbackUri + target;
                jsonObject = this.assembleEnglishContentTtsRequest(fairyTale, callbackUrl);
                fairyTale.setEnglishContentTaskId(jsonObject.getStr("task_id"));
                fairyTale.setEnglishContentTtsStatus(TtsStatusEnum.SYNTHESIZING);
                break;

            case 6:
                // 英文标题+内容一起合成：需要英文版或双语版，translationStatus必须等于2
                if (YesNoEnum.NO.equals(fairyTale.getNeedEnglishVersion()) &&
                    YesNoEnum.NO.equals(fairyTale.getNeedBilingualVersion())) {
                    throw new BizException(BaseResponseCode.FAIL, "只有需要英文版或双语版的童话故事才能进行英文标题+内容语音合成");
                }
                if (!TranslationStatusEnum.SUCCESS.equals(fairyTale.getTranslationStatus())) {
                    throw new BizException(BaseResponseCode.FAIL, "英文标题+内容语音合成需要翻译成功后才能进行");
                }
                // 设置合成状态
                fairyTale.setEnglishTitleContentTtsStartTime(LocalDateTime.now());
                fairyTale.setEnglishTitleContentRequestId(IdUtil.fastSimpleUUID());
                callbackUrl = callbackHost + callbackUri + target;
                jsonObject = this.assembleEnglishTitleContentTtsRequest(fairyTale, callbackUrl);
                fairyTale.setEnglishTitleContentTaskId(jsonObject.getStr("task_id"));
                fairyTale.setEnglishTitleContentTtsStatus(TtsStatusEnum.SYNTHESIZING);
                break;

            default:
                throw new BizException(BaseResponseCode.FAIL, "不支持的语音合成目标");
        }

        // 设置最近合成时间
        fairyTale.setLastSynthesizeTime(LocalDateTime.now());

        // 更新数据库
        boolean result = updateById(fairyTale);
        if (!result) {
            throw new BizException(BaseResponseCode.FAIL, "更新语音合成状态失败");
        }

        log.info("语音合成任务已启动，ID：{}，目标：{}", id, target);
    }

    /**
     * 异步执行翻译任务
     * 包含两个子任务：翻译标题和翻译内容
     *
     * @param fairyTaleId 童话故事ID
     */
    private void executeTranslateTaskAsync(Long fairyTaleId) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始执行异步翻译任务，童话故事ID：{}", fairyTaleId);

                // 重新查询童话故事，确保获取最新数据
                FairyTale fairyTale = getById(fairyTaleId);
                if (fairyTale == null) {
                    log.error("异步翻译任务失败：童话故事不存在，ID：{}", fairyTaleId);
                    return;
                }

                // 任务1：翻译标题
                String translatedTitle = null;
                try {
                    log.info("开始翻译标题，原标题：{}", fairyTale.getChineseTitle());
                    TranslateTextResponse titleResponse = translateService.translateText(
                            Collections.singletonList(fairyTale.getChineseTitle()),
                        "zh",
                        "en"
                    );

                    translatedTitle = titleResponse.getTranslationList().get(0).getTranslation();
                    log.info("标题翻译成功：{}", translatedTitle);
                } catch (Exception e) {
                    log.error("标题翻译异常：{}", e.getMessage(), e);
                }

                // 任务2：翻译内容
                String translatedContent = null;
                try {
                    log.info("开始翻译内容，内容长度：{}", fairyTale.getChineseContent().length());
                    TranslateTextResponse contentResponse = translateService.translateText(
                            Collections.singletonList(fairyTale.getChineseContent()),
                        "zh",
                        "en"
                    );

                    translatedContent = contentResponse.getTranslationList().get(0).getTranslation();
                    log.info("内容翻译成功，翻译后长度：{}", translatedContent.length());
                } catch (Exception e) {
                    log.error("内容翻译异常：{}", e.getMessage(), e);
                }

                // 更新童话故事表
                updateTranslationResult(fairyTaleId, translatedTitle, translatedContent);

            } catch (Exception e) {
                log.error("异步翻译任务执行失败，童话故事ID：{}，错误：{}", fairyTaleId, e.getMessage(), e);
                // 更新翻译状态为失败
                try {
                    LambdaUpdateWrapper<FairyTale> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(FairyTale::getId, fairyTaleId)
                            .set(FairyTale::getTranslationStatus, TranslationStatusEnum.FAILED)
                            .set(FairyTale::getTranslationCompleteTime, java.time.LocalDateTime.now());
                    update(updateWrapper);
                    log.info("翻译状态已更新为失败，童话故事ID：{}", fairyTaleId);
                } catch (Exception updateException) {
                    log.error("更新翻译失败状态失败，童话故事ID：{}，错误：{}", fairyTaleId, updateException.getMessage(), updateException);
                }
            }
        }, executor);
    }

    /**
     * 更新翻译结果
     *
     * @param fairyTaleId 童话故事ID
     * @param translatedTitle 翻译后的标题
     * @param translatedContent 翻译后的内容
     */
    private void updateTranslationResult(Long fairyTaleId, String translatedTitle, String translatedContent) {
        try {
            LambdaUpdateWrapper<FairyTale> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FairyTale::getId, fairyTaleId);

            // 设置翻译结果
            if (translatedTitle != null) {
                updateWrapper.set(FairyTale::getEnglishTitle, translatedTitle);
            }
            if (translatedContent != null) {
                updateWrapper.set(FairyTale::getEnglishContent, translatedContent);
            }

            // 判断翻译是否完全成功
            if (translatedTitle != null && translatedContent != null) {
                updateWrapper.set(FairyTale::getTranslationStatus, TranslationStatusEnum.SUCCESS);
                updateWrapper.set(FairyTale::getTranslationCompleteTime, java.time.LocalDateTime.now());
                log.info("翻译任务完全成功，童话故事ID：{}", fairyTaleId);
            } else {
                updateWrapper.set(FairyTale::getTranslationStatus, TranslationStatusEnum.FAILED);
                updateWrapper.set(FairyTale::getTranslationCompleteTime, java.time.LocalDateTime.now());
                log.warn("翻译任务部分失败，童话故事ID：{}，标题翻译：{}，内容翻译：{}",
                    fairyTaleId, translatedTitle != null ? "成功" : "失败", translatedContent != null ? "成功" : "失败");
            }

            update(updateWrapper);

        } catch (Exception e) {
            log.error("更新翻译结果失败，童话故事ID：{}，错误：{}", fairyTaleId, e.getMessage(), e);
        }
    }

    private JSONObject assembleChineseTitleTtsRequest(FairyTale fairyTale, String callbackUrl) {
        // 组装中文标题语音合成请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("reqid", fairyTale.getChineseTitleRequestId());
        request.set("text", fairyTale.getChineseTitle());
        request.set("format", fairyTale.getAudioFormat().getValue());
        request.set("voice_type", fairyTale.getChineseVoiceType());
        request.set("language", "cn");
        request.set("sample_rate", fairyTale.getSampleRate().getValue());
        request.set("volume", fairyTale.getChineseVolume());
        request.set("speed", fairyTale.getChineseSpeed());
        request.set("pitch", fairyTale.getChinesePitch());
        request.set("enable_subtitle", 0);
        request.set("sentence_interval", fairyTale.getChineseSentenceInterval());
        request.set("style", fairyTale.getChineseVoiceStyle());
//        request.set("callback_url", callbackUrl);
        if(fairyTale.getApiVersion() == ApiVersionEnum.EMOTION_PREDICTION){
            return ttsAsyncService.submitTTSAsyncEmotionTask(request);
        }
        return ttsAsyncService.submitTTSAsyncTask(request);
    }

    private JSONObject assembleChineseContentTtsRequest(FairyTale fairyTale, String callbackUrl) {
        // 组装中文标题语音合成请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("reqid", fairyTale.getChineseContentRequestId());
        request.set("text", fairyTale.getChineseContent());
        request.set("format", fairyTale.getAudioFormat().getValue());
        request.set("voice_type", fairyTale.getChineseVoiceType());
        request.set("language", "cn");
        request.set("sample_rate", fairyTale.getSampleRate().getValue());
        request.set("volume", fairyTale.getChineseVolume());
        request.set("speed", fairyTale.getChineseSpeed());
        request.set("pitch", fairyTale.getChinesePitch());
        request.set("enable_subtitle", 2);
        request.set("sentence_interval", fairyTale.getChineseSentenceInterval());
        request.set("style", fairyTale.getChineseVoiceStyle());
//        request.set("callback_url", callbackUrl);
        if(fairyTale.getApiVersion() == ApiVersionEnum.EMOTION_PREDICTION){
            return ttsAsyncService.submitTTSAsyncEmotionTask(request);
        }
        return ttsAsyncService.submitTTSAsyncTask(request);
    }

    private JSONObject assembleChineseTitleContentTtsRequest(FairyTale fairyTale, String callbackUrl) {
        // 组装中文标题+内容语音合成请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("reqid", fairyTale.getChineseTitleContentRequestId());
        request.set("text", fairyTale.getChineseTitle() + "。" + fairyTale.getChineseContent());
        request.set("format", fairyTale.getAudioFormat().getValue());
        request.set("voice_type", fairyTale.getChineseVoiceType());
        request.set("language", "cn");
        request.set("sample_rate", fairyTale.getSampleRate().getValue());
        request.set("volume", fairyTale.getChineseVolume());
        request.set("speed", fairyTale.getChineseSpeed());
        request.set("pitch", fairyTale.getChinesePitch());
        request.set("enable_subtitle", 2);
        request.set("sentence_interval", fairyTale.getChineseSentenceInterval());
        request.set("style", fairyTale.getChineseVoiceStyle());
//        request.set("callback_url", callbackUrl);
        if(fairyTale.getApiVersion() == ApiVersionEnum.EMOTION_PREDICTION){
            return ttsAsyncService.submitTTSAsyncEmotionTask(request);
        }
        return ttsAsyncService.submitTTSAsyncTask(request);
    }

    private JSONObject assembleEnglishTitleTtsRequest(FairyTale fairyTale, String callbackUrl) {
        // 组装中文标题语音合成请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("reqid", fairyTale.getEnglishTitleRequestId());
        request.set("text", fairyTale.getEnglishTitle());
        request.set("format", fairyTale.getAudioFormat().getValue());
        request.set("voice_type", fairyTale.getEnglishVoiceType());
        request.set("language", "en");
        request.set("sample_rate", fairyTale.getSampleRate().getValue());
        request.set("volume", fairyTale.getEnglishVolume());
        request.set("speed", fairyTale.getEnglishSpeed());
        request.set("pitch", fairyTale.getChinesePitch());
        request.set("enable_subtitle", 0);
        request.set("sentence_interval", fairyTale.getEnglishSentenceInterval());
        request.set("style", fairyTale.getEnglishVoiceStyle());
//        request.set("callback_url", callbackUrl);
        if(fairyTale.getApiVersion() == ApiVersionEnum.EMOTION_PREDICTION){
            return ttsAsyncService.submitTTSAsyncEmotionTask(request);
        }
        return ttsAsyncService.submitTTSAsyncTask(request);
    }

    private JSONObject assembleEnglishContentTtsRequest(FairyTale fairyTale, String callbackUrl) {
        // 组装中文标题语音合成请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("reqid", fairyTale.getEnglishContentRequestId());
        request.set("text", fairyTale.getEnglishContent());
        request.set("format", fairyTale.getAudioFormat().getValue());
        request.set("voice_type", fairyTale.getEnglishVoiceType());
        request.set("language", "en");
        request.set("sample_rate", fairyTale.getSampleRate().getValue());
        request.set("volume", fairyTale.getEnglishVolume());
        request.set("speed", fairyTale.getEnglishSpeed());
        request.set("pitch", fairyTale.getEnglishPitch());
        request.set("enable_subtitle", 2);
        request.set("sentence_interval", fairyTale.getEnglishSentenceInterval());
        request.set("style", fairyTale.getEnglishVoiceStyle());
//        request.set("callback_url", callbackUrl);
        if(fairyTale.getApiVersion() == ApiVersionEnum.EMOTION_PREDICTION){
            return ttsAsyncService.submitTTSAsyncEmotionTask(request);
        }
        return ttsAsyncService.submitTTSAsyncTask(request);
    }

    private JSONObject assembleEnglishTitleContentTtsRequest(FairyTale fairyTale, String callbackUrl) {
        // 组装英文标题+内容语音合成请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("reqid", fairyTale.getEnglishTitleContentRequestId());
        request.set("text", fairyTale.getEnglishTitle() + ". " + fairyTale.getEnglishContent());
        request.set("format", fairyTale.getAudioFormat().getValue());
        request.set("voice_type", fairyTale.getEnglishVoiceType());
        request.set("language", "en");
        request.set("sample_rate", fairyTale.getSampleRate().getValue());
        request.set("volume", fairyTale.getEnglishVolume());
        request.set("speed", fairyTale.getEnglishSpeed());
        request.set("pitch", fairyTale.getEnglishPitch());
        request.set("enable_subtitle", 2);
        request.set("sentence_interval", fairyTale.getEnglishSentenceInterval());
        request.set("style", fairyTale.getEnglishVoiceStyle());
//        request.set("callback_url", callbackUrl);
        if(fairyTale.getApiVersion() == ApiVersionEnum.EMOTION_PREDICTION){
            return ttsAsyncService.submitTTSAsyncEmotionTask(request);
        }
        return ttsAsyncService.submitTTSAsyncTask(request);
    }

    @Override
    @Transactional
    public void handleVolcengineTtsCallback(TtsCallbackDTO callbackDTO, String audioType) {
        log.info("处理火山引擎TTS回调，任务ID：{}，状态：{}，音频类型：{}",
            callbackDTO.getTask_id(), callbackDTO.getCode(), audioType);

        // 根据音频类型和task_id查找童话故事记录
        FairyTale fairyTale = findFairyTaleByTaskId(callbackDTO.getTask_id(), audioType);
        if (fairyTale == null) {
            log.error("根据任务ID和音频类型未找到童话故事记录，任务ID：{}，音频类型：{}", callbackDTO.getTask_id(), audioType);
            return;
        }

        // 更新TTS状态和结果
        LambdaUpdateWrapper<FairyTale> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FairyTale::getId, fairyTale.getId());

        // 根据音频类型设置对应的TTS结果
        setTtsResultByAudioType(updateWrapper, callbackDTO, audioType);

        if (callbackDTO.getCode() == 0 && callbackDTO.getTask_status() == 1) {
            // 合成成功，开始上传音频到OSS
            setTtsStatusByAudioType(updateWrapper, audioType, TtsStatusEnum.UPLOADING);
            log.info("TTS合成成功，开始上传音频，童话故事ID：{}，音频类型：{}，音频URL：{}",
                fairyTale.getId(), audioType, callbackDTO.getAudio_url());

            // 先更新状态为上传中
            update(updateWrapper);

            try {
                // 上传音频到OSS
                PutObjectResult putObjectResult = aliYunOssService.putObjectWithCallback(new URL(callbackDTO.getAudio_url()), fairyTale.getAudioFormat().getValue());

                // 处理OSS回调响应
                Long fileId = processOssCallbackResponse(putObjectResult);

                // 设置成功状态和文件ID
                if (fileId != null) {
                    setAudioIdByAudioType(updateWrapper, audioType, fileId);
                    log.info("音频上传完成，童话故事ID：{}，音频类型：{}，文件ID：{}",
                        fairyTale.getId(), audioType, fileId);
                } else {
                    log.info("音频上传完成，但未获取到文件ID，童话故事ID：{}，音频类型：{}",
                        fairyTale.getId(), audioType);
                }

                setTtsStatusByAudioType(updateWrapper, audioType, TtsStatusEnum.SUCCESS);
                setTtsCompleteTimeByAudioType(updateWrapper, audioType, LocalDateTime.now());
                update(updateWrapper);

                log.info("音频上传任务已完成，童话故事ID：{}，音频类型：{}", fairyTale.getId(), audioType);
                return; // 提前返回，不再执行后续的update
            } catch (Exception e) {
                log.error("上传音频到OSS失败，童话故事ID：{}，音频类型：{}，错误：{}",
                    fairyTale.getId(), audioType, e.getMessage(), e);
                // 设置为失败状态
                setTtsStatusByAudioType(updateWrapper, audioType, TtsStatusEnum.FAILED);
                setTtsCompleteTimeByAudioType(updateWrapper, audioType, LocalDateTime.now());
            }
        } else {
            // 合成失败
            setTtsStatusByAudioType(updateWrapper, audioType, TtsStatusEnum.FAILED);
            setTtsCompleteTimeByAudioType(updateWrapper, audioType, LocalDateTime.now());
            log.error("TTS合成失败，童话故事ID：{}，音频类型：{}，错误信息：{}",
                fairyTale.getId(), audioType, callbackDTO.getMessage());
        }

        update(updateWrapper);
        log.info("TTS回调处理完成，童话故事ID：{}，音频类型：{}", fairyTale.getId(), audioType);
    }

    @Override
    @Transactional
    public void publish(Long id, PublishStatusEnum publishStatus) {
        log.info("发布童话故事，ID：{}，发布状态：{}", id, publishStatus);

        // 根据ID查询童话故事
        FairyTale fairyTale = getById(id);
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "童话故事不存在");
        }

        // 如果是设置为未发布，直接更新
        if (PublishStatusEnum.UNPUBLISHED.equals(publishStatus)) {
            fairyTale.setPublishStatus(PublishStatusEnum.UNPUBLISHED);

            boolean result = updateById(fairyTale);
            if (!result) {
                throw new BizException(BaseResponseCode.FAIL, "更新发布状态失败");
            }
            log.info("童话故事已设置为未发布，ID：{}", id);
            return;
        }

        // 如果是设置为已发布，需要进行校验
        if (PublishStatusEnum.PUBLISHED.equals(publishStatus)) {
            // 只检查标题+内容合成状态
            // 检查中文标题+内容的TTS状态（必须的）
            if (!TtsStatusEnum.SUCCESS.equals(fairyTale.getChineseTitleContentTtsStatus())) {
                throw new BizException(BaseResponseCode.FAIL, "中文标题+内容语音合成未完成，无法发布");
            }

            // 如果需要英文版或双语版，检查翻译状态和英文标题+内容TTS状态
            if(YesNoEnum.YES == fairyTale.getNeedEnglishVersion() || YesNoEnum.YES == fairyTale.getNeedBilingualVersion()) {
                if (!TranslationStatusEnum.SUCCESS.equals(fairyTale.getTranslationStatus())) {
                    throw new BizException(BaseResponseCode.FAIL, "英文翻译未完成，无法发布");
                }
                if (!TtsStatusEnum.SUCCESS.equals(fairyTale.getEnglishTitleContentTtsStatus())) {
                    throw new BizException(BaseResponseCode.FAIL, "英文标题+内容语音合成未完成，无法发布");
                }
            }

            // 校验通过，设置为已发布
            fairyTale.setPublishStatus(PublishStatusEnum.PUBLISHED);

            boolean result = updateById(fairyTale);
            if (!result) {
                throw new BizException(BaseResponseCode.FAIL, "更新发布状态失败");
            }
            log.info("童话故事已发布，ID：{}", id);
        } else {
            throw new BizException(BaseResponseCode.FAIL, "不支持的发布状态值");
        }
    }

    @Override
    @Transactional
    public String querySynthesizeResult(Long id, Integer target) {
        log.info("查询语音合成结果，ID：{}，目标：{}", id, target);

        // 根据ID查询童话故事
        FairyTale fairyTale = getById(id);
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "童话故事不存在");
        }

        // 获取对应目标的任务ID
        String taskId = getTaskIdByTarget(fairyTale, target);
        if (taskId == null) {
            throw new BizException(BaseResponseCode.FAIL, "该目标尚未开始语音合成任务");
        }

        // 检查当前TTS状态，只允许合成中和上传中的状态查询
        TtsStatusEnum currentStatus = getTtsStatusByTarget(fairyTale, target);
        String targetName = getTargetName(target);

        if (TtsStatusEnum.SUCCESS.equals(currentStatus)) {
            log.info("{}已经合成成功，直接返回成功信息，ID：{}，目标：{}", targetName, id, target);
            return targetName + "合成成功";
        }

        if (TtsStatusEnum.FAILED.equals(currentStatus)) {
            log.info("{}合成已失败，直接返回失败信息，ID：{}，目标：{}", targetName, id, target);
            return targetName + "合成失败";
        }

        if (TtsStatusEnum.PENDING.equals(currentStatus)) {
            throw new BizException(BaseResponseCode.FAIL, targetName + "尚未开始合成，无法查询结果");
        }

        // 构建查询请求
        JSONObject request = new JSONObject();
        request.set("appid", fairyTale.getAppId());
        request.set("task_id", taskId);

        // 根据API版本调用不同的查询接口
        JSONObject response;
        try {
            if (ApiVersionEnum.EMOTION_PREDICTION.equals(fairyTale.getApiVersion())) {
                response = ttsAsyncService.ttsAsyncWithEmotionQuery(request);
            } else {
                response = ttsAsyncService.ttsAsyncQuery(request);
            }
        } catch (Exception e) {
            log.error("查询语音合成结果失败，ID：{}，目标：{}，异常：{}", id, target, e.getMessage(), e);
            // 构造失败的响应，设置task_status为2，让流程进入失败处理逻辑
            response = new JSONObject();
            response.set("task_status", 2);
            response.set("message", "查询合成结果异常：" + e.getMessage());
        }

        log.info("查询语音合成结果成功，ID：{}，目标：{}，响应：{}", id, target, response.toString());

        // 根据任务状态返回不同的信息
        Integer taskStatus = response.getInt("task_status");

        if (taskStatus == null) {
            return targetName + "合成状态未知，请稍后重试";
        }

        switch (taskStatus) {
            case 0:
                return targetName + "正在合成中，请稍后查询";
            case 1:
                // 合成成功时，异步处理回调逻辑
                TtsCallbackDTO callbackDTO = JacksonUtil.toBean(response.toString(), TtsCallbackDTO.class);
                // 查询接口不会返回code和message字段，所以在这里手动添加，免得和处理回调的逻辑冲突
                callbackDTO.setCode(0);
                callbackDTO.setMessage("success");
                handleTtsCallbackAsync(callbackDTO, target.toString(), id);
                return targetName + "合成成功";
            case 2:
                // 合成失败时，异步处理回调逻辑以更新状态
                TtsCallbackDTO failedCallbackDTO = new TtsCallbackDTO();
                failedCallbackDTO.setTask_id(taskId);
                failedCallbackDTO.setCode(1); // 设置为失败状态
                failedCallbackDTO.setTask_status(2); // 任务失败
                failedCallbackDTO.setMessage(response.getStr("message"));
                handleTtsCallbackAsync(failedCallbackDTO, target.toString(), id);

                String errorMsg = response.getStr("message");
                return targetName + "合成失败" + (StrUtil.isNotBlank(errorMsg) ? "：" + errorMsg : "");
            default:
                return targetName + "合成状态异常，状态码：" + taskStatus;
        }
    }

    /**
     * 根据任务ID和音频类型查找童话故事记录
     */
    private FairyTale findFairyTaleByTaskId(String taskId, String audioType) {
        LambdaQueryWrapper<FairyTale> wrapper = new LambdaQueryWrapper<>();

        switch (audioType) {
            case "1": // 中文标题
                wrapper.eq(FairyTale::getChineseTitleTaskId, taskId);
                break;
            case "2": // 中文内容
                wrapper.eq(FairyTale::getChineseContentTaskId, taskId);
                break;
            case "3": // 中文标题+内容
                wrapper.eq(FairyTale::getChineseTitleContentTaskId, taskId);
                break;
            case "4": // 英文标题
                wrapper.eq(FairyTale::getEnglishTitleTaskId, taskId);
                break;
            case "5": // 英文内容
                wrapper.eq(FairyTale::getEnglishContentTaskId, taskId);
                break;
            case "6": // 英文标题+内容
                wrapper.eq(FairyTale::getEnglishTitleContentTaskId, taskId);
                break;
            default:
                log.error("不支持的音频类型：{}", audioType);
                return null;
        }

        return getOne(wrapper);
    }

    /**
     * 根据音频类型设置TTS结果
     */
    private void setTtsResultByAudioType(LambdaUpdateWrapper<FairyTale> updateWrapper,
                                        TtsCallbackDTO callbackDTO, String audioType) {
        String resultJson = JSONUtil.toJsonStr(callbackDTO);

        switch (audioType) {
            case "1": // 中文标题
                updateWrapper.set(FairyTale::getChineseTitleTtsResult, resultJson);
                break;
            case "2": // 中文内容
                updateWrapper.set(FairyTale::getChineseContentTtsResult, resultJson);
                break;
            case "3": // 中文标题+内容
                updateWrapper.set(FairyTale::getChineseTitleContentTtsResult, resultJson);
                break;
            case "4": // 英文标题
                updateWrapper.set(FairyTale::getEnglishTitleTtsResult, resultJson);
                break;
            case "5": // 英文内容
                updateWrapper.set(FairyTale::getEnglishContentTtsResult, resultJson);
                break;
            case "6": // 英文标题+内容
                updateWrapper.set(FairyTale::getEnglishTitleContentTtsResult, resultJson);
                break;
        }
    }

    /**
     * 根据音频类型设置TTS状态
     */
    private void setTtsStatusByAudioType(LambdaUpdateWrapper<FairyTale> updateWrapper,
                                        String audioType, TtsStatusEnum status) {
        switch (audioType) {
            case "1": // 中文标题
                updateWrapper.set(FairyTale::getChineseTitleTtsStatus, status);
                break;
            case "2": // 中文内容
                updateWrapper.set(FairyTale::getChineseContentTtsStatus, status);
                break;
            case "3": // 中文标题+内容
                updateWrapper.set(FairyTale::getChineseTitleContentTtsStatus, status);
                break;
            case "4": // 英文标题
                updateWrapper.set(FairyTale::getEnglishTitleTtsStatus, status);
                break;
            case "5": // 英文内容
                updateWrapper.set(FairyTale::getEnglishContentTtsStatus, status);
                break;
            case "6": // 英文标题+内容
                updateWrapper.set(FairyTale::getEnglishTitleContentTtsStatus, status);
                break;
        }
    }

    /**
     * 根据音频类型设置TTS完成时间
     */
    private void setTtsCompleteTimeByAudioType(LambdaUpdateWrapper<FairyTale> updateWrapper,
                                              String audioType, LocalDateTime completeTime) {
        switch (audioType) {
            case "1": // 中文标题
                updateWrapper.set(FairyTale::getChineseTitleTtsCompleteTime, completeTime);
                break;
            case "2": // 中文内容
                updateWrapper.set(FairyTale::getChineseContentTtsCompleteTime, completeTime);
                break;
            case "3": // 中文标题+内容
                updateWrapper.set(FairyTale::getChineseTitleContentTtsCompleteTime, completeTime);
                break;
            case "4": // 英文标题
                updateWrapper.set(FairyTale::getEnglishTitleTtsCompleteTime, completeTime);
                break;
            case "5": // 英文内容
                updateWrapper.set(FairyTale::getEnglishContentTtsCompleteTime, completeTime);
                break;
            case "6": // 英文标题+内容
                updateWrapper.set(FairyTale::getEnglishTitleContentTtsCompleteTime, completeTime);
                break;
        }
    }

    /**
     * 处理OSS回调响应，提取文件ID
     *
     * @param putObjectResult OSS上传结果
     * @return 文件ID，如果获取失败返回null
     */
    private Long processOssCallbackResponse(PutObjectResult putObjectResult) {
        try (InputStream callbackResponse = putObjectResult.getResponse().getContent()) {
            if (callbackResponse == null) {
                log.info("OSS回调响应数据为空");
                return null;
            }

            String responseBody = new String(callbackResponse.readAllBytes(), StandardCharsets.UTF_8);
            log.info("OSS回调响应数据: {}", responseBody);

            JSONObject responseJson = JSONUtil.parseObj(responseBody);
            return responseJson.getJSONObject("data").getLong("fileId");
        } catch (IOException e) {
            log.warn("读取OSS回调响应数据失败: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.warn("解析OSS回调响应数据失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据音频类型设置音频文件ID
     */
    private void setAudioIdByAudioType(LambdaUpdateWrapper<FairyTale> updateWrapper,
                                      String audioType, Long audioId) {
        switch (audioType) {
            case "1": // 中文标题
                updateWrapper.set(FairyTale::getChineseTitleAudioId, audioId);
                break;
            case "2": // 中文内容
                updateWrapper.set(FairyTale::getChineseContentAudioId, audioId);
                break;
            case "3": // 中文标题+内容
                updateWrapper.set(FairyTale::getChineseTitleContentAudioId, audioId);
                break;
            case "4": // 英文标题
                updateWrapper.set(FairyTale::getEnglishTitleAudioId, audioId);
                break;
            case "5": // 英文内容
                updateWrapper.set(FairyTale::getEnglishContentAudioId, audioId);
                break;
            case "6": // 英文标题+内容
                updateWrapper.set(FairyTale::getEnglishTitleContentAudioId, audioId);
                break;
        }
    }

    /**
     * 根据目标获取对应的任务ID
     */
    private String getTaskIdByTarget(FairyTale fairyTale, Integer target) {
        return switch (target) {
            case 1 -> // 中文标题
                    fairyTale.getChineseTitleTaskId();
            case 2 -> // 中文内容
                    fairyTale.getChineseContentTaskId();
            case 3 -> // 中文标题+内容
                    fairyTale.getChineseTitleContentTaskId();
            case 4 -> // 英文标题
                    fairyTale.getEnglishTitleTaskId();
            case 5 -> // 英文内容
                    fairyTale.getEnglishContentTaskId();
            case 6 -> // 英文标题+内容
                    fairyTale.getEnglishTitleContentTaskId();
            default -> null;
        };
    }

    /**
     * 根据目标获取目标名称
     */
    private String getTargetName(Integer target) {
        switch (target) {
            case 1:
                return "中文标题";
            case 2:
                return "中文内容";
            case 3:
                return "中文标题+内容";
            case 4:
                return "英文标题";
            case 5:
                return "英文内容";
            case 6:
                return "英文标题+内容";
            default:
                return "未知目标";
        }
    }

    /**
     * 根据目标获取对应的TTS状态
     */
    private TtsStatusEnum getTtsStatusByTarget(FairyTale fairyTale, Integer target) {
        switch (target) {
            case 1: // 中文标题
                return fairyTale.getChineseTitleTtsStatus();
            case 2: // 中文内容
                return fairyTale.getChineseContentTtsStatus();
            case 3: // 中文标题+内容
                return fairyTale.getChineseTitleContentTtsStatus();
            case 4: // 英文标题
                return fairyTale.getEnglishTitleTtsStatus();
            case 5: // 英文内容
                return fairyTale.getEnglishContentTtsStatus();
            case 6: // 英文标题+内容
                return fairyTale.getEnglishTitleContentTtsStatus();
            default:
                return null;
        }
    }

    /**
     * 异步处理TTS回调
     */
    private void handleTtsCallbackAsync(TtsCallbackDTO callbackDTO, String audioType, Long fairyTaleId) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步处理TTS回调，童话故事ID：{}，音频类型：{}", fairyTaleId, audioType);
                // 通过ApplicationContext获取代理对象来调用事务方法
                FairyTaleService proxyService = SpringContextHolder.getBean(FairyTaleService.class);
                proxyService.handleVolcengineTtsCallback(callbackDTO, audioType);
                log.info("异步处理TTS回调完成，童话故事ID：{}，音频类型：{}", fairyTaleId, audioType);
            } catch (Exception e) {
                log.error("异步处理TTS回调失败，童话故事ID：{}，音频类型：{}，错误：{}",
                    fairyTaleId, audioType, e.getMessage(), e);
            }
        }, executor);
    }

    /**
     * 生成故事编号
     * 格式：YYYYMMDD001，按照日期+000依次编号
     * @return 故事编号
     */
    private String generateStoryNumber() {
        // 获取当前日期，格式：YYYYMMDD
        String datePrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询当天最大的故事编号
        String maxStoryNumber = fairyTaleMapper.getMaxStoryNumberByDate(datePrefix);

        int nextSequence = 1;
        if (StrUtil.isNotBlank(maxStoryNumber)) {
            // 提取序号部分（最后3位）
            String sequencePart = maxStoryNumber.substring(8); // 从第9位开始取3位
            nextSequence = Integer.parseInt(sequencePart) + 1;
        }

        // 生成新的故事编号，序号部分补零到3位
        return datePrefix + String.format("%03d", nextSequence);
    }

    @Override
    public RandomFairyTaleVO getRandomFairyTale(FairyTaleLanguageVersionEnum languageVersion) {
        log.info("根据语言版本随机获取童话故事（包含收藏状态），语言版本：{}", languageVersion);

        // 使用wrapper查询已发布的童话故事
        LambdaQueryWrapper<FairyTale> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FairyTale::getPublishStatus, PublishStatusEnum.PUBLISHED);

        // 根据语言版本添加查询条件
        switch (languageVersion) {
            case CHINESE:
                // 中文版：所有已发布的故事都有中文版
                // 无需额外条件
                break;
            case ENGLISH:
                // 英文版：需要英文版本
                wrapper.eq(FairyTale::getNeedEnglishVersion, true);
                break;
            case BILINGUAL:
                // 双语版：需要双语版本
                wrapper.eq(FairyTale::getNeedBilingualVersion, true);
                break;
            default:
                throw new BizException(BaseResponseCode.FAIL, "不支持的语言版本");
        }

        wrapper.last("ORDER BY RAND() LIMIT 1"); // MySQL随机排序并限制一条记录

        FairyTale fairyTale = getOne(wrapper);
        if (fairyTale == null) {
            return null;
        }

        // 转换为VO
        RandomFairyTaleVO randomFairyTaleVO = new RandomFairyTaleVO();
        BeanUtil.copyProperties(fairyTale, randomFairyTaleVO);

        // 检查用户是否登录，如果登录则查询收藏状态
        if (StpUtil.isLogin()) {
            // 查询用户是否收藏了该版本的故事
            LambdaQueryWrapper<FairyTaleFavorite> favoriteWrapper = new LambdaQueryWrapper<>();
            favoriteWrapper.select(FairyTaleFavorite::getId)
                    .eq(FairyTaleFavorite::getUserBaseId, AuthUtil.getUserId())
                    .eq(FairyTaleFavorite::getFairyTaleId, fairyTale.getId())
                    .eq(FairyTaleFavorite::getFairyTaleLanguageVersion, languageVersion);

            // 使用 SpringContextHolder 获取 FairyTaleFavoriteService 避免循环依赖
            FairyTaleFavorite favorite = SpringContextHolder.getBean(FairyTaleFavoriteService.class).getOne(favoriteWrapper);
            randomFairyTaleVO.setIsFavorite(favorite == null ? YesNoEnum.NO : YesNoEnum.YES);

            log.info("用户已登录，查询收藏状态完成，用户ID：{}，是否收藏：{}", AuthUtil.getUserId(), randomFairyTaleVO.getIsFavorite());
        } else {
            // 用户未登录，收藏状态为null
            randomFairyTaleVO.setIsFavorite(YesNoEnum.NO);
            log.info("用户未登录，收藏状态设置为null");
        }

        return randomFairyTaleVO;
    }

    /**
     * 验证截图参数
     * @param screenshots 截图字符串（多个图片ID，逗号分隔）
     * @param fieldName 字段名称，用于错误提示
     */
    private void validateScreenshots(String screenshots, String fieldName) {
        if (StrUtil.isBlank(screenshots)) {
            // 截图字段为空是允许的
            return;
        }

        // 分割图片ID字符串
        String[] imageIds = screenshots.split(",");

        // 验证图片ID数量限制（最多10张）
        if (imageIds.length > 10) {
            throw new BizException(BaseResponseCode.FAIL, fieldName + "最多只能上传10张图片");
        }

        // 收集有效的图片ID
        Set<Long> validImageIds = new HashSet<>();

        // 验证每个图片ID的格式
        for (String imageId : imageIds) {
            String trimmedId = imageId.trim();
            if (StrUtil.isBlank(trimmedId)) {
                throw new BizException(BaseResponseCode.FAIL, fieldName + "包含空的图片ID");
            }

            // 验证图片ID是否为有效的Long类型
            try {
                Long parsedId = Long.parseLong(trimmedId);
                validImageIds.add(parsedId);
            } catch (NumberFormatException e) {
                throw new BizException(BaseResponseCode.FAIL, fieldName + "包含无效的图片ID格式：" + trimmedId);
            }
        }

        // 批量查询图片文件是否存在
        if (!validImageIds.isEmpty()) {
            List<OssFile> existingFiles = ossFileService.listByIds(validImageIds);

            // 检查是否所有图片ID都存在
            Set<Long> existingFileIds = existingFiles.stream()
                    .map(OssFile::getId)
                    .collect(Collectors.toSet());

            // 找出不存在的图片ID
            Set<Long> notFoundIds = validImageIds.stream()
                    .filter(id -> !existingFileIds.contains(id))
                    .collect(Collectors.toSet());

            if (!notFoundIds.isEmpty()) {
                throw new BizException(BaseResponseCode.FAIL,
                    fieldName + "包含不存在的图片ID：" + notFoundIds.toString());
            }

            // 验证文件类型是否为图片
            for (OssFile ossFile : existingFiles) {
                if (ossFile.getFileType() != FileTypeEnum.IMAGE) {
                    throw new BizException(BaseResponseCode.FAIL,
                        fieldName + "包含非图片类型的文件ID：" + ossFile.getId());
                }
            }
        }
    }

    @Override
    @Transactional
    public void batchSynthesize(BatchSynthesizeDTO batchSynthesizeDTO) {
        log.info("开始批量语音合成，参数：{}", batchSynthesizeDTO);

        List<Long> storyIds = batchSynthesizeDTO.getStoryIds();
        if (storyIds == null || storyIds.isEmpty()) {
            throw new BizException(BaseResponseCode.FAIL, "故事ID列表不能为空");
        }

        // 去重故事ID列表
        List<Long> uniqueStoryIds = storyIds.stream().distinct().collect(Collectors.toList());
        log.info("去重后的故事ID数量：{}，原始数量：{}", uniqueStoryIds.size(), storyIds.size());

        // 查询所有故事
        List<FairyTale> fairyTales = listByIds(uniqueStoryIds);

        // 检查不存在的故事ID
        if (fairyTales.size() != uniqueStoryIds.size()) {
            Set<Long> existingIds = fairyTales.stream().map(FairyTale::getId).collect(Collectors.toSet());
            List<Long> notFoundIds = uniqueStoryIds.stream()
                .filter(id -> !existingIds.contains(id))
                .toList();
            throw new BizException(BaseResponseCode.FAIL, "以下故事ID不存在：" + notFoundIds);
        }

        // 检查故事状态和合成状态
        List<String> validationErrors = new ArrayList<>();
        for (FairyTale fairyTale : fairyTales) {
            validateStoryForBatchSynthesize(fairyTale, validationErrors);
        }

        if (!validationErrors.isEmpty()) {
            throw new BizException(BaseResponseCode.FAIL, "批量合成校验失败：" + String.join("; ", validationErrors));
        }

        // 第一阶段：只更新必要的参数，不更新合成状态和时间
        List<FairyTale> paramUpdateList = new ArrayList<>();
        List<Map<String, Object>> synthesizeMessages = new ArrayList<>();

        for (FairyTale fairyTale : fairyTales) {
            // 更新故事为未发布状态
            fairyTale.setPublishStatus(PublishStatusEnum.UNPUBLISHED);

            // 更新语音合成参数
            updateSynthesizeParameters(fairyTale, batchSynthesizeDTO);

            // 注意：不在这里设置最近合成时间，在实际合成时设置

            paramUpdateList.add(fairyTale);

            // 准备合成消息
            addSynthesizeMessages(fairyTale, synthesizeMessages);
        }

        // 批量更新参数（不包括合成状态和时间）
        updateBatchById(paramUpdateList);
        log.info("批量更新故事参数完成，共{}个故事", paramUpdateList.size());

        // 异步执行批量合成任务
        if (!synthesizeMessages.isEmpty()) {
            executeBatchSynthesizeAsync(synthesizeMessages);
            log.info("批量语音合成任务已启动，共{}条合成任务", synthesizeMessages.size());
        }

        log.info("批量语音合成任务启动完成，故事数量：{}，合成任务数量：{}",
            fairyTales.size(), synthesizeMessages.size());
    }

    /**
     * 更新语音合成参数
     */
    private void updateSynthesizeParameters(FairyTale fairyTale, BatchSynthesizeDTO dto) {
        fairyTale.setApiVersion(dto.getApiVersion());
        fairyTale.setAudioFormat(dto.getAudioFormat());
        fairyTale.setChineseVoiceType(dto.getChineseVoiceType());
        fairyTale.setEnglishVoiceType(dto.getEnglishVoiceType());
        fairyTale.setSampleRate(dto.getSampleRate());
        fairyTale.setChineseVoiceStyle(dto.getChineseVoiceStyle());
        fairyTale.setEnglishVoiceStyle(dto.getEnglishVoiceStyle());
        fairyTale.setChineseVolume(dto.getChineseVolume());
        fairyTale.setChineseSpeed(dto.getChineseSpeed());
        fairyTale.setChinesePitch(dto.getChinesePitch());
        fairyTale.setChineseSentenceInterval(dto.getChineseSentenceInterval());
        fairyTale.setEnglishVolume(dto.getEnglishVolume());
        fairyTale.setEnglishSpeed(dto.getEnglishSpeed());
        fairyTale.setEnglishPitch(dto.getEnglishPitch());
        fairyTale.setEnglishSentenceInterval(dto.getEnglishSentenceInterval());

        // 注意：不在这里设置合成状态，在实际合成时设置
    }

    /**
     * 添加合成消息到消息列表
     */
    private void addSynthesizeMessages(FairyTale fairyTale, List<Map<String, Object>> messages) {
        Long storyId = fairyTale.getId();

        // 中文版本（所有故事都需要）
        addMessage(messages, storyId, 3); // 中文标题+内容

        // 英文版本（根据需要添加）
        if (YesNoEnum.YES.equals(fairyTale.getNeedEnglishVersion()) || YesNoEnum.YES.equals(fairyTale.getNeedBilingualVersion())) {
            addMessage(messages, storyId, 6); // 英文标题+内容
        }
    }

    /**
     * 添加单个合成消息
     */
    private void addMessage(List<Map<String, Object>> messages, Long storyId, Integer target) {
        Map<String, Object> message = new HashMap<>();
        message.put("storyId", storyId);
        message.put("target", target);
        messages.add(message);
    }

    /**
     * 异步执行批量合成任务
     */
    @Async("commonExecutor")
    public void executeBatchSynthesizeAsync(List<Map<String, Object>> synthesizeMessages) {
        log.info("开始异步执行批量语音合成，总任务数：{}", synthesizeMessages.size());

        // 分批处理，每批10个
        int batchSize = 10;
        List<List<Map<String, Object>>> batches = partitionList(synthesizeMessages, batchSize);

        for (int i = 0; i < batches.size(); i++) {
            List<Map<String, Object>> batch = batches.get(i);
            log.info("开始处理第{}批合成任务，任务数：{}", i + 1, batch.size());

            try {
                // 并行处理当前批次的任务
                List<CompletableFuture<Void>> futures = batch.stream()
                    .map(this::processSingleSynthesizeTask)
                    .toList();

                // 等待当前批次所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                log.info("第{}批合成任务处理完成", i + 1);

                // 批次间间隔，避免过于频繁的请求
                if (i < batches.size() - 1) {
                    Thread.sleep(2000); // 间隔2秒
                }

            } catch (Exception e) {
                log.error("第{}批合成任务处理失败", i + 1, e);
            }
        }

        log.info("批量语音合成任务全部完成");
    }

    /**
     * 处理单个合成任务
     */
    private CompletableFuture<Void> processSingleSynthesizeTask(Map<String, Object> message) {
        return CompletableFuture.runAsync(() -> {
            Long storyId = (Long) message.get("storyId");
            Integer target = (Integer) message.get("target");

            try {
                log.debug("开始处理批量合成任务，故事ID：{}，目标：{}", storyId, target);

                // 调用专门的批量合成方法，不复用现有的合成方法
                executeSingleBatchSynthesize(storyId, target);

                log.debug("批量合成任务处理完成，故事ID：{}，目标：{}", storyId, target);

            } catch (Exception e) {
                log.error("处理批量合成任务失败，故事ID：{}，目标：{}", storyId, target, e);

                // 失败处理：恢复数据库状态
                handleSynthesizeFailure(storyId, target, e);
            }
        }, executor);
    }

    /**
     * 执行单个批量合成任务（专门为批量合成设计，参数校验不同）
     */
    private void executeSingleBatchSynthesize(Long storyId, Integer target) {
        // 只支持target 3（中文标题+内容）和 6（英文标题+内容）
        if (target != 3 && target != 6) {
            throw new BizException(BaseResponseCode.FAIL, "批量合成只支持target 3和6，当前target：" + target);
        }

        // 1. 重新查询故事，确保数据最新
        FairyTale fairyTale = getById(storyId);
        if (fairyTale == null) {
            throw new BizException(BaseResponseCode.FAIL, "故事不存在，ID：" + storyId);
        }

        // 2. 简化的状态检查（批量合成时的校验逻辑）
        validateBatchSynthesizeTask(fairyTale, target);

        // 3. 调用现有的TTS合成方法
        try {
            callBatchTtsApi(fairyTale, target);
        } catch (Exception e) {
            // TTS接口调用失败，更新状态为失败
            updateTtsStatusToFailed(storyId, target, e.getMessage());
            throw new BizException(BaseResponseCode.FAIL, "TTS接口调用失败：" + e.getMessage());
        }
    }

    /**
     * 批量合成任务的状态校验（比普通合成更宽松）
     */
    private void validateBatchSynthesizeTask(FairyTale fairyTale, Integer target) {
        if (target == 3) {
            // 中文标题+内容合成任务
            if (StrUtil.isBlank(fairyTale.getChineseTitle()) || StrUtil.isBlank(fairyTale.getChineseContent())) {
                throw new BizException(BaseResponseCode.FAIL, "中文标题或内容为空");
            }
        } else if (target == 6) {
            // 英文标题+内容合成任务
            if (!TranslationStatusEnum.SUCCESS.equals(fairyTale.getTranslationStatus())) {
                throw new BizException(BaseResponseCode.FAIL, "英文翻译未完成");
            }
            if (StrUtil.isBlank(fairyTale.getEnglishTitle()) || StrUtil.isBlank(fairyTale.getEnglishContent())) {
                throw new BizException(BaseResponseCode.FAIL, "英文标题或内容为空");
            }
        }
    }

    /**
     * 调用批量TTS合成接口
     */
    private void callBatchTtsApi(FairyTale fairyTale, Integer target) {
        try {
            // 设置AppId
            fairyTale.setAppId(systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_APP_ID));

            JSONObject jsonObject;
            LocalDateTime now = LocalDateTime.now();

            if (target == 3) {
                // 中文标题+内容合成
                fairyTale.setChineseTitleContentTtsStartTime(now);
                fairyTale.setChineseTitleContentRequestId(IdUtil.fastSimpleUUID());

                // 调用现有的组装方法（448行）
                jsonObject = assembleChineseTitleContentTtsRequest(fairyTale, null);

                fairyTale.setChineseTitleContentTaskId(jsonObject.getStr("task_id"));
                fairyTale.setChineseTitleContentTtsStatus(TtsStatusEnum.SYNTHESIZING);

            } else if (target == 6) {
                // 英文标题+内容合成
                fairyTale.setEnglishTitleContentTtsStartTime(now);
                fairyTale.setEnglishTitleContentRequestId(IdUtil.fastSimpleUUID());

                // 调用现有的组装方法（517行）
                jsonObject = assembleEnglishTitleContentTtsRequest(fairyTale, null);

                fairyTale.setEnglishTitleContentTaskId(jsonObject.getStr("task_id"));
                fairyTale.setEnglishTitleContentTtsStatus(TtsStatusEnum.SYNTHESIZING);

            } else {
                throw new BizException(BaseResponseCode.FAIL, "不支持的合成目标：" + target);
            }

            // 设置最近合成时间（实际合成开始的时间）
            fairyTale.setLastSynthesizeTime(now);

            // 一次性更新所有状态和时间
            boolean result = updateById(fairyTale);
            if (!result) {
                throw new BizException(BaseResponseCode.FAIL, "更新语音合成状态失败");
            }

            log.info("批量语音合成任务已启动，故事ID：{}，目标：{}，任务ID：{}",
                fairyTale.getId(), target, jsonObject.getStr("task_id"));

        } catch (Exception e) {
            log.error("调用批量TTS接口失败，故事ID：{}，目标：{}", fairyTale.getId(), target, e);
            throw e;
        }
    }



    /**
     * 处理合成失败的情况
     */
    private void handleSynthesizeFailure(Long storyId, Integer target, Exception e) {
        try {
            log.warn("开始处理合成失败的恢复操作，故事ID：{}，目标：{}", storyId, target);

            // 更新TTS状态为失败
            updateTtsStatusToFailed(storyId, target, e.getMessage());

            log.info("合成失败恢复操作完成，故事ID：{}，目标：{}", storyId, target);

        } catch (Exception recoveryException) {
            log.error("合成失败恢复操作也失败了，故事ID：{}，目标：{}", storyId, target, recoveryException);
        }
    }





    /**
     * 更新TTS状态为失败
     */
    private void updateTtsStatusToFailed(Long storyId, Integer target, String errorMessage) {
        try {
            FairyTale updateEntity = new FairyTale();
            updateEntity.setId(storyId);

            if (target == 3) {
                updateEntity.setChineseTitleContentTtsStatus(TtsStatusEnum.FAILED);
            } else if (target == 6) {
                updateEntity.setEnglishTitleContentTtsStatus(TtsStatusEnum.FAILED);
            } else {
                log.warn("批量合成失败处理：不支持的target：{}，故事ID：{}", target, storyId);
                return;
            }

            updateById(updateEntity);
            log.info("已更新TTS状态为失败，故事ID：{}，目标：{}，错误：{}", storyId, target, errorMessage);

        } catch (Exception e) {
            log.error("更新TTS状态为失败也失败了，故事ID：{}，目标：{}", storyId, target, e);
        }
    }

    /**
     * 将列表分割为指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(new ArrayList<>(list.subList(i, end)));
        }
        return batches;
    }

    /**
     * 验证故事是否可以进行批量合成
     */
    private void validateStoryForBatchSynthesize(FairyTale fairyTale, List<String> validationErrors) {
        String storyPrefix = "故事编号[" + fairyTale.getStoryNumber() + "]";

        // 1. 检查中文标题+内容合成状态：不能是合成中
        TtsStatusEnum chineseTitleContentStatus = fairyTale.getChineseTitleContentTtsStatus();
        if (TtsStatusEnum.SYNTHESIZING.equals(chineseTitleContentStatus)) {
            validationErrors.add(storyPrefix + "中文标题+内容正在合成中，不能重复合成");
        }

        // 2. 检查英文版本相关状态（如果需要英文版或双语版）
        if (YesNoEnum.YES.equals(fairyTale.getNeedEnglishVersion()) ||
            YesNoEnum.YES.equals(fairyTale.getNeedBilingualVersion())) {

            // 检查翻译状态：必须是翻译成功
            if (!TranslationStatusEnum.SUCCESS.equals(fairyTale.getTranslationStatus())) {
                validationErrors.add(storyPrefix + "需要英文版但翻译未成功，不能进行批量合成");
            } else {
                // 翻译成功的情况下，检查英文标题+内容合成状态：不能是合成中
                TtsStatusEnum englishTitleContentStatus = fairyTale.getEnglishTitleContentTtsStatus();
                if (TtsStatusEnum.SYNTHESIZING.equals(englishTitleContentStatus)) {
                    validationErrors.add(storyPrefix + "英文标题+内容正在合成中，不能重复合成");
                }
            }
        }
    }
}
