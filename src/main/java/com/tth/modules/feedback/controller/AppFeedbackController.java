package com.tth.modules.feedback.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.bean.BeanUtil;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.annotation.RepeatSubmit;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.response.R;
import com.tth.framework.utils.AuthUtil;
import com.tth.modules.feedback.model.dto.AddAppFeedbackDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.feedback.service.AppFeedbackService;
import com.tth.modules.feedback.entity.AppFeedback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.Operation;

/**
 * <p>
 * 功能反馈表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Slf4j
@RestController
@Tag(name = "功能反馈表接口【appFeedback】", description = "权限前缀：【appFeedback】")
@RequestMapping("/appFeedback")
public class AppFeedbackController extends BaseController<AppFeedback> {

    @Resource
    private AppFeedbackService appFeedbackService;

    @Override
    protected String getModuleName() {
        return "appFeedback";
    }

    @Override
    public BaseService<AppFeedback> getBaseService() {
        return appFeedbackService;
    }

    @RepeatSubmit
    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "功能反馈模块", value = "添加功能反馈")
    @Operation(summary = "添加功能反馈", description = "用户提交功能反馈或建议")
    @PostMapping("/add")
    public R<Void> addFeedback(@RequestBody @Valid AddAppFeedbackDTO addAppFeedbackDTO) {
        log.info("用户添加功能反馈，参数：{}", addAppFeedbackDTO);

        // 获取当前登录用户ID
        Long userBaseId = AuthUtil.getUserId();

        // 创建反馈记录
        AppFeedback appFeedback = new AppFeedback();
        BeanUtil.copyProperties(addAppFeedbackDTO, appFeedback);
        appFeedback.setUserBaseId(userBaseId);

        // 保存反馈记录
        boolean result = appFeedbackService.save(appFeedback);
        if (!result) {
            throw new BizException(BaseResponseCode.FAIL, "添加反馈失败");
        }

        log.info("用户添加功能反馈成功，反馈ID：{}", appFeedback.getId());
        return R.successWithMessage("反馈提交成功，感谢您的建议！");
    }
}
