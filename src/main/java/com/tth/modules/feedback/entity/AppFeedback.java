package com.tth.modules.feedback.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 功能反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_app_feedback")
@Schema(name = "AppFeedback", description = "功能反馈表")
public class AppFeedback extends BaseEntity {

    @Schema(description = "客户用户ID（关联tth_user_base）")
    private Long userBaseId;

    @Schema(description = "反馈内容")
    private String content;
}
