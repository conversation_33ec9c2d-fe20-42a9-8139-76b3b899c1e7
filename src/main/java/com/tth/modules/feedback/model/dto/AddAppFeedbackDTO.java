package com.tth.modules.feedback.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 添加功能反馈请求DTO
 */
@Data
@Schema(description = "添加功能反馈请求")
public class AddAppFeedbackDTO {

    @NotBlank(message = "反馈内容不能为空")
    @Size(min = 1, max = 2000, message = "反馈内容长度必须在1-2000字符之间")
    @Schema(description = "反馈内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "建议增加夜间模式功能")
    private String content;
}
