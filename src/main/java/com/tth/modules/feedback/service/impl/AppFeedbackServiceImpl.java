package com.tth.modules.feedback.service.impl;

import com.tth.modules.feedback.entity.AppFeedback;
import com.tth.modules.feedback.mapper.AppFeedbackMapper;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.feedback.service.AppFeedbackService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 功能反馈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-28
 */
@Slf4j
@Service
public class AppFeedbackServiceImpl extends BaseServiceImpl<AppFeedbackMapper, AppFeedback> implements AppFeedbackService {

    @Resource
    private AppFeedbackMapper appFeedbackMapper;
}
