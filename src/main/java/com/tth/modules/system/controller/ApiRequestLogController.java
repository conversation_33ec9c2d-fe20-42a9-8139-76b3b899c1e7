package com.tth.modules.system.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.system.service.ApiRequestLogService;
import com.tth.modules.system.entity.ApiRequestLog;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * API请求日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@RestController
@Tag(name = "API请求日志表接口【apiRequestLog】", description = "权限前缀：【apiRequestLog】")
@RequestMapping("/apiRequestLog")
public class ApiRequestLogController extends BaseController<ApiRequestLog> {

    @Resource
    private ApiRequestLogService apiRequestLogService;

    @Override
    protected String getModuleName() {
        return "apiRequestLog";
    }

    @Override
    public BaseService<ApiRequestLog> getBaseService() {
    return apiRequestLogService;
    }
}
