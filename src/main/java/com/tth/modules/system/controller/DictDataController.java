package com.tth.modules.system.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.system.service.DictDataService;
import com.tth.modules.system.entity.DictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 数据字典数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Slf4j
@RestController
@Tag(name = "数据字典数据表接口【dictData】", description = "权限前缀：【dictData】")
@RequestMapping("/dictData")
public class DictDataController extends BaseController<DictData> {

    @Resource
    private DictDataService dictDataService;

    @Override
    protected String getModuleName() {
        return "dictData";
    }

    @Override
    public BaseService<DictData> getBaseService() {
    return dictDataService;
    }
}
