package com.tth.modules.system.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.system.service.DictTypeService;
import com.tth.modules.system.entity.DictType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 数据字典类型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Slf4j
@RestController
@Tag(name = "数据字典类型表接口【dictType】", description = "权限前缀：【dictType】")
@RequestMapping("/dictType")
public class DictTypeController extends BaseController<DictType> {

    @Resource
    private DictTypeService dictTypeService;

    @Override
    protected String getModuleName() {
        return "dictType";
    }

    @Override
    public BaseService<DictType> getBaseService() {
    return dictTypeService;
    }
}
