package com.tth.modules.system.controller;

import cn.hutool.core.collection.CollUtil;
import com.tth.common.model.R;
import com.tth.modules.system.model.dto.BatchFileUrlDTO;
import com.tth.modules.system.model.vo.FileUrlVO;
import com.tth.modules.system.service.OssFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 文件URL获取接口
 * 提供按需获取文件URL的能力
 */
@Slf4j
@RestController
@RequestMapping("/api/file-url")
@Tag(name = "文件URL管理", description = "文件URL获取相关接口")
public class FileUrlController {

    @Resource
    private OssFileService ossFileService;

    /**
     * 获取单个文件URL
     */
    @GetMapping("/{fileId}")
    @Operation(summary = "获取单个文件URL")
    public R<FileUrlVO> getFileUrl(@PathVariable Long fileId,
                                   @RequestParam(defaultValue = "60") Integer expireMinutes) {
        
        if (fileId == null) {
            return R.fail("文件ID不能为空");
        }
        
        try {
            String url = ossFileService.getFileUrlById(fileId, expireMinutes);
            
            FileUrlVO vo = new FileUrlVO();
            vo.setFileId(fileId);
            vo.setUrl(url);
            vo.setExpireMinutes(expireMinutes);
            
            return R.success(vo);
            
        } catch (Exception e) {
            log.error("获取文件URL失败，fileId: {}", fileId, e);
            return R.fail("获取文件URL失败");
        }
    }

    /**
     * 批量获取文件URL
     */
    @PostMapping("/batch")
    @Operation(summary = "批量获取文件URL")
    public R<Map<Long, String>> batchGetFileUrls(@RequestBody BatchFileUrlDTO dto) {
        
        if (CollUtil.isEmpty(dto.getFileIds())) {
            return R.success(new HashMap<>());
        }
        
        try {
            Set<Long> fileIds = dto.getFileIds();
            int expireMinutes = dto.getExpireMinutes() != null ? dto.getExpireMinutes() : 60;
            
            // 批量获取文件URL
            Map<Long, String> urlMap = ossFileService.batchGetFileUrls(fileIds, expireMinutes);
            
            log.debug("批量获取文件URL完成，请求{}个文件，返回{}个URL", 
                fileIds.size(), urlMap.size());
            
            return R.success(urlMap);
            
        } catch (Exception e) {
            log.error("批量获取文件URL失败", e);
            return R.fail("批量获取文件URL失败");
        }
    }

    /**
     * 获取音频文件播放URL（针对童话故事优化）
     */
    @GetMapping("/audio/{fileId}")
    @Operation(summary = "获取音频文件播放URL")
    public R<FileUrlVO> getAudioUrl(@PathVariable Long fileId) {
        
        if (fileId == null) {
            return R.fail("文件ID不能为空");
        }
        
        try {
            // 音频文件使用8小时过期时间
            String url = ossFileService.getFileUrlById(fileId, 480);
            
            FileUrlVO vo = new FileUrlVO();
            vo.setFileId(fileId);
            vo.setUrl(url);
            vo.setExpireMinutes(480);
            vo.setFileType("audio");
            
            return R.success(vo);
            
        } catch (Exception e) {
            log.error("获取音频文件URL失败，fileId: {}", fileId, e);
            return R.fail("获取音频文件URL失败");
        }
    }

    /**
     * 批量获取音频文件URL
     */
    @PostMapping("/audio/batch")
    @Operation(summary = "批量获取音频文件URL")
    public R<Map<Long, String>> batchGetAudioUrls(@RequestBody List<Long> fileIds) {
        
        if (CollUtil.isEmpty(fileIds)) {
            return R.success(new HashMap<>());
        }
        
        try {
            // 音频文件使用8小时过期时间
            Map<Long, String> urlMap = ossFileService.batchGetFileUrls(
                CollUtil.newHashSet(fileIds), 480);
            
            log.debug("批量获取音频URL完成，请求{}个文件，返回{}个URL", 
                fileIds.size(), urlMap.size());
            
            return R.success(urlMap);
            
        } catch (Exception e) {
            log.error("批量获取音频文件URL失败", e);
            return R.fail("批量获取音频文件URL失败");
        }
    }

    /**
     * 预热文件URL缓存
     */
    @PostMapping("/warmup")
    @Operation(summary = "预热文件URL缓存")
    public R<String> warmupCache(@RequestBody List<Long> fileIds,
                                 @RequestParam(defaultValue = "60") Integer expireMinutes) {
        
        if (CollUtil.isEmpty(fileIds)) {
            return R.success("无需预热");
        }
        
        try {
            // 预热缓存：提前生成并缓存文件URL
            ossFileService.batchGetFileUrls(CollUtil.newHashSet(fileIds), expireMinutes);
            
            return R.success("预热完成，共预热" + fileIds.size() + "个文件");
            
        } catch (Exception e) {
            log.error("预热文件URL缓存失败", e);
            return R.fail("预热缓存失败");
        }
    }
}
