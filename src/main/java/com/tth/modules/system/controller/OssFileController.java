package com.tth.modules.system.controller;

import com.tth.framework.filter.CustomContentCachingRequestWrapper;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.system.service.OssFileService;
import com.tth.modules.system.entity.OssFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;
import com.tth.framework.response.R;
import com.tth.framework.annotation.ApiLog;
import com.tth.thirdparty.aliyun.service.AliYunOssService;
import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 阿里云OSS文件信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Slf4j
@RestController
@Tag(name = "阿里云OSS文件信息表接口【ossFile】", description = "权限前缀：【ossFile】")
@RequestMapping("/ossFile")
public class OssFileController extends BaseController<OssFile> {

    @Resource
    private OssFileService ossFileService;

    @Resource
    private AliYunOssService aliYunOssService;

    @Override
    protected String getModuleName() {
        return "ossFile";
    }

    @Override
    public BaseService<OssFile> getBaseService() {
        return ossFileService;
    }

    @SaIgnore
    @ApiLog(module = "OSS文件模块", value = "OSS上传回调接口")
    @Operation(summary = "OSS上传回调接口", description = "处理阿里云OSS上传完成后的回调", hidden = true)
    @PostMapping("/callback")
    public R<Map<String, Object>> handleCallback(HttpServletRequest request, HttpServletResponse response) {
        try {
            String requestBody = ((CustomContentCachingRequestWrapper) request).getBodyString();

            // 验证OSS回调请求
            if (!aliYunOssService.verifyOSSCallbackRequest(request, requestBody)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("status", "error");
                errorResult.put("message", "非法请求");
                response.setStatus(HttpStatus.FORBIDDEN.value());
                log.error("童话故事音频上传OSS回调签名验证失败");
                return R.fail(errorResult);
            }

            // 从请求头中获取签名ID
            String signatureId = request.getHeader("x-oss-signature-id");

            // 调用service层处理业务逻辑
            Map<String, Object> result = ossFileService.handleOssUploadCallback(requestBody, signatureId);

            // 检查处理结果
            if ("error".equals(result.get("status"))) {
                response.setStatus(HttpStatus.FORBIDDEN.value());
                return R.fail(result);
            }

            return R.success(result);
        } catch (Exception e) {
            log.error("OSS回调处理失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("status", "error");
            errorResult.put("message", e.getMessage());
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return R.fail(errorResult);
        }
    }

}
