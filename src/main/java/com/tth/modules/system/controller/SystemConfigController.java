package com.tth.modules.system.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.modules.system.entity.SystemConfig;
import com.tth.modules.system.enums.SystemConfigGroupTypeEnum;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.framework.response.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

/**
 * <p>
 * 系统配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Slf4j
@Validated
@RestController
@Tag(name = "系统配置表接口【systemConfig】", description = "权限前缀：【systemConfig】")
@RequestMapping("/systemConfig")
public class SystemConfigController extends BaseController<SystemConfig> {

    @Resource
    private SystemConfigService systemConfigService;

    @Override
    protected String getModuleName() {
        return "systemConfig";
    }

    @Override
    public BaseService<SystemConfig> getBaseService() {
    return systemConfigService;
    }

    /**
     * 查询公开配置接口（开放接口，无需登录）
     * 仅返回非敏感信息的配置
     */
    @SaIgnore
    @GetMapping("/public/query")
    @Operation(summary = "查询公开配置", description = "查询公开配置信息，仅返回非敏感信息，无需登录")
    public R<SystemConfig> getPublicConfig(
            @Parameter(description = "分组类型", required = true)
            @RequestParam @NotNull(message = "分组类型不能为空") SystemConfigGroupTypeEnum groupType,

            @Parameter(description = "服务提供商", required = true)
            @RequestParam @NotNull(message = "服务提供商不能为空") SystemConfigProviderEnum provider,

            @Parameter(description = "配置键", required = true)
            @RequestParam @NotBlank(message = "配置键不能为空") String configKey) {

        log.debug("查询公开配置: groupType={}, provider={}, configKey={}",
                groupType, provider, configKey);

        SystemConfig config = systemConfigService.getPublicConfig(groupType, provider, configKey);

        if (config == null) {
            log.debug("未找到公开配置: groupType={}, provider={}, configKey={}",
                    groupType, provider, configKey);
            return R.fail("配置不存在或为敏感信息");
        }

        return R.success(config);
    }
}
