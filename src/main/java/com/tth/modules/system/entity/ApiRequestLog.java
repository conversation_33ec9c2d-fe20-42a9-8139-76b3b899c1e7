package com.tth.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.SuccessStatusEnum;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.system.enums.LogTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * API请求日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_api_request_log")
@Schema(name = "ApiRequestLog", description = "API请求日志表")
public class ApiRequestLog extends BaseEntity {

    @Schema(description = "日志类型：1-系统API日志，2-第三方API日志")
    private LogTypeEnum logType;

    @Schema(description = "请求ID，用于链路追踪")
    private String requestId;

    @Schema(description = "请求URL")
    private String url;

    @Schema(description = "请求方法(GET/POST等)")
    private String method;

    @Schema(description = "模块名称")
    private String module;

    @Schema(description = "操作描述")
    private String operation;

    @Schema(description = "操作用户ID")
    private Long userId;

    @Schema(description = "查询参数(URL参数)")
    private String queryParams;

    @Schema(description = "请求头参数")
    private String headerParams;

    @Schema(description = "请求体内容")
    private String requestBody;

    @Schema(description = "响应状态码，来自响应体中的code字段")
    private Integer responseStatus;

    @Schema(description = "业务状态(0成功,1失败)表示业务逻辑是否成功执行")
    private SuccessStatusEnum bizStatus;

    @Schema(description = "响应体内容")
    private String responseBody;

    @Schema(description = "客户端IP")
    private String ip;

    @Schema(description = "IP所在的实际中文地址")
    private String ipPosition;

    @Schema(description = "用户代理")
    private String userAgent;

    @Schema(description = "请求耗时(毫秒)")
    private Long duration;

    @Schema(description = "HTTP状态码(200、400、404、500等)")
    private Integer httpStatus;

    @Schema(description = "错误信息，包含异常堆栈")
    private String errorMsg;
}
