package com.tth.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据字典数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_dict_data")
@Schema(name = "DictData", description = "数据字典数据表")
public class DictData extends BaseEntity {

    @Schema(description = "字典类型编码")
    private String dictType;

    @Schema(description = "字典标签（显示标签值）")
    private String dictLabel;

    @Schema(description = "字典键值（字典值）")
    private String dictValue;

    @Schema(description = "实际存储的值")
    private String storeValue;

    @Schema(description = "排序")
    private Integer dictSort;

    @Schema(description = "CSS样式")
    private String cssClass;

    @Schema(description = "表格回显样式")
    private String listClass;

    @Schema(description = "是否默认（0-否，1-是）")
    private YesNoEnum isDefault;

    @Schema(description = "状态（0-禁用，1-启用）")
    private EnabledStatusEnum status;
}
