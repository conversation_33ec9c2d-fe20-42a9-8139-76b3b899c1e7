package com.tth.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据字典类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_dict_type")
@Schema(name = "DictType", description = "数据字典类型表")
public class DictType extends BaseEntity {

    @Schema(description = "字典类型名称")
    private String dictName;

    @Schema(description = "字典类型编码")
    private String dictType;

    @Schema(description = "状态（0-禁用，1-启用）")
    private EnabledStatusEnum status;

    @Schema(description = "排序")
    private Integer sort;
}
