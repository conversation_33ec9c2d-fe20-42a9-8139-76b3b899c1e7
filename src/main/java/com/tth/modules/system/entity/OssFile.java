package com.tth.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.system.enums.FileTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 阿里云OSS文件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_oss_file")
@Schema(name = "OssFile", description = "阿里云OSS文件信息表")
public class OssFile extends BaseEntity {

    @Schema(description = "文件名称")
    private String fileName;

    private Long userId;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件访问URL")
    private String fileUrl;

    @Schema(description = "文件扩展名")
    private String fileExt;

    @Schema(description = "文件大小(字节)")
    private Long fileSize;

    @Schema(description = "文件MIME类型")
    private String mimeType;

    @Schema(description = "资源类型(1-图片,2-视频,3-音频,4-文档,5-压缩包,6-APK安装包,99-其他)")
    private FileTypeEnum fileType;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "图片宽度")
    private Integer width;

    @Schema(description = "图片高度")
    private Integer height;

    @Schema(description = "图片格式，例如JPG、PNG等。该变量仅适用于图片格式，对于非图片格式，该变量的值为空。")
    private String format;

    @Schema(description = "OSS桶名称")
    private String bucketName;

    @Schema(description = "OSS对象名称")
    private String objectName;

    @Schema(description = "OSS ETag")
    private String etag;

    @Schema(description = "发起请求的客户端IP地址。")
    private String clientIp;

    @Schema(description = "ip中文地址")
    private String ipPosition;

    @Schema(description = "发起请求的接口名称，例如PutObject、PostObject等。")
    private String operation;

    @Schema(description = "关联业务ID")
    private Long businessId;

    @Schema(description = "关联业务类型")
    private String businessType;
}
