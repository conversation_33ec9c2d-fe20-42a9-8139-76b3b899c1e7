package com.tth.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.system.enums.SystemConfigGroupTypeEnum;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.enums.SystemConfigValueTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_system_config")
@Schema(name = "SystemConfig", description = "系统配置表")
public class SystemConfig extends BaseEntity {

    @Schema(description = "分组类型")
    private SystemConfigGroupTypeEnum groupType;

    @Schema(description = "服务提供商")
    private SystemConfigProviderEnum provider;

    @Schema(description = "配置键")
    private String configKey;

    @Schema(description = "配置值类型：1-字符串值,2-文档文件")
    private SystemConfigValueTypeEnum valueType;

    @Schema(description = "配置值（valueType=1时使用）")
    private String configValue;

    @MarkFileUrl(expireMinutes = 600)
    @Schema(description = "关联OSS文件ID（valueType=2时使用）")
    private Long ossFileId;

    @Schema(description = "配置描述")
    private String description;

    @Schema(description = "是否敏感信息（0-否，1-是）")
    private YesNoEnum isSensitive = YesNoEnum.YES;

    @Schema(description = "排序")
    private Integer sort;
}
