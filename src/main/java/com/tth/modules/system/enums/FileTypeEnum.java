package com.tth.modules.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件类型枚举
 * 用于标识上传到OSS的文件资源类型
 */
@Slf4j
@Getter
@AllArgsConstructor
@MarkDictType("文件类型")
@Schema(description = "文件类型")
public enum FileTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "图片")
    IMAGE(1, "图片"),

    @Schema(description = "视频")
    VIDEO(2, "视频"),

    @Schema(description = "音频")
    AUDIO(3, "音频"),

    @Schema(description = "文档")
    DOCUMENT(4, "文档"),

    @Schema(description = "压缩包")
    ARCHIVE(5, "压缩包"),

    @Schema(description = "APK安装包")
    APK(6, "APK安装包"),

    @Schema(description = "其他")
    OTHER(99, "其他");

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据MIME类型判断资源类型
     * @param mimeType MIME类型
     * @return 对应的资源类型枚举
     */
    public static FileTypeEnum fromMimeType(String mimeType) {
        if (mimeType == null || mimeType.isEmpty()) {
            return OTHER;
        }

        String mime = mimeType.toLowerCase();

        // 图片类型
        if (mime.startsWith("image/")) {
            return IMAGE;
        }

        // 视频类型
        if (mime.startsWith("video/")) {
            return VIDEO;
        }

        // 音频类型
        if (mime.startsWith("audio/")) {
            return AUDIO;
        }

        // 文档类型
        if (mime.startsWith("application/")) {
            // 常见文档MIME类型
            if (mime.matches("application/(pdf|msword|vnd\\.ms-.*|vnd\\.openxmlformats-.*|vnd\\.oasis\\.opendocument\\..*)")) {
                return DOCUMENT;
            }

            // 压缩包MIME类型
            if (mime.matches("application/(zip|x-rar-compressed|x-7z-compressed|x-tar|x-gzip|x-bzip2|java-archive)")) {
                return ARCHIVE;
            }

            // APK安装包MIME类型
            if (mime.equals("application/vnd.android.package-archive")) {
                return APK;
            }
        }

        // 文本文档
        if (mime.startsWith("text/")) {
            return DOCUMENT;
        }

        // 记录未识别的MIME类型，便于后续完善
        log.debug("未识别的MIME类型: {}", mime);

        // 默认为其他类型
        return OTHER;
    }
}