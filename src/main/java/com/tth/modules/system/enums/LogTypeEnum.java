package com.tth.modules.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "日志类型")
@Getter
@AllArgsConstructor
public enum LogTypeEnum implements IEnum<Integer> {

    @Schema(description = "系统接口日志")
    SYSTEM_API(1, "系统接口日志"),

    @Schema(description = "第三方接口日志")
    THIRD_PARTY_API(2, "第三方接口日志"),
    ;

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    @JsonValue
    private final String desc;
    ;

    @Override
    public Integer getValue() {
        return value;
    }
}
