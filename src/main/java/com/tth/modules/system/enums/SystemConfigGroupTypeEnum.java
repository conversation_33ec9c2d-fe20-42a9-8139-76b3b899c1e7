package com.tth.modules.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("配置分组类型")
@Schema(description = "配置分组类型")
public enum SystemConfigGroupTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "系统配置")
    SYSTEM(1, "系统配置"),

    @Schema(description = "第三方服务配置")
    THIRD_PARTY(2, "第三方服务配置"),

    @Schema(description = "APP配置")
    APP(3, "APP配置"),

    @Schema(description = "运营配置")
    OPERATIONS(4, "运营配置"),

    @Schema(description = "业务配置")
    BUSINESS(5, "业务配置"),

    @Schema(description = "订单配置")
    ORDER(6, "订单配置"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
