package com.tth.modules.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("服务提供商")
public enum SystemConfigProviderEnum implements IEnum<Integer>, IEnumDesc {

    // 系统内的配置，如SystemConfigGroupTypeEnum不为THIRD_PARTY，都属于基础配置
    @Schema(description = "基础")
    BASIC(0, "基础"),

    @Schema(description = "支付宝")
    ALIPAY(1, "支付宝"),

    @Schema(description = "微信")
    WECHAT_PAY(2, "微信"),

    @Schema(description = "阿里云OSS配置")
    ALI_YUN_OSS(3, "阿里云OSS配置"),

    @Schema(description = "腾讯云")
    TENCENT(4, "腾讯云"),

    @Schema(description = "火山引擎")
    VOLCENGINE(5, "火山引擎"),

    @Schema(description = "微信小程序")
    WECHAT_MINI_PROGRAM(6, "微信小程序"),
    ;

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
