package com.tth.modules.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统配置值类型枚举
 */
@Getter
@AllArgsConstructor
@MarkDictType("系统配置值类型")
@Schema(description = "系统配置值类型枚举")
public enum SystemConfigValueTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "字符串值")
    STRING(1, "字符串值"),

    @Schema(description = "文档文件")
    FILE(2, "文档文件");

    private final int value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
