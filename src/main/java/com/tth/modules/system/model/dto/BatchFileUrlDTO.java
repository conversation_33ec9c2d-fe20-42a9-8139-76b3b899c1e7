package com.tth.modules.system.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Set;

/**
 * 批量获取文件URL请求DTO
 */
@Data
@Schema(description = "批量获取文件URL请求")
public class BatchFileUrlDTO {

    @NotEmpty(message = "文件ID列表不能为空")
    @Schema(description = "文件ID列表")
    private Set<Long> fileIds;

    @Min(value = 1, message = "过期时间最少1分钟")
    @Max(value = 600, message = "过期时间最多600分钟")
    @Schema(description = "过期时间（分钟），默认60分钟")
    private Integer expireMinutes = 60;
}
