package com.tth.modules.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件URL响应VO
 */
@Data
@Schema(description = "文件URL信息")
public class FileUrlVO {

    @Schema(description = "文件ID")
    private Long fileId;

    @Schema(description = "文件访问URL")
    private String url;

    @Schema(description = "过期时间（分钟）")
    private Integer expireMinutes;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "URL过期时间戳")
    private Long expireTimestamp;

    /**
     * 设置过期时间戳
     */
    public void setExpireMinutes(Integer expireMinutes) {
        this.expireMinutes = expireMinutes;
        if (expireMinutes != null) {
            this.expireTimestamp = System.currentTimeMillis() + expireMinutes * 60 * 1000L;
        }
    }
}
