package com.tth.modules.system.service;

import com.tth.modules.system.entity.OssFile;
import com.tth.framework.base.BaseService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 阿里云OSS文件信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface OssFileService extends BaseService<OssFile> {

    /**
     * 根据文件ID获取文件访问URL
     * 如果bucket是私有的，返回预签名URL；如果是公开的，返回直接URL
     *
     * @param fileId 文件ID
     * @return 文件访问URL，如果文件不存在返回null
     */
    String getFileUrlById(Long fileId);

    /**
     * 根据文件ID获取文件访问URL
     * 如果bucket是私有的，返回预签名URL；如果是公开的，返回直接URL
     *
     * @param fileId 文件ID
     * @param expireMinutes 预签名URL过期时间（分钟），仅对私有bucket有效
     * @return 文件访问URL，如果文件不存在返回null
     */
    String getFileUrlById(Long fileId, int expireMinutes);

    /**
     * 批量获取文件访问URL
     *
     * @param fileIds 文件ID集合
     * @return 文件ID到URL的映射
     */
    Map<Long, String> batchGetFileUrls(Set<Long> fileIds);

    /**
     * 批量获取文件访问URL
     *
     * @param fileIds 文件ID集合
     * @param expireMinutes 预签名URL过期时间（分钟），仅对私有bucket有效
     * @return 文件ID到URL的映射
     */
    Map<Long, String> batchGetFileUrls(Set<Long> fileIds, int expireMinutes);

    /**
     * 处理OSS上传回调
     *
     * @param requestBody 回调请求体
     * @param signatureId 签名ID
     * @return 处理结果
     */
    Map<String, Object> handleOssUploadCallback(String requestBody, String signatureId);

    String getParamValue(Map<String, List<String>> paramsMap, String key);
}
