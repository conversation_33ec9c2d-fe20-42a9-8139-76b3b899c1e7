package com.tth.modules.system.service;

import com.tth.modules.system.entity.SystemConfig;
import com.tth.modules.system.enums.SystemConfigGroupTypeEnum;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.framework.base.BaseService;

import java.util.Map;

/**
 * <p>
 * 系统配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
public interface SystemConfigService extends BaseService<SystemConfig> {

    /**
     * 获取指定服务提供商的配置参数
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(SystemConfigProviderEnum provider, String configKey);

    /**
     * 获取指定服务提供商的所有配置参数
     *
     * @param provider 服务提供商枚举
     * @return 配置参数Map，键为参数名，值为参数值
     */
    Map<String, String> getAllConfigValues(SystemConfigProviderEnum provider);

    /**
     * 获取指定服务提供商的配置参数（使用枚举属性）
     *
     * @param provider 服务提供商枚举
     * @param property 配置属性枚举（必须实现Enum接口）
     * @return 配置值
     */
    <E extends Enum<E>> String getConfigValue(SystemConfigProviderEnum provider, E property);

    /**
     * 清除指定服务提供商的缓存
     *
     * @param provider 服务提供商枚举
     */
    void clearCache(SystemConfigProviderEnum provider);

    /**
     * 清除指定配置项的缓存
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     */
    void clearCache(SystemConfigProviderEnum provider, String configKey);

    /**
     * 清除所有系统配置缓存
     */
    void clearAllCache();

    /**
     * 预热指定服务提供商的配置缓存
     *
     * @param provider 服务提供商枚举
     */
    void warmUpCache(SystemConfigProviderEnum provider);

    /**
     * 获取配置的实际值（根据valueType自动判断返回字符串值还是文件URL）
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     * @return 配置的实际值（字符串或文件URL）
     */
    String getActualConfigValue(SystemConfigProviderEnum provider, String configKey);

    /**
     * 获取配置的文件URL（仅当valueType为FILE时有效）
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     * @return 文件URL，如果不是文件类型或文件不存在则返回null
     */
    String getConfigFileUrl(SystemConfigProviderEnum provider, String configKey);

    /**
     * 查询公开配置（仅返回非敏感信息）
     *
     * @param groupType 分组类型
     * @param provider 服务提供商
     * @param configKey 配置键
     * @return 配置对象，如果不存在或是敏感信息则返回null
     */
    SystemConfig getPublicConfig(SystemConfigGroupTypeEnum groupType, SystemConfigProviderEnum provider, String configKey);
}
