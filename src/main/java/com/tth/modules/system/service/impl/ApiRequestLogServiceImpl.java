package com.tth.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tth.framework.enums.LogLevel;
import com.tth.modules.system.entity.ApiRequestLog;
import com.tth.modules.system.mapper.ApiRequestLogMapper;
import com.tth.modules.system.service.ApiRequestLogService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * API请求日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Slf4j
@Service
public class ApiRequestLogServiceImpl extends BaseServiceImpl<ApiRequestLogMapper, ApiRequestLog> implements ApiRequestLogService {

    @Resource
    private ApiRequestLogMapper apiRequestLogMapper;

    /**
     * 定时清理日志
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanupLogs() {
        log.info("开始执行日志清理任务");

        try {
            // 清理7天前的基础日志
            LocalDateTime basicLogRetention = LocalDateTime.now().minusDays(7);
            int basicCount = deleteLogsByLevel(LogLevel.BASIC, basicLogRetention);
            log.info("已清理{}条基础级别日志", basicCount);

            // 清理30天前的标准日志
            LocalDateTime standardLogRetention = LocalDateTime.now().minusDays(30);
            int standardCount = deleteLogsByLevel(LogLevel.STANDARD, standardLogRetention);
            log.info("已清理{}条标准级别日志", standardCount);

            // 清理90天前的详细日志
            LocalDateTime detailedLogRetention = LocalDateTime.now().minusDays(90);
            int detailedCount = deleteLogsByLevel(LogLevel.DETAILED, detailedLogRetention);
            log.info("已清理{}条详细级别日志", detailedCount);

            log.info("日志清理任务完成，共清理{}条日志", basicCount + standardCount + detailedCount);
        } catch (Exception e) {
            log.error("日志清理任务执行失败", e);
        }
    }

    /**
     * 根据日志级别和时间删除日志
     * 使用逻辑删除
     *
     * @param level 日志级别
     * @param beforeTime 删除该时间之前的日志
     * @return 删除的记录数
     */
    private int deleteLogsByLevel(LogLevel level, LocalDateTime beforeTime) {
        try {
            // 创建更新条件
            UpdateWrapper<ApiRequestLog> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("deleted", true);
            updateWrapper.set("deleted_time", LocalDateTime.now());
            updateWrapper.set("deleted_by", 0L); // 系统用户ID

            // 设置查询条件
            updateWrapper.lt("created_time", beforeTime);

            // 根据日志级别设置不同的条件
            if (level == LogLevel.BASIC) {
                // 基础日志没有明确的标记，通过module和operation是否为空来判断
                updateWrapper.and(w -> w.isNull("module").or().eq("module", ""));
                updateWrapper.and(w -> w.isNull("operation").or().eq("operation", ""));
            } else if (level == LogLevel.STANDARD) {
                // 标准日志有module或operation，但没有header_params
                updateWrapper.and(w -> w.isNotNull("module").ne("module", "")
                        .or().isNotNull("operation").ne("operation", ""));
                updateWrapper.and(w -> w.isNull("header_params").or().eq("header_params", ""));
            } else if (level == LogLevel.DETAILED) {
                // 详细日志有header_params
                updateWrapper.isNotNull("header_params").ne("header_params", "");
            }

            // 执行逻辑删除
            return this.baseMapper.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("删除{}级别日志失败", level, e);
            return 0;
        }
    }


}
