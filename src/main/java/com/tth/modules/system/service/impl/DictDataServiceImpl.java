package com.tth.modules.system.service.impl;

import com.tth.modules.system.entity.DictData;
import com.tth.modules.system.mapper.DictDataMapper;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.system.service.DictDataService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据字典数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Slf4j
@Service
public class DictDataServiceImpl extends BaseServiceImpl<DictDataMapper, DictData> implements DictDataService {

    @Resource
    private DictDataMapper dictDataMapper;
}
