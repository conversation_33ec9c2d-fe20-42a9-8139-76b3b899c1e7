package com.tth.modules.system.service.impl;

import com.tth.modules.system.entity.DictType;
import com.tth.modules.system.mapper.DictTypeMapper;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.system.service.DictTypeService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据字典类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Slf4j
@Service
public class DictTypeServiceImpl extends BaseServiceImpl<DictTypeMapper, DictType> implements DictTypeService {

    @Resource
    private DictTypeMapper dictTypeMapper;
}
