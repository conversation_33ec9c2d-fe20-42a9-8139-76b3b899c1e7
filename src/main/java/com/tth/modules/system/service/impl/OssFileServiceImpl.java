package com.tth.modules.system.service.impl;

import com.tth.modules.system.entity.OssFile;
import com.tth.modules.system.mapper.OssFileMapper;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.system.service.OssFileService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tth.thirdparty.aliyun.service.AliYunOssService;
import cn.hutool.core.util.StrUtil;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.aliyun.enums.AliYunOssProperty;
import com.tth.common.constant.RedisKeyConstants;
import com.tth.common.utils.RedisUtil;
import com.tth.modules.system.enums.FileTypeEnum;
import com.tth.framework.utils.IpUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <p>
 * 阿里云OSS文件信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Slf4j
@Service
public class OssFileServiceImpl extends BaseServiceImpl<OssFileMapper, OssFile> implements OssFileService {

    @Resource
    private OssFileMapper ossFileMapper;

    @Resource
    private AliYunOssService aliYunOssService;

    @Resource
    private SystemConfigService systemConfigService;



    @Override
    public String getFileUrlById(Long fileId) {
        OssFile ossFile = this.getById(fileId);
        return aliYunOssService.generatePreSignedUrl(ossFile.getObjectName());
    }

    @Override
    public String getFileUrlById(Long fileId, int expireMinutes) {
        if (fileId == null) {
            return null;
        }

        // 检查是否有自定义域名配置
        String customDomain = systemConfigService.getConfigValue(SystemConfigProviderEnum.ALI_YUN_OSS, AliYunOssProperty.CUSTOM_DOMAIN.name());

        if (StrUtil.isNotBlank(customDomain)) {
            // 公开bucket，直接返回URL，可以缓存objectName
            String objectName = getObjectNameFromCache(fileId);
            if (objectName == null) {
                return null;
            }
            return customDomain + "/" + objectName;
        } else {
            // 私有bucket，缓存预签名URL
            String urlCacheKey = RedisKeyConstants.Business.OssFile.FILE_URL + fileId + ":" + expireMinutes;
            String cachedUrl = RedisUtil.get(urlCacheKey, String.class);

            if (StrUtil.isNotBlank(cachedUrl)) {
                return cachedUrl;
            }

            // 生成新的预签名URL
            String objectName = getObjectNameFromCache(fileId);
            if (objectName == null) {
                return null;
            }

            String preSignedUrl = aliYunOssService.generatePreSignedUrl(objectName, expireMinutes);
            // 缓存时间比URL过期时间短10分钟，确保不会返回即将过期的URL
            int cacheExpire = (expireMinutes - 10) * 60;
            if (cacheExpire > 0) {
                RedisUtil.set(urlCacheKey, preSignedUrl, cacheExpire);
            }
            return preSignedUrl;
        }
    }

    @Override
    public Map<Long, String> batchGetFileUrls(Set<Long> fileIds) {
        return batchGetFileUrls(fileIds, 60);
    }

    @Override
    public Map<Long, String> batchGetFileUrls(Set<Long> fileIds, int expireMinutes) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, String> resultMap = new HashMap<>();

        // 检查是否有自定义域名配置
        String customDomain = systemConfigService.getConfigValue(SystemConfigProviderEnum.ALI_YUN_OSS, AliYunOssProperty.CUSTOM_DOMAIN.name());

        if (StrUtil.isNotBlank(customDomain)) {
            // 公开bucket，批量获取objectName并拼接URL
            Map<Long, String> objectNameMap = batchGetObjectNames(fileIds);
            for (Map.Entry<Long, String> entry : objectNameMap.entrySet()) {
                resultMap.put(entry.getKey(), customDomain + "/" + entry.getValue());
            }
        } else {
            // 私有bucket，需要生成预签名URL
            Map<Long, String> objectNameMap = batchGetObjectNames(fileIds);
            for (Map.Entry<Long, String> entry : objectNameMap.entrySet()) {
                Long fileId = entry.getKey();
                String objectName = entry.getValue();

                // 先检查缓存
                String urlCacheKey = RedisKeyConstants.Business.OssFile.FILE_URL + fileId + ":" + expireMinutes;
                String cachedUrl = RedisUtil.get(urlCacheKey, String.class);

                if (StrUtil.isNotBlank(cachedUrl)) {
                    resultMap.put(fileId, cachedUrl);
                } else {
                    // 生成预签名URL
                    String presignedUrl = aliYunOssService.generatePreSignedUrl(objectName, expireMinutes);
                    resultMap.put(fileId, presignedUrl);

                    // 缓存时间比URL过期时间短10分钟，确保不会返回即将过期的URL
                    int cacheExpire = (expireMinutes - 10) * 60;
                    if (cacheExpire > 0) {
                        RedisUtil.set(urlCacheKey, presignedUrl, cacheExpire);
                    }
                }
            }
        }

        return resultMap;
    }

    /**
     * 从缓存或数据库获取文件的objectName
     */
    private String getObjectNameFromCache(Long fileId) {
        // 先从缓存获取文件信息
        String cacheKey = RedisKeyConstants.Business.OssFile.FILE_INFO + fileId;
        OssFile ossFile = RedisUtil.get(cacheKey, OssFile.class);

        if (ossFile == null) {
            // 缓存未命中，查询数据库
            ossFile = this.getById(fileId);
            if (ossFile == null) {
                log.warn("文件不存在，文件ID: {}", fileId);
                return null;
            }
            // 存入缓存
            RedisUtil.set(cacheKey, ossFile, RedisKeyConstants.Business.OssFile.FILE_INFO_CACHE_EXPIRE_SECONDS);
        }

        return ossFile.getObjectName();
    }

    /**
     * 批量获取文件的objectName
     */
    private Map<Long, String> batchGetObjectNames(Set<Long> fileIds) {
        Map<Long, String> resultMap = new HashMap<>();
        Set<Long> uncachedIds = new HashSet<>();

        // 先从缓存获取
        for (Long fileId : fileIds) {
            String cacheKey = RedisKeyConstants.Business.OssFile.FILE_INFO + fileId;
            OssFile ossFile = RedisUtil.get(cacheKey, OssFile.class);
            if (ossFile != null) {
                resultMap.put(fileId, ossFile.getObjectName());
            } else {
                uncachedIds.add(fileId);
            }
        }

        // 批量查询未缓存的文件
        if (!uncachedIds.isEmpty()) {
            List<OssFile> ossFiles = this.listByIds(uncachedIds);
            for (OssFile ossFile : ossFiles) {
                resultMap.put(ossFile.getId(), ossFile.getObjectName());
                // 存入缓存
                String cacheKey = RedisKeyConstants.Business.OssFile.FILE_INFO + ossFile.getId();
                RedisUtil.set(cacheKey, ossFile, RedisKeyConstants.Business.OssFile.FILE_INFO_CACHE_EXPIRE_SECONDS);
            }
        }

        return resultMap;
    }

    @Override
    public Map<String, Object> handleOssUploadCallback(String requestBody, String signatureId) {
        log.info("处理OSS上传回调，签名ID：{}", signatureId);

        // 获取回调参数
        // 从 requestBody 中解析参数，格式如：bucket=xxx&object=xxx&etag=xxx...
        // 使用Hutool的HttpUtil.decodeParams方法解析URL编码的参数
        Map<String, List<String>> paramsMap = HttpUtil.decodeParams(requestBody, StandardCharsets.UTF_8);

        // 直接获取各个参数的第一个值
        String bucket = getParamValue(paramsMap, "bucket");
        String objectName = getParamValue(paramsMap, "object");
        String etag = getParamValue(paramsMap, "etag");
        String size = getParamValue(paramsMap, "size");
        String mimeType = getParamValue(paramsMap, "mimeType");
        String width = getParamValue(paramsMap, "width");
        String height = getParamValue(paramsMap, "height");
        String format = getParamValue(paramsMap, "format");
        String clientIp = getParamValue(paramsMap, "clientIp");
        String operation = getParamValue(paramsMap, "operation");

        // 验证签名是否有效（如果没有签名ID，则跳过验证，兼容旧版本）
        if (StrUtil.isNotBlank(signatureId)) {
            boolean isValidSignature = aliYunOssService.verifyAndRemoveSignature(signatureId, objectName);
            if (!isValidSignature) {
                log.warn("签名验证失败，签名ID: {}, 对象名: {}", signatureId, objectName);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("status", "error");
                errorResult.put("message", "签名无效或已使用");
                return errorResult;
            }
            log.info("签名验证成功，签名ID: {}, 对象名: {}", signatureId, objectName);
        } else {
            log.warn("未提供签名ID，跳过签名验证，对象名: {}", objectName);
        }

        // 从对象名中提取原始文件名
        String originalFileName = extractOriginalFileName(objectName);

        // 根据MIME类型和文件名确定文件类型
        FileTypeEnum fileType = FileTypeEnum.fromMimeType(mimeType);

        // 构建文件URL
        String customDomain = systemConfigService.getConfigValue(SystemConfigProviderEnum.ALI_YUN_OSS, AliYunOssProperty.CUSTOM_DOMAIN.name());
        String fileUrl;
        if(StrUtil.isNotBlank(customDomain)){
            fileUrl = customDomain + "/" + objectName;
        } else {
            String endpoint = systemConfigService.getConfigValue(SystemConfigProviderEnum.ALI_YUN_OSS, AliYunOssProperty.ENDPOINT.name());
            fileUrl = StrUtil.format("https://{}.{}/{}", bucket, endpoint, objectName);
        }

        // 创建OSS文件记录
        OssFile ossFile = new OssFile();
        ossFile.setFileName(originalFileName);
        ossFile.setUserId(extractUserIdFromObjectName(objectName));
        ossFile.setFilePath(objectName);
        ossFile.setFileUrl(fileUrl);
        ossFile.setFileExt(FileUtil.extName(originalFileName));
        ossFile.setFileSize(StrUtil.isNotBlank(size) ? Long.parseLong(size) : null);
        ossFile.setMimeType(mimeType);
        ossFile.setFileType(fileType);

        // 设置图片特有信息
        if (StrUtil.isNotBlank(width)) {
            try {
                ossFile.setWidth(Integer.parseInt(width));
            } catch (NumberFormatException e) {
                log.warn("图片宽度格式不正确: {}", width);
            }
        }

        if (StrUtil.isNotBlank(height)) {
            try {
                ossFile.setHeight(Integer.parseInt(height));
            } catch (NumberFormatException e) {
                log.warn("图片高度格式不正确: {}", height);
            }
        }

        ossFile.setFormat(format);

        // 设置OSS特有信息
        ossFile.setBucketName(bucket);
        ossFile.setObjectName(objectName);
        ossFile.setEtag(etag);
        ossFile.setClientIp(clientIp);
        ossFile.setIpPosition(IpUtil.getRealAddressByIP(clientIp));
        ossFile.setOperation(operation);

        // 保存到数据库
        save(ossFile);

        // 返回成功响应
        Map<String, Object> result = new HashMap<>();
        result.put("fileName", originalFileName);
        result.put("fileId", ossFile.getId());
        result.put("status", "success");

        log.info("OSS文件记录创建成功，文件ID：{}，对象名：{}", ossFile.getId(), objectName);
        return result;
    }

    /**
     * 从参数映射中获取指定键的第一个值
     *
     * @param paramsMap 参数映射
     * @param key 参数名
     * @return 参数值，如果不存在返回空字符串
     */
    public String getParamValue(Map<String, List<String>> paramsMap, String key) {
        List<String> values = paramsMap.get(key);
        return (values != null && !values.isEmpty()) ? values.get(0) : "";
    }

    /**
     * 从对象名中提取原始文件名
     * 格式：uploads/uuid-filename.ext
     *
     * @param objectName OSS对象名
     * @return 原始文件名
     */
    private String extractOriginalFileName(String objectName) {
        if (StrUtil.isBlank(objectName)) {
            return "";
        }

        // 查找最后一个斜杠
        int lastSlashIndex = objectName.lastIndexOf('/');
        if (lastSlashIndex < 0 || lastSlashIndex >= objectName.length() - 1) {
            return objectName; // 没有斜杠或斜杠在末尾
        }

        // 获取最后一部分
        String lastPart = objectName.substring(lastSlashIndex + 1);

        // 查找UUID分隔符的位置（假设使用'-'分隔）
        int uuidSeparatorIndex = lastPart.indexOf('-');
        if (uuidSeparatorIndex < 0 || uuidSeparatorIndex >= lastPart.length() - 1) {
            return lastPart; // 没有分隔符或分隔符在末尾
        }

        // 提取原始文件名
        return lastPart.substring(uuidSeparatorIndex + 1);
    }

    /**
     * 从对象名中提取用户ID
     * 格式：xxxx/雪花id/userId的十六进制/2020-10-10/a.txt
     * 其中userId以十六进制形式存储（使用HexUtil.toHex(userId)转换）
     *
     * @param objectName OSS对象名
     * @return 用户ID
     */
    private Long extractUserIdFromObjectName(String objectName) {
        if (objectName == null || objectName.isEmpty()) {
            return null;
        }

        // 分割路径
        String[] parts = objectName.split("/");

        // 检查是否有足够的部分
        if (parts.length < 2) {
            return null;
        }

        // 第三部分是十六进制形式的userId
        String hexUserId = parts[2];

        try {
            // 将十六进制字符串转换回Long类型
            return Long.parseLong(hexUserId, 16);
        } catch (NumberFormatException e) {
            log.warn("无法从对象名中解析十六进制用户ID: {}", objectName, e);
            return null;
        }
    }
}
