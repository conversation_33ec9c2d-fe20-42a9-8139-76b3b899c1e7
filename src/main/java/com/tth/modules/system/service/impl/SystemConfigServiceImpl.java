package com.tth.modules.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tth.framework.constant.CacheConstants;
import com.tth.modules.system.entity.SystemConfig;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.enums.SystemConfigValueTypeEnum;
import com.tth.modules.system.enums.SystemConfigGroupTypeEnum;
import com.tth.modules.system.mapper.SystemConfigMapper;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.utils.SpringContextHolder;
import com.tth.modules.system.service.OssFileService;
import com.tth.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-18
 */
@Slf4j
@Service
public class SystemConfigServiceImpl extends BaseServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {

    @Resource
    private SystemConfigMapper systemConfigMapper;

    /**
     * 获取指定服务提供商的配置参数
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     * @return 配置值
     */
    public String getConfigValue(SystemConfigProviderEnum provider, String configKey) {
        if (provider == null || configKey == null) {
            log.warn("获取配置参数时，服务提供商或配置键为空");
            return null;
        }

        // 构建缓存键
        String cacheKey = CacheConstants.SYSTEM_CONFIG_VALUE_KEY + provider.name() + ":" + configKey;

        // 先从缓存获取
        String cachedValue = RedisUtil.get(cacheKey, String.class);
        if (cachedValue != null) {
            log.debug("从缓存获取配置参数: provider={}, configKey={}, value={}", provider.name(), configKey, cachedValue);
            // 如果缓存值为空字符串，表示数据库中不存在该配置，返回null
            return StrUtil.isBlank(cachedValue) ? null : cachedValue;
        }

        // 缓存中没有，从数据库查询
        log.debug("缓存中未找到配置参数，从数据库查询: provider={}, configKey={}", provider.name(), configKey);
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getProvider, provider)
                .eq(SystemConfig::getConfigKey, configKey)
                .last("LIMIT 1");

        SystemConfig config = getOne(queryWrapper);
        String configValue = config != null ? config.getConfigValue() : null;

        // 将结果存入缓存（包括null值，用空字符串表示）
        String cacheValue = configValue != null ? configValue : "";
        RedisUtil.set(cacheKey, cacheValue, CacheConstants.SYSTEM_CONFIG_CACHE_EXPIRE);
        log.debug("配置参数已缓存: provider={}, configKey={}, value={}", provider.name(), configKey, configValue);

        return configValue;
    }

    /**
     * 获取指定服务提供商的所有配置参数
     *
     * @param provider 服务提供商枚举
     * @return 配置参数Map，键为参数名，值为参数值
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getAllConfigValues(SystemConfigProviderEnum provider) {
        if (provider == null) {
            log.warn("获取所有配置参数时，服务提供商为空");
            return Collections.emptyMap();
        }

        // 构建缓存键
        String cacheKey = CacheConstants.SYSTEM_CONFIG_PROVIDER_KEY + provider.name();

        // 先从缓存获取
        Map<String, String> cachedConfigMap = RedisUtil.getMap(cacheKey, String.class, String.class);
        if (cachedConfigMap != null && !cachedConfigMap.isEmpty()) {
            log.debug("从缓存获取所有配置参数: provider={}, size={}", provider.name(), cachedConfigMap.size());
            return cachedConfigMap;
        }

        // 缓存中没有，从数据库查询
        log.debug("缓存中未找到所有配置参数，从数据库查询: provider={}", provider.name());
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getProvider, provider);

        List<SystemConfig> configList = list(queryWrapper);
        Map<String, String> configMap;

        if (configList == null || configList.isEmpty()) {
            log.warn("未找到服务提供商[{}]的配置参数", provider.getDesc());
            configMap = Collections.emptyMap();
        } else {
            configMap = new HashMap<>(configList.size());
            for (SystemConfig config : configList) {
                String value;
                // 根据valueType返回相应的值
                if (SystemConfigValueTypeEnum.FILE.equals(config.getValueType())) {
                    // 文件类型，返回文件URL
                    if (config.getOssFileId() != null) {
                        // 使用SpringContextHolder避免循环依赖
                       OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
                       value = ossFileService.getFileUrlById(config.getOssFileId());
                    } else {
                        value = null;
                    }
                } else {
                    // 字符串类型，返回配置值
                    value = config.getConfigValue();
                }
                configMap.put(config.getConfigKey(), value);
            }
        }

        // 将结果存入缓存
        RedisUtil.set(cacheKey, configMap, CacheConstants.SYSTEM_CONFIG_CACHE_EXPIRE);
        log.debug("所有配置参数已缓存: provider={}, size={}", provider.name(), configMap.size());

        return configMap;
    }

    /**
     * 获取指定服务提供商的配置参数（使用枚举属性）
     *
     * @param provider 服务提供商枚举
     * @param property 配置属性枚举（必须实现Enum接口）
     * @return 配置值
     */
    public <E extends Enum<E>> String getConfigValue(SystemConfigProviderEnum provider, E property) {
        if (provider == null || property == null) {
            log.warn("获取配置参数时，服务提供商或属性枚举为空");
            return null;
        }

        return getConfigValue(provider, property.name());
    }

    /**
     * 清除指定服务提供商的缓存
     *
     * @param provider 服务提供商枚举
     */
    public void clearCache(SystemConfigProviderEnum provider) {
        if (provider == null) {
            log.warn("清除缓存时，服务提供商为空");
            return;
        }

        try {
            // 清除单个配置值缓存
            String valueKeyPrefix = CacheConstants.SYSTEM_CONFIG_VALUE_KEY + provider.name() + ":";
            RedisUtil.deleteByPrefix(valueKeyPrefix);

            // 清除所有配置缓存
            String providerKey = CacheConstants.SYSTEM_CONFIG_PROVIDER_KEY + provider.name();
            RedisUtil.delete(providerKey);

            log.info("已清除服务提供商[{}]的所有配置缓存", provider.name());
        } catch (Exception e) {
            log.error("清除配置缓存异常: provider={}, error={}", provider.name(), e.getMessage(), e);
        }
    }

    /**
     * 清除指定配置项的缓存
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     */
    public void clearCache(SystemConfigProviderEnum provider, String configKey) {
        if (provider == null || configKey == null) {
            log.warn("清除缓存时，服务提供商或配置键为空");
            return;
        }

        try {
            // 清除单个配置值缓存
            String cacheKey = CacheConstants.SYSTEM_CONFIG_VALUE_KEY + provider.name() + ":" + configKey;
            RedisUtil.delete(cacheKey);

            // 清除所有配置缓存（因为单个配置变更会影响整体）
            String providerKey = CacheConstants.SYSTEM_CONFIG_PROVIDER_KEY + provider.name();
            RedisUtil.delete(providerKey);

            log.info("已清除配置缓存: provider={}, configKey={}", provider.name(), configKey);
        } catch (Exception e) {
            log.error("清除配置缓存异常: provider={}, configKey={}, error={}", provider.name(), configKey, e.getMessage(), e);
        }
    }

    /**
     * 清除所有系统配置缓存
     */
    public void clearAllCache() {
        try {
            RedisUtil.deleteByPrefix(CacheConstants.SYSTEM_CONFIG_KEY);
            log.info("已清除所有系统配置缓存");
        } catch (Exception e) {
            log.error("清除所有系统配置缓存异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 预热指定服务提供商的配置缓存
     *
     * @param provider 服务提供商枚举
     */
    public void warmUpCache(SystemConfigProviderEnum provider) {
        if (provider == null) {
            log.warn("预热缓存时，服务提供商为空");
            return;
        }

        try {
            log.info("开始预热服务提供商[{}]的配置缓存", provider.name());
            // 调用getAllConfigValues方法，会自动缓存结果
            Map<String, String> configMap = getAllConfigValues(provider);
            log.info("预热完成，服务提供商[{}]共缓存{}个配置项", provider.name(), configMap.size());
        } catch (Exception e) {
            log.error("预热配置缓存异常: provider={}, error={}", provider.name(), e.getMessage(), e);
        }
    }

    /**
     * 获取配置的实际值（根据valueType自动判断返回字符串值还是文件URL）
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     * @return 配置的实际值（字符串或文件URL）
     */
    @Override
    public String getActualConfigValue(SystemConfigProviderEnum provider, String configKey) {
        if (provider == null || configKey == null) {
            log.warn("获取配置实际值时，服务提供商或配置键为空");
            return null;
        }

        // 查询完整的配置记录
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getProvider, provider)
                .eq(SystemConfig::getConfigKey, configKey)
                .last("LIMIT 1");

        SystemConfig config = getOne(queryWrapper);
        if (config == null) {
            log.debug("未找到配置: provider={}, configKey={}", provider.name(), configKey);
            return null;
        }

        // 根据valueType返回相应的值
        if (SystemConfigValueTypeEnum.FILE.equals(config.getValueType())) {
            // 文件类型，返回文件URL
            if (config.getOssFileId() != null) {
                // 使用SpringContextHolder避免循环依赖
                OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
                return ossFileService.getFileUrlById(config.getOssFileId());
            }
            return null;
        } else {
            // 字符串类型，返回配置值
            return config.getConfigValue();
        }
    }

    /**
     * 获取配置的文件URL（仅当valueType为FILE时有效）
     *
     * @param provider 服务提供商枚举
     * @param configKey 配置键
     * @return 文件URL，如果不是文件类型或文件不存在则返回null
     */
    @Override
    public String getConfigFileUrl(SystemConfigProviderEnum provider, String configKey) {
        if (provider == null || configKey == null) {
            log.warn("获取配置文件URL时，服务提供商或配置键为空");
            return null;
        }

        // 查询完整的配置记录
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getProvider, provider)
                .eq(SystemConfig::getConfigKey, configKey)
                .eq(SystemConfig::getValueType, SystemConfigValueTypeEnum.FILE)
                .last("LIMIT 1");

        SystemConfig config = getOne(queryWrapper);
        if (config == null) {
            log.debug("未找到文件类型配置: provider={}, configKey={}", provider.name(), configKey);
            return null;
        }

        // 返回文件URL
        if (config.getOssFileId() != null) {
            // 使用SpringContextHolder避免循环依赖
            OssFileService ossFileService = SpringContextHolder.getBean(OssFileService.class);
            return ossFileService.getFileUrlById(config.getOssFileId());
        }
        return null;
    }

    /**
     * 查询公开配置（仅返回非敏感信息）
     *
     * @param groupType 分组类型
     * @param provider 服务提供商
     * @param configKey 配置键
     * @return 配置对象，如果不存在或是敏感信息则返回null
     */
    @Override
    public SystemConfig getPublicConfig(SystemConfigGroupTypeEnum groupType, SystemConfigProviderEnum provider, String configKey) {
        // 查询配置，限制为非敏感信息
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemConfig::getGroupType, groupType)
                .eq(SystemConfig::getProvider, provider)
                .eq(SystemConfig::getConfigKey, configKey)
                .eq(SystemConfig::getIsSensitive, YesNoEnum.NO) // 只查询非敏感信息
                .last("LIMIT 1");

        SystemConfig config = getOne(queryWrapper);
        if (config == null) {
            log.debug("未找到公开配置: groupType={}, provider={}, configKey={}",
                    groupType.getDesc(), provider.getDesc(), configKey);
            return null;
        }

        log.debug("查询到公开配置: groupType={}, provider={}, configKey={}, valueType={}",
                groupType.getDesc(), provider.getDesc(), configKey, config.getValueType());
        return config;
    }
}
