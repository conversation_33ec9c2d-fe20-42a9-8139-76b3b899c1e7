package com.tth.modules.user.controller;

import com.tth.framework.response.R;
import com.tth.modules.user.model.vo.AdminUserVO;
import com.tth.modules.user.model.dto.CreateAdminUserDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.AdminUserService;
import com.tth.modules.user.entity.AdminUser;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 系统用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "管理员用户接口【adminUser】", description = "权限前缀：【adminUser】")
@RequestMapping("/admin/user")
public class AdminUserController extends BaseController<AdminUser> {

    @Resource
    private AdminUserService adminUserService;

    @Override
    protected String getModuleName() {
        return "adminUser";
    }

    @Override
    public BaseService<AdminUser> getBaseService() {
        return adminUserService;
    }

    @PostMapping("/create")
    @Operation(summary = "创建管理员用户", description = "创建新的管理员用户")
    public R<AdminUserVO> createAdminUser(@RequestBody @Valid CreateAdminUserDTO createAdminUserDTO) {
        AdminUserVO adminUserVO = adminUserService.createAdminUser(createAdminUserDTO);
        return R.success(adminUserVO);
    }
}
