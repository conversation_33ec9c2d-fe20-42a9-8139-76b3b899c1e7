package com.tth.modules.user.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.tth.common.enums.VerifyStatusEnum;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.response.R;
import com.tth.framework.utils.AuthUtil;
import com.tth.modules.user.dto.ReviewVerifyDTO;
import com.tth.modules.user.dto.SubmitVerifyDTO;
import com.tth.modules.user.dto.UpdateUserInfoDTO;
import com.tth.modules.user.vo.UserVerifyStatusVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.CustomerUserService;
import com.tth.modules.user.entity.CustomerUser;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 客户用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "客户用户表接口【customerUser】", description = "权限前缀：【customerUser】")
@RequestMapping("/customerUser")
public class CustomerUserController extends BaseController<CustomerUser> {

    @Resource
    private CustomerUserService customerUserService;

    @Override
    protected String getModuleName() {
        return "customerUser";
    }

    @Override
    public BaseService<CustomerUser> getBaseService() {
        return customerUserService;
    } 
     
    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "客户用户模块", value = "查询用户实名认证状态")
    @Operation(summary = "查询用户实名认证状态", description = "获取当前用户的实名认证状态信息")
    @GetMapping("/verifyStatus")
    public R<UserVerifyStatusVO> getUserVerifyStatus() {
        Long userBaseId = AuthUtil.getUserId();
        UserVerifyStatusVO vo = customerUserService.getUserVerifyStatus(userBaseId);
        return R.success(vo);
    }

    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "客户用户模块", value = "提交实名认证")
    @Operation(summary = "提交实名认证", description = "用户提交实名认证信息，只有未认证或认证失败状态才可以提交")
    @PostMapping("/submitVerify")
    public R<String> submitVerify(@RequestBody @Valid SubmitVerifyDTO dto) {
        Long userBaseId = AuthUtil.getUserId();
        customerUserService.submitVerify(userBaseId, dto);
        return R.successWithMessage("实名认证信息提交成功，请等待审核");
    }

    @ApiLog(module = "客户用户模块", value = "审核实名认证")
    @Operation(summary = "审核实名认证", description = "管理员审核用户实名认证，可设置为已认证或认证失败状态")
    @PostMapping("/reviewVerify")
    public R<String> reviewVerify(@RequestBody @Valid ReviewVerifyDTO dto) {
        customerUserService.reviewVerify(dto);
        String statusDesc = dto.getVerifyStatus() == VerifyStatusEnum.VERIFIED ? "已认证" : "认证失败";
        return R.successWithMessage("用户实名认证状态已更新为：" + statusDesc);
    }

    @SaCheckRole({"CUSTOMER"})
    @ApiLog(module = "客户用户模块", value = "更新用户信息")
    @Operation(summary = "更新用户信息", description = "客户用户更新个人信息")
    @PostMapping("/updateInfo")
    public R<String> updateUserInfo(@RequestBody @Valid UpdateUserInfoDTO dto) {
        Long userBaseId = AuthUtil.getUserId();
        customerUserService.updateUserInfo(userBaseId, dto);
        return R.successWithMessage("用户信息更新成功");
    }
}
