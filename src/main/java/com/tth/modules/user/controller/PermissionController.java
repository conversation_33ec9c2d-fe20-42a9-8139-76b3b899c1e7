package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.PermissionService;
import com.tth.modules.user.entity.Permission;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "权限表接口【permission】", description = "权限前缀：【permission】")
@RequestMapping("/permission")
public class PermissionController extends BaseController<Permission> {

    @Resource
    private PermissionService permissionService;

    @Override
    protected String getModuleName() {
        return "permission";
    }

    @Override
    public BaseService<Permission> getBaseService() {
        return permissionService;
    }
}
