package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.RoleService;
import com.tth.modules.user.entity.Role;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "角色表接口【role】", description = "权限前缀：【role】")
@RequestMapping("/role")
public class RoleController extends BaseController<Role> {

    @Resource
    private RoleService roleService;

    @Override
    protected String getModuleName() {
        return "role";
    }

    @Override
    public BaseService<Role> getBaseService() {
        return roleService;
    }
}
