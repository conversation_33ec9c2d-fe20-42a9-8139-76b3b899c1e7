package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.RolePermissionService;
import com.tth.modules.user.entity.RolePermission;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 角色权限关联表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "角色权限关联表接口【rolePermission】", description = "权限前缀：【rolePermission】")
@RequestMapping("/rolePermission")
public class RolePermissionController extends BaseController<RolePermission> {

    @Resource
    private RolePermissionService rolePermissionService;

    @Override
    protected String getModuleName() {
        return "rolePermission";
    }

    @Override
    public BaseService<RolePermission> getBaseService() {
        return rolePermissionService;
    }
}
