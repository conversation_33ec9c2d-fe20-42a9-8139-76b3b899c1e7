package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.UserAuthService;
import com.tth.modules.user.entity.UserAuth;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 用户认证表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "用户认证表接口【userAuth】", description = "权限前缀：【userAuth】")
@RequestMapping("/userAuth")
public class UserAuthController extends BaseController<UserAuth> {

    @Resource
    private UserAuthService userAuthService;

    @Override
    protected String getModuleName() {
        return "userAuth";
    }

    @Override
    public BaseService<UserAuth> getBaseService() {
        return userAuthService;
    }
}
