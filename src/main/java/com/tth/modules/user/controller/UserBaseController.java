package com.tth.modules.user.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.UserBaseService;
import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 用户基础表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "用户基础表接口【userBase】", description = "权限前缀：【userBase】")
@RequestMapping("/userBase")
public class UserBaseController extends BaseController<UserBase> {

    @Resource
    private UserBaseService userBaseService;

    @Override
    protected String getModuleName() {
        return "userBase";
    }

    @Override
    public BaseService<UserBase> getBaseService() {
        return userBaseService;
    }
}
