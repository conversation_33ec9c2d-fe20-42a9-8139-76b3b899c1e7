package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.UserDeviceService;
import com.tth.modules.user.entity.UserDevice;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 用户设备表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "用户设备表接口【userDevice】", description = "权限前缀：【userDevice】")
@RequestMapping("/userDevice")
public class UserDeviceController extends BaseController<UserDevice> {

    @Resource
    private UserDeviceService userDeviceService;

    @Override
    protected String getModuleName() {
        return "userDevice";
    }

    @Override
    public BaseService<UserDevice> getBaseService() {
        return userDeviceService;
    }
}
