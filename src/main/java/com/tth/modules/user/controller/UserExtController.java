package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.UserExtService;
import com.tth.modules.user.entity.UserExt;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 用户扩展信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
@Tag(name = "用户扩展信息表接口【userExt】", description = "权限前缀：【userExt】")
@RequestMapping("/userExt")
public class UserExtController extends BaseController<UserExt> {

    @Resource
    private UserExtService userExtService;

    @Override
    protected String getModuleName() {
        return "userExt";
    }

    @Override
    public BaseService<UserExt> getBaseService() {
        return userExtService;
    }
}
