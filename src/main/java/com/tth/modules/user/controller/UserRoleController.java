package com.tth.modules.user.controller;

import com.tth.modules.user.entity.UserBase;
import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.tth.modules.user.service.UserRoleService;
import com.tth.modules.user.entity.UserRole;
import org.springframework.web.bind.annotation.RestController;
import com.tth.framework.base.BaseController;
import com.tth.framework.base.BaseService;

/**
 * <p>
 * 用户角色关联表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@RestController
//@Tag(name = "用户角色关联表接口【userRole】", description = "权限前缀：【userRole】")
@RequestMapping("/userRole")
public class UserRoleController extends BaseController<UserRole> {

    @Resource
    private UserRoleService userRoleService;

    @Override
    protected String getModuleName() {
        return "userRole";
    }

    @Override
    public BaseService<UserRole> getBaseService() {
        return userRoleService;
    }
}
