package com.tth.modules.user.dto;

import com.tth.common.enums.GenderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 创建管理员用户DTO
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@Schema(name = "CreateAdminUserDTO", description = "创建管理员用户DTO")
public class CreateAdminUserDTO {

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "性别（0-未知，1-男，2-女）")
    private GenderEnum gender;

    @Schema(description = "部门ID")
    private String deptId;

    @Schema(description = "职位")
    private String jobTitle;
}
