package com.tth.modules.user.dto;

import com.tth.common.enums.VerifyStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;

/**
 * 审核实名认证DTO
 */
@Getter
@Setter
@Schema(name = "ReviewVerifyDTO", description = "审核实名认证请求")
public class ReviewVerifyDTO {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @NotNull(message = "认证状态不能为空")
    @Schema(description = "认证状态（2-已认证，3-认证失败）", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"VERIFIED", "FAILED"})
    private VerifyStatusEnum verifyStatus;

    @Schema(description = "认证失败原因（认证失败时必填）")
    private String verifyFailReason;
}
