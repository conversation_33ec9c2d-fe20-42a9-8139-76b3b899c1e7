package com.tth.modules.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 提交实名认证DTO
 */
@Getter
@Setter
@Schema(name = "SubmitVerifyDTO", description = "提交实名认证请求")
public class SubmitVerifyDTO {

    @NotBlank(message = "真实姓名不能为空")
    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String realName;

    @NotBlank(message = "身份证号码不能为空")
    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idCardNo;

    @NotNull(message = "身份证人像面图片ID不能为空")
    @Schema(description = "身份证人像面图片ID（关联tth_oss_file）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long idCardProfileFileId;

    @NotNull(message = "身份证国徽面图片ID不能为空")
    @Schema(description = "身份证国徽面图片ID（关联tth_oss_file）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long idCardNationalFileId;
}
