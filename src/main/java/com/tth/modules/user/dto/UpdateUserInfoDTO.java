package com.tth.modules.user.dto;

import com.tth.common.enums.GenderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 修改用户信息DTO
 */
@Getter
@Setter
@Schema(name = "UpdateUserInfoDTO", description = "修改用户信息请求")
public class UpdateUserInfoDTO {

    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Size(max = 100, message = "地区长度不能超过100个字符")
    @Schema(description = "地区")
    private String region;
}
