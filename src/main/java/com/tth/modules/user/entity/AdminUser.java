package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.GenderEnum;
import com.tth.framework.base.BaseEntity;

import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
//@Getter
//@Setter
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tth_admin_user")
@Schema(name = "AdminUser", description = "系统用户表")
public class AdminUser extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userBaseId;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "职位")
    private String jobTitle;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    private LocalDateTime loginTime;
}
