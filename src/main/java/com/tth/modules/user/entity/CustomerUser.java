package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.GenderEnum;
import com.tth.common.enums.PlatformSourceEnum;
import com.tth.common.enums.VerifyStatusEnum;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.base.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_customer_user")
@Schema(name = "CustomerUser", description = "客户用户表")
public class CustomerUser extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userBaseId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "性别（0-未知，1-男，2-女）")
    private GenderEnum gender;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "地区")
    private String region;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "用户等级")
    private Integer level;

    @Schema(description = "积分")
    private Integer points;

    @Schema(description = "注册来源")
    private PlatformSourceEnum registerSource;

    @Schema(description = "注册IP")
    private String registerIp;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号码")
    private String idCardNo;

    @MarkFileUrl
    @Schema(description = "身份证人像面图片ID（关联tth_oss_file）")
    private Long idCardProfileFileId;

    @MarkFileUrl
    @Schema(description = "身份证国徽面图片ID（关联tth_oss_file）")
    private Long idCardNationalFileId;

    @Schema(description = "实名认证状态（0-未认证，1-认证中，2-已认证，3-认证失败）")
    private VerifyStatusEnum verifyStatus;

    @Schema(description = "认证时间")
    private LocalDateTime verifyTime;

    @Schema(description = "认证失败原因")
    private String verifyFailReason;
}
