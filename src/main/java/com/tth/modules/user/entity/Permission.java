package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.user.enums.field.PermissionTypeEnum;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_permission")
@Schema(name = "Permission", description = "权限表")
public class Permission extends BaseEntity {

    @Schema(description = "父权限ID")
    private Long parentId;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "权限编码")
    private String permissionCode;

    @Schema(description = "权限类型")
    private PermissionTypeEnum permissionType;

    @Schema(description = "用户类型")
    private UserTypeEnum userType;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "组件")
    private String component;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态（0-禁用，1-启用）")
    private EnabledStatusEnum status;
}
