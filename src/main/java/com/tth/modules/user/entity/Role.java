package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_role")
@Schema(name = "Role", description = "角色表")
public class Role extends BaseEntity {

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "用户类型")
    private UserTypeEnum userType;

    @Schema(description = "状态（0-禁用，1-启用）")
    private EnabledStatusEnum status;

    @Schema(description = "排序")
    private Integer sort;
}
