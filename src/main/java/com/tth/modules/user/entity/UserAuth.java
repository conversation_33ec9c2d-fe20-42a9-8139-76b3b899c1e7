package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.user.enums.field.IdentityTypeEnum;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户认证表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_user_auth")
@Schema(name = "UserAuth", description = "用户认证表")
public class UserAuth extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userBaseId;

    @Schema(description = "用户类型")
    private UserTypeEnum userType;

    @Schema(description = "认证类型")
    private IdentityTypeEnum identityType;

    @Schema(description = "认证标识（用户名/手机号/邮箱/微信openid等）")
    private String identifier;

    @Schema(description = "凭证（密码/token等）")
    private String credential;

    @Schema(description = "是否已验证（0-否，1-是）")
    private YesNoEnum verified;
}
