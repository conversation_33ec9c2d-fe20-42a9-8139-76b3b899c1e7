package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.user.enums.field.UserTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 用户基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_user_base")
@Schema(name = "UserBase", description = "用户基础表")
public class UserBase extends BaseEntity {

    @Schema(description = "用户类型（1-管理员，2-客户，3-商家，4-合作伙伴等）")
    private UserTypeEnum userType;

    @Schema(description = "状态（0-禁用，1-启用）")
    private EnabledStatusEnum status;

    @TableField(exist = false)
    private List<UserExt> userExtList;
}
