package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.base.BaseEntity;
import com.tth.modules.user.enums.field.DeviceTypeEnum;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_user_device")
@Schema(name = "UserDevice", description = "用户设备表")
public class UserDevice extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userId;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备类型")
    private DeviceTypeEnum deviceType;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备型号")
    private String deviceModel;

    @Schema(description = "操作系统版本")
    private String osVersion;

    @Schema(description = "应用版本")
    private String appVersion;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "登录令牌")
    private String token;

    @Schema(description = "令牌过期时间")
    private LocalDateTime tokenExpireTime;
}
