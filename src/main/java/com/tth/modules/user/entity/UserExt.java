package com.tth.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户扩展信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_user_ext")
@Schema(name = "UserExt", description = "用户扩展信息表")
public class UserExt extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userId;

    @Schema(description = "属性键")
    private String attrKey;

    @Schema(description = "属性值")
    private String attrValue;
}
