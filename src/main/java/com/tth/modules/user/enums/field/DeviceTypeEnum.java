package com.tth.modules.user.enums.field;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型枚举
 * 用于标识用户使用的不同设备类型
 */
@MarkDictType("设备类型")
@Schema(description = "设备类型")
@Getter
@AllArgsConstructor
public enum DeviceTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "PC")
    PC(1, "PC"),

    @Schema(description = "Android")
    ANDROID(2, "Android"),

    @Schema(description = "iOS")
    IOS(3, "iOS"),

    @Schema(description = "Web")
    WEB(4, "Web"),

    @Schema(description = "iPad")
    IPAD(5, "iPad"),

    @Schema(description = "Android平板")
    ANDROID_TABLET(6, "Android平板"),

    @Schema(description = "Windows Phone")
    WINDOWS_PHONE(7, "Windows Phone"),

    @Schema(description = "智能电视")
    SMART_TV(8, "智能电视"),

    @Schema(description = "智能手表")
    SMART_WATCH(9, "智能手表"),

    @Schema(description = "车载设备")
    CAR(10, "车载设备"),

    @Schema(description = "其他")
    OTHER(99, "其他");

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    private final String desc;

    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值查找对应的枚举
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static DeviceTypeEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (DeviceTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述查找对应的枚举
     * @param desc 枚举描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static DeviceTypeEnum fromDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (DeviceTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为移动设备
     * @return 如果是移动设备返回true，否则返回false
     */
    public boolean isMobile() {
        return this == ANDROID || this == IOS || this == WINDOWS_PHONE || 
               this == SMART_WATCH;
    }

    /**
     * 判断是否为平板设备
     * @return 如果是平板设备返回true，否则返回false
     */
    public boolean isTablet() {
        return this == IPAD || this == ANDROID_TABLET;
    }

    /**
     * 判断是否为桌面设备
     * @return 如果是桌面设备返回true，否则返回false
     */
    public boolean isDesktop() {
        return this == PC;
    }

    /**
     * 判断是否为Web设备
     * @return 如果是Web设备返回true，否则返回false
     */
    public boolean isWeb() {
        return this == WEB;
    }

    /**
     * 判断是否为iOS系列设备
     * @return 如果是iOS系列设备返回true，否则返回false
     */
    public boolean isIosFamily() {
        return this == IOS || this == IPAD;
    }

    /**
     * 判断是否为Android系列设备
     * @return 如果是Android系列设备返回true，否则返回false
     */
    public boolean isAndroidFamily() {
        return this == ANDROID || this == ANDROID_TABLET;
    }

    /**
     * 判断是否为智能家居设备
     * @return 如果是智能家居设备返回true，否则返回false
     */
    public boolean isSmartHome() {
        return this == SMART_TV;
    }

    /**
     * 判断是否为可穿戴设备
     * @return 如果是可穿戴设备返回true，否则返回false
     */
    public boolean isWearable() {
        return this == SMART_WATCH;
    }
}
