package com.tth.modules.user.enums.field;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 认证类型枚举
 * 用于标识用户的不同认证方式
 */
@MarkDictType("认证类型")
@Schema(description = "认证类型")
@Getter
@AllArgsConstructor
public enum IdentityTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "用户名")
    USERNAME(1, "用户名"),

    @Schema(description = "手机号")
    PHONE(2, "手机号"),

    @Schema(description = "邮箱")
    EMAIL(3, "邮箱"),

    @Schema(description = "微信")
    WECHAT(4, "微信"),

    @Schema(description = "微信小程序")
    WECHAT_MINI_PROGRAM(5, "微信小程序"),

    @Schema(description = "微信小程序授权手机号")
    WECHAT_MINI_PROGRAM_PHONE(6, "微信小程序授权手机号"),

    @Schema(description = "支付宝")
    ALIPAY(7, "支付宝"),

    @Schema(description = "QQ")
    QQ(8, "QQ"),

    @Schema(description = "微博")
    WEIBO(9, "微博"),

    @Schema(description = "Apple ID")
    APPLE(10, "Apple ID"),

    @Schema(description = "Google")
    GOOGLE(11, "Google"),

    @Schema(description = "Facebook")
    FACEBOOK(12, "Facebook"),

    @Schema(description = "Twitter")
    TWITTER(13, "Twitter"),

    @Schema(description = "GitHub")
    GITHUB(14, "GitHub"),

    @Schema(description = "其他")
    OTHER(99, "其他");

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    private final String desc;

    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值查找对应的枚举
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static IdentityTypeEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (IdentityTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述查找对应的枚举
     * @param desc 枚举描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static IdentityTypeEnum fromDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (IdentityTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为用户名认证
     * @return 如果是用户名认证返回true，否则返回false
     */
    public boolean isUsername() {
        return this == USERNAME;
    }

    /**
     * 判断是否为手机号认证
     * @return 如果是手机号认证返回true，否则返回false
     */
    public boolean isPhone() {
        return this == PHONE;
    }

    /**
     * 判断是否为邮箱认证
     * @return 如果是邮箱认证返回true，否则返回false
     */
    public boolean isEmail() {
        return this == EMAIL;
    }

    /**
     * 判断是否为第三方认证
     * @return 如果是第三方认证返回true，否则返回false
     */
    public boolean isThirdParty() {
        return this == WECHAT || this == WECHAT_MINI_PROGRAM || this == WECHAT_MINI_PROGRAM_PHONE || this == ALIPAY || this == QQ ||
               this == WEIBO || this == APPLE || this == GOOGLE ||
               this == FACEBOOK || this == TWITTER || this == GITHUB;
    }

    /**
     * 判断是否为国内第三方认证
     * @return 如果是国内第三方认证返回true，否则返回false
     */
    public boolean isDomesticThirdParty() {
        return this == WECHAT || this == WECHAT_MINI_PROGRAM || this == WECHAT_MINI_PROGRAM_PHONE || this == ALIPAY || this == QQ || this == WEIBO;
    }

    /**
     * 判断是否为国外第三方认证
     * @return 如果是国外第三方认证返回true，否则返回false
     */
    public boolean isForeignThirdParty() {
        return this == APPLE || this == GOOGLE || this == FACEBOOK || 
               this == TWITTER || this == GITHUB;
    }
}
