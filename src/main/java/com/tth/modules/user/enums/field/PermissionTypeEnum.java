package com.tth.modules.user.enums.field;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权限类型枚举
 * 用于标识不同类型的权限
 */
@MarkDictType("权限类型")
@Schema(description = "权限类型")
@Getter
@AllArgsConstructor
public enum PermissionTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "菜单")
    MENU(1, "菜单"),

    @Schema(description = "按钮")
    BUTTON(2, "按钮"),

    @Schema(description = "接口")
    API(3, "接口"),

    @Schema(description = "数据")
    DATA(4, "数据"),

    @Schema(description = "其他")
    OTHER(99, "其他");

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    private final String desc;

    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值查找对应的枚举
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static PermissionTypeEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PermissionTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述查找对应的枚举
     * @param desc 枚举描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static PermissionTypeEnum fromDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (PermissionTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为菜单类型
     * @return 如果是菜单类型返回true，否则返回false
     */
    public boolean isMenu() {
        return this == MENU;
    }

    /**
     * 判断是否为按钮类型
     * @return 如果是按钮类型返回true，否则返回false
     */
    public boolean isButton() {
        return this == BUTTON;
    }

    /**
     * 判断是否为接口类型
     * @return 如果是接口类型返回true，否则返回false
     */
    public boolean isApi() {
        return this == API;
    }

    /**
     * 判断是否为数据类型
     * @return 如果是数据类型返回true，否则返回false
     */
    public boolean isData() {
        return this == DATA;
    }
}
