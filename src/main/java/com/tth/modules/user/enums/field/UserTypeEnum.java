package com.tth.modules.user.enums.field;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举
 */
@MarkDictType("用户类型")
@Schema(description = "用户类型")
@Getter
@AllArgsConstructor
public enum UserTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "管理员")
    ADMIN(1, "管理员"),

    @Schema(description = "客户")
    CUSTOMER(2, "客户"),

    @Schema(description = "商家")
    MERCHANT(3, "商家"),

    @Schema(description = "合作伙伴")
    PARTNER(4, "合作伙伴");

    private final int value;

    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }
}
