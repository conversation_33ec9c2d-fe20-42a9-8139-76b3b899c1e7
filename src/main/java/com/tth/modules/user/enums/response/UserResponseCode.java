package com.tth.modules.user.enums.response;

import com.tth.framework.response.IResponseCode;
import com.tth.framework.response.ModuleResponseCode;
import lombok.Getter;

@Getter
@ModuleResponseCode(module = 700, desc = "用户模块")
public enum UserResponseCode implements IResponseCode {
    NOT_FOUND(1, "用户不存在"),
    DUPLICATE_USERNAME(2, "用户名已存在"),
    INVALID_PASSWORD(3, "密码错误"),
    ACCOUNT_LOCKED(4, "账号已被锁定"),
    ;

    private final int code;
    private final String message;

    UserResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
}