package com.tth.modules.user.model.vo;

import com.tth.common.enums.EnabledStatusEnum;
import com.tth.common.enums.GenderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 管理员用户VO
 */
@Data
@Schema(description = "管理员用户信息")
public class AdminUserVO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "职位")
    private String jobTitle;

    @Schema(description = "状态")
    private EnabledStatusEnum status;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    private Date loginTime;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "备注")
    private String remark;
}
