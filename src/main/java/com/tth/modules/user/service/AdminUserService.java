package com.tth.modules.user.service;

import com.tth.modules.user.entity.AdminUser;
import com.tth.framework.base.BaseService;
import com.tth.modules.user.model.vo.AdminUserVO;
import com.tth.modules.user.model.dto.CreateAdminUserDTO;

/**
 * <p>
 * 系统用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface AdminUserService extends BaseService<AdminUser> {

    /**
     * 创建管理员用户
     *
     * @param createAdminUserDTO 创建管理员用户参数
     * @return 管理员用户信息
     */
    AdminUserVO createAdminUser(CreateAdminUserDTO createAdminUserDTO);
}
