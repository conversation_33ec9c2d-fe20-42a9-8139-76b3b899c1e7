package com.tth.modules.user.service;

import com.tth.modules.user.dto.ReviewVerifyDTO;
import com.tth.modules.user.dto.SubmitVerifyDTO;
import com.tth.modules.user.dto.UpdateUserInfoDTO;
import com.tth.modules.user.entity.CustomerUser;
import com.tth.framework.base.BaseService;
import com.tth.modules.user.vo.UserVerifyStatusVO;

/**
 * <p>
 * 客户用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface CustomerUserService extends BaseService<CustomerUser> {

    /**
     * 获取用户实名认证状态
     * @param userBaseId 用户基础ID
     * @return 用户实名认证状态VO
     */
    UserVerifyStatusVO getUserVerifyStatus(Long userBaseId);

    /**
     * 提交实名认证
     * @param userBaseId 用户基础ID
     * @param dto 提交实名认证DTO
     */
    void submitVerify(Long userBaseId, SubmitVerifyDTO dto);

    /**
     * 审核实名认证
     * @param dto 审核实名认证DTO
     */
    void reviewVerify(ReviewVerifyDTO dto);

    /**
     * 更新用户信息
     * @param userBaseId 用户基础ID
     * @param dto 更新用户信息DTO
     */
    void updateUserInfo(Long userBaseId, UpdateUserInfoDTO dto);
}
