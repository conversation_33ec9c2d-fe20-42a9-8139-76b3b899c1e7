package com.tth.modules.user.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import com.tth.common.enums.EnabledStatusEnum;
import com.tth.common.enums.YesNoEnum;
import com.tth.framework.exception.core.BizException;
import com.tth.modules.user.entity.AdminUser;
import com.tth.modules.user.entity.UserAuth;
import com.tth.modules.user.entity.UserBase;
import com.tth.modules.user.enums.field.IdentityTypeEnum;
import com.tth.modules.user.enums.response.UserResponseCode;
import com.tth.modules.user.enums.field.UserTypeEnum;
import com.tth.modules.user.mapper.AdminUserMapper;
import com.tth.modules.user.model.vo.AdminUserVO;
import com.tth.modules.user.model.dto.CreateAdminUserDTO;
import com.tth.modules.user.service.AdminUserService;
import com.tth.modules.user.service.UserAuthService;
import com.tth.modules.user.service.UserBaseService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 系统用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class AdminUserServiceImpl extends BaseServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private UserBaseService userBaseService;

    @Resource
    private UserAuthService userAuthService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AdminUserVO createAdminUser(CreateAdminUserDTO createAdminUserDTO) {
        // 1. 检查用户名是否已存在
        boolean exists = userAuthService.lambdaQuery()
                .eq(UserAuth::getIdentifier, createAdminUserDTO.getUsername())
                .eq(UserAuth::getIdentityType, IdentityTypeEnum.USERNAME)
                .eq(UserAuth::getUserType, UserTypeEnum.ADMIN)
                .exists();

        if (exists) {
            throw new BizException(UserResponseCode.DUPLICATE_USERNAME);
        }

        // 2. 创建 UserBase 记录
        UserBase userBase = new UserBase();
        userBase.setUserType(UserTypeEnum.ADMIN);
        userBase.setStatus(EnabledStatusEnum.ENABLED); // 默认启用
        userBase.setRemark(createAdminUserDTO.getRemark());
        userBaseService.save(userBase);

        // 3. 创建 UserAuth 记录
        UserAuth userAuth = new UserAuth();
        userAuth.setUserBaseId(userBase.getId());
        userAuth.setUserType(UserTypeEnum.ADMIN);
        userAuth.setIdentityType(IdentityTypeEnum.USERNAME);
        userAuth.setIdentifier(createAdminUserDTO.getUsername());
        userAuth.setCredential(SaSecureUtil.md5(createAdminUserDTO.getPassword())); // 密码加密
        userAuth.setVerified(YesNoEnum.YES); // 默认已验证
        userAuthService.save(userAuth);

        // 4. 创建 AdminUser 记录
        AdminUser adminUser = new AdminUser();
        adminUser.setUserBaseId(userBase.getId());
        adminUser.setRealName(createAdminUserDTO.getRealName());
        adminUser.setAvatar(createAdminUserDTO.getAvatar());
        adminUser.setEmail(createAdminUserDTO.getEmail());
        adminUser.setPhone(createAdminUserDTO.getPhone());
        adminUser.setGender(createAdminUserDTO.getGender());
        adminUser.setDeptId(createAdminUserDTO.getDeptId());
        adminUser.setJobTitle(createAdminUserDTO.getJobTitle());
        adminUser.setRemark(createAdminUserDTO.getRemark());
        this.save(adminUser);

        // 5. 构建返回结果
        AdminUserVO adminUserVO = new AdminUserVO();
        BeanUtils.copyProperties(adminUser, adminUserVO);
        adminUserVO.setId(userBase.getId());
        adminUserVO.setUsername(createAdminUserDTO.getUsername());
        adminUserVO.setStatus(userBase.getStatus());
        adminUserVO.setCreatedTime(userBase.getCreatedTime());
        adminUserVO.setRemark(userBase.getRemark());

        return adminUserVO;
    }
}
