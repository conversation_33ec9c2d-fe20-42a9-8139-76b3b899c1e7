package com.tth.modules.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tth.common.enums.VerifyStatusEnum;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.modules.user.dto.ReviewVerifyDTO;
import com.tth.modules.user.dto.SubmitVerifyDTO;
import com.tth.modules.user.dto.UpdateUserInfoDTO;
import com.tth.modules.user.entity.CustomerUser;
import com.tth.modules.user.mapper.CustomerUserMapper;
import com.tth.modules.user.service.CustomerUserService;
import com.tth.framework.base.BaseServiceImpl;
import com.tth.modules.user.vo.UserVerifyStatusVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 客户用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class CustomerUserServiceImpl extends BaseServiceImpl<CustomerUserMapper, CustomerUser> implements CustomerUserService {

    @Resource
    private CustomerUserMapper customerUserMapper;

    @Override
    public UserVerifyStatusVO getUserVerifyStatus(Long userBaseId) {
        CustomerUser customerUser = lambdaQuery()
                .eq(CustomerUser::getUserBaseId, userBaseId)
                .one();

        if (customerUser == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "用户不存在");
        }

        UserVerifyStatusVO vo = new UserVerifyStatusVO();
        vo.setIdCardProfileFileId(customerUser.getIdCardProfileFileId());
        vo.setIdCardNationalFileId(customerUser.getIdCardNationalFileId());
        vo.setVerifyStatus(customerUser.getVerifyStatus());
        vo.setRealName(customerUser.getRealName());
        vo.setIdCardNo(customerUser.getIdCardNo());

        return vo;
    }

    @Override
    public void submitVerify(Long userBaseId, SubmitVerifyDTO dto) {
        CustomerUser customerUser = lambdaQuery()
                .eq(CustomerUser::getUserBaseId, userBaseId)
                .one();

        if (customerUser == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "用户不存在");
        }

        // 检查当前认证状态，只有未认证或认证失败才可以提交
        VerifyStatusEnum currentStatus = customerUser.getVerifyStatus();
        if (currentStatus != null &&
            currentStatus != VerifyStatusEnum.UNVERIFIED &&
            currentStatus != VerifyStatusEnum.FAILED) {
            throw new BizException(BaseResponseCode.FAIL, "当前状态不允许提交认证，只有未认证或认证失败状态才可以提交");
        }

        // 使用update(entity, wrapper)方法，完美支持自动填充、乐观锁和null值更新
        LambdaUpdateWrapper<CustomerUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerUser::getId, customerUser.getId())
                .eq(CustomerUser::getVersion, customerUser.getVersion()) // 乐观锁条件
                .set(CustomerUser::getRealName, dto.getRealName())
                .set(CustomerUser::getIdCardNo, dto.getIdCardNo())
                .set(CustomerUser::getIdCardProfileFileId, dto.getIdCardProfileFileId())
                .set(CustomerUser::getIdCardNationalFileId, dto.getIdCardNationalFileId())
                .set(CustomerUser::getVerifyStatus, VerifyStatusEnum.VERIFYING)
                .set(CustomerUser::getVerifyTime, LocalDateTime.now())
                .set(CustomerUser::getVerifyFailReason, null); // 支持设置为null

        boolean updated = update(new CustomerUser(), updateWrapper);
        if (!updated) {
            throw new BizException(BaseResponseCode.FAIL, "提交失败，请重试");
        }
    }

    @Override
    public void reviewVerify(ReviewVerifyDTO dto) {
        // 验证认证状态只能是已认证或认证失败
        if (dto.getVerifyStatus() != VerifyStatusEnum.VERIFIED &&
            dto.getVerifyStatus() != VerifyStatusEnum.FAILED) {
            throw new BizException(BaseResponseCode.FAIL, "认证状态只能设置为已认证或认证失败");
        }

        // 如果是认证失败，必须提供失败原因
        if (dto.getVerifyStatus() == VerifyStatusEnum.FAILED &&
            (dto.getVerifyFailReason() == null || dto.getVerifyFailReason().trim().isEmpty())) {
            throw new BizException(BaseResponseCode.FAIL, "认证失败时必须提供失败原因");
        }

        // 根据userBaseId查询CustomerUser
        CustomerUser customerUser = lambdaQuery()
                .eq(CustomerUser::getUserBaseId, dto.getUserId())
                .one();

        if (customerUser == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "用户不存在");
        }

        // 使用update(entity, wrapper)方法，完美支持自动填充、乐观锁和null值更新
        LambdaUpdateWrapper<CustomerUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerUser::getId, customerUser.getId())
                .eq(CustomerUser::getVersion, customerUser.getVersion()) // 乐观锁条件
                .set(CustomerUser::getVerifyStatus, dto.getVerifyStatus())
                .set(CustomerUser::getVerifyTime, LocalDateTime.now());

        if (dto.getVerifyStatus() == VerifyStatusEnum.FAILED) {
            updateWrapper.set(CustomerUser::getVerifyFailReason, dto.getVerifyFailReason());
        } else {
            updateWrapper.set(CustomerUser::getVerifyFailReason, null); // 支持设置为null
        }

        boolean updated = update(new CustomerUser(), updateWrapper);
        if (!updated) {
            throw new BizException(BaseResponseCode.FAIL, "审核失败，请重试");
        }
    }

    @Override
    public void updateUserInfo(Long userBaseId, UpdateUserInfoDTO dto) {
        CustomerUser customerUser = lambdaQuery()
                .eq(CustomerUser::getUserBaseId, userBaseId)
                .one();

        if (customerUser == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "用户不存在");
        }

        // 使用update(entity, wrapper)方法，完美支持自动填充、乐观锁
        LambdaUpdateWrapper<CustomerUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerUser::getId, customerUser.getId())
                .eq(CustomerUser::getVersion, customerUser.getVersion()); // 乐观锁条件

        // 只更新非空字段
        if (dto.getNickname() != null) {
            updateWrapper.set(CustomerUser::getNickname, dto.getNickname());
        }
        if (dto.getAvatar() != null) {
            updateWrapper.set(CustomerUser::getAvatar, dto.getAvatar());
        }
        if (dto.getGender() != null) {
            updateWrapper.set(CustomerUser::getGender, dto.getGender());
        }
        if (dto.getBirthday() != null) {
            updateWrapper.set(CustomerUser::getBirthday, dto.getBirthday());
        }
        if (dto.getRegion() != null) {
            updateWrapper.set(CustomerUser::getRegion, dto.getRegion());
        }

        boolean updated = update(new CustomerUser(), updateWrapper);
        if (!updated) {
            throw new BizException(BaseResponseCode.FAIL, "更新用户信息失败，请重试");
        }
    }
}
