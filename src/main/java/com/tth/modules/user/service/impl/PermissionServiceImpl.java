package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.Permission;
import com.tth.modules.user.mapper.PermissionMapper;
import com.tth.modules.user.service.PermissionService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class PermissionServiceImpl extends BaseServiceImpl<PermissionMapper, Permission> implements PermissionService {

    @Resource
    private PermissionMapper permissionMapper;
}
