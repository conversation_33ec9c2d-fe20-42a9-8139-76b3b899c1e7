package com.tth.modules.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.tth.modules.user.entity.RolePermission;
import com.tth.modules.user.mapper.RolePermissionMapper;
import com.tth.modules.user.service.RolePermissionService;
import com.tth.modules.user.service.UserCacheService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色权限关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Slf4j
@Service
public class RolePermissionServiceImpl extends BaseServiceImpl<RolePermissionMapper, RolePermission> implements RolePermissionService {

    @Resource
    private RolePermissionMapper rolePermissionMapper;

    @Resource
    private UserCacheService userCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(RolePermission entity) {
        boolean result = super.save(entity);
        if (result) {
            // 清除角色相关的所有用户权限缓存
            userCacheService.clearRoleAuthCache(entity.getRoleId());
            log.debug("角色权限关系保存成功，已触发角色[{}]相关的所有用户权限缓存清除", entity.getRoleId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(java.util.Collection<RolePermission> entityList) {
        boolean result = super.saveBatch(entityList);
        if (result && !entityList.isEmpty()) {
            // 提取所有角色ID
            List<Long> roleIds = entityList.stream()
                    .map(RolePermission::getRoleId)
                    .distinct()
                    .collect(Collectors.toList());

            // 清除所有相关角色的用户权限缓存
            roleIds.forEach(roleId -> userCacheService.clearRoleAuthCache(roleId));
            log.debug("批量保存角色权限关系成功，已触发相关角色的所有用户权限缓存清除");
        }
        return result;
    }

//    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        // 先获取记录，以便后续清除缓存
        RolePermission rolePermission = getById(id);
        boolean result = super.removeById(id);
        if (result && rolePermission != null) {
            // 清除角色相关的所有用户权限缓存
            userCacheService.clearRoleAuthCache(rolePermission.getRoleId());
            log.debug("角色权限关系删除成功，已触发角色[{}]相关的所有用户权限缓存清除", rolePermission.getRoleId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Wrapper<RolePermission> wrapper) {
        // 先查询出所有要删除的记录，以便后续清除缓存
        List<RolePermission> rolePermissions = list(wrapper);
        boolean result = super.remove(wrapper);
        if (result && !rolePermissions.isEmpty()) {
            // 提取所有角色ID
            List<Long> roleIds = rolePermissions.stream()
                    .map(RolePermission::getRoleId)
                    .distinct()
                    .collect(Collectors.toList());

            // 清除所有相关角色的用户权限缓存
            roleIds.forEach(roleId -> userCacheService.clearRoleAuthCache(roleId));
            log.debug("批量删除角色权限关系成功，已触发相关角色的所有用户权限缓存清除");
        }
        return result;
    }
}
