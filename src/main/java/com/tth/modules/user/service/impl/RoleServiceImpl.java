package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.Role;
import com.tth.modules.user.mapper.RoleMapper;
import com.tth.modules.user.service.RoleService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class RoleServiceImpl extends BaseServiceImpl<RoleMapper, Role> implements RoleService {

    @Resource
    private RoleMapper roleMapper;
}
