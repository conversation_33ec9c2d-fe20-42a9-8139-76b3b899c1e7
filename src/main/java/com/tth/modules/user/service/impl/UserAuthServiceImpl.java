package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.UserAuth;
import com.tth.modules.user.mapper.UserAuthMapper;
import com.tth.modules.user.service.UserAuthService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户认证表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class UserAuthServiceImpl extends BaseServiceImpl<UserAuthMapper, UserAuth> implements UserAuthService {

    @Resource
    private UserAuthMapper userAuthMapper;
}
