package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.UserBase;
import com.tth.modules.user.mapper.UserBaseMapper;
import com.tth.modules.user.service.UserBaseService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class UserBaseServiceImpl extends BaseServiceImpl<UserBaseMapper, UserBase> implements UserBaseService {

    @Resource
    private UserBaseMapper userBaseMapper;
}
