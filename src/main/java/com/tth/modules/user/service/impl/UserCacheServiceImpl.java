package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.UserRole;
import com.tth.modules.user.service.UserCacheService;
import com.tth.modules.user.service.UserRoleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户缓存服务实现类
 * 用于管理用户相关的缓存
 */
@Slf4j
@Service
public class UserCacheServiceImpl implements UserCacheService {

//    @Resource
//    private StpInterfaceImpl stpInterface;

    @Resource
    private UserRoleService userRoleService;

    /**
     * 清除指定用户的权限缓存
     *
     * @param userId 用户ID
     */
    @Override
    public void clearUserAuthCache(Long userId) {
//        stpInterface.clearUserCache(userId);
        log.info("已清除用户[{}]的权限缓存", userId);
    }

    /**
     * 清除指定角色关联的所有用户的权限缓存
     * 当角色权限变更时调用此方法
     *
     * @param roleId 角色ID
     */
    @Override
    @Async
    public void clearRoleAuthCache(Long roleId) {
        log.info("开始清除角色[{}]关联的所有用户权限缓存", roleId);

        // 查询拥有该角色的所有用户
        List<UserRole> userRoles = userRoleService.lambdaQuery()
                .eq(UserRole::getRoleId, roleId)
                .list();

        if (userRoles.isEmpty()) {
            log.info("角色[{}]没有关联的用户，无需清除缓存", roleId);
            return;
        }

        // 提取用户ID列表
        List<Long> userIds = userRoles.stream()
                .map(UserRole::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 清除每个用户的权限缓存
        int count = 0;
        for (Long userId : userIds) {
//            stpInterface.clearUserCache(userId);
            count++;
        }

        log.info("已成功清除角色[{}]关联的{}个用户的权限缓存", roleId, count);
    }
}
