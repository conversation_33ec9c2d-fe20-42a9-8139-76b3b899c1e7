package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.UserDevice;
import com.tth.modules.user.mapper.UserDeviceMapper;
import com.tth.modules.user.service.UserDeviceService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class UserDeviceServiceImpl extends BaseServiceImpl<UserDeviceMapper, UserDevice> implements UserDeviceService {

    @Resource
    private UserDeviceMapper userDeviceMapper;
}
