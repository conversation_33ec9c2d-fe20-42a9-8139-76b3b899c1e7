package com.tth.modules.user.service.impl;

import com.tth.modules.user.entity.UserExt;
import com.tth.modules.user.mapper.UserExtMapper;
import com.tth.modules.user.service.UserExtService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户扩展信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
public class UserExtServiceImpl extends BaseServiceImpl<UserExtMapper, UserExt> implements UserExtService {

    @Resource
    private UserExtMapper userExtMapper;
}
