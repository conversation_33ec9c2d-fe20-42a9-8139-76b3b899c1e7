package com.tth.modules.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.tth.modules.user.entity.UserRole;
import com.tth.modules.user.mapper.UserRoleMapper;
import com.tth.modules.user.service.UserRoleService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户角色关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Slf4j
@Service
public class UserRoleServiceImpl extends BaseServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    @Resource
    private UserRoleMapper userRoleMapper;

//    @Resource
//    private UserCacheService userCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(UserRole entity) {
        boolean result = super.save(entity);
        if (result) {
            // 清除用户权限缓存
//            userCacheService.clearUserAuthCache(entity.getUserId());
//            log.debug("用户角色关系保存成功，已清除用户[{}]的权限缓存", entity.getUserId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(java.util.Collection<UserRole> entityList) {
        boolean result = super.saveBatch(entityList);
        if (result) {
            // 清除所有相关用户的权限缓存
//            entityList.forEach(entity ->
//                userCacheService.clearUserAuthCache(entity.getUserId()));
//            log.debug("批量保存用户角色关系成功，已清除相关用户的权限缓存");
        }
        return result;
    }

//    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        // 先获取记录，以便后续清除缓存
        UserRole userRole = getById(id);
        boolean result = super.removeById(id);
        if (result && userRole != null) {
            // 清除用户权限缓存
//            userCacheService.clearUserAuthCache(userRole.getUserId());
//            log.debug("用户角色关系删除成功，已清除用户[{}]的权限缓存", userRole.getUserId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Wrapper<UserRole> wrapper) {
        // 先查询出所有要删除的记录，以便后续清除缓存
        List<UserRole> userRoles = list(wrapper);
        boolean result = super.remove(wrapper);
        if (result && !userRoles.isEmpty()) {
            // 清除所有相关用户的权限缓存
//            userRoles.forEach(userRole ->
//                userCacheService.clearUserAuthCache(userRole.getUserId()));
//            log.debug("批量删除用户角色关系成功，已清除相关用户的权限缓存");
        }
        return result;
    }
}
