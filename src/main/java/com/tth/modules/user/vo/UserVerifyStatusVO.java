package com.tth.modules.user.vo;

import com.tth.common.enums.VerifyStatusEnum;
import com.tth.framework.annotation.MarkFileUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户实名认证状态VO
 */
@Getter
@Setter
@Schema(name = "UserVerifyStatusVO", description = "用户实名认证状态")
public class UserVerifyStatusVO {

    @MarkFileUrl
    @Schema(description = "身份证人像面图片ID（关联tth_oss_file）")
    private Long idCardProfileFileId;

    @MarkFileUrl
    @Schema(description = "身份证国徽面图片ID（关联tth_oss_file）")
    private Long idCardNationalFileId;

    @Schema(description = "实名认证状态（0-未认证，1-认证中，2-已认证，3-认证失败）")
    private VerifyStatusEnum verifyStatus;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号码")
    private String idCardNo;
}
