//package com.tth.modules.wardrobe.controller;
//
//import org.springframework.web.bind.annotation.RequestMapping;
//import jakarta.annotation.Resource;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import com.tth.modules.wardrobe.service.WardrobeAreaService;
//import com.tth.modules.wardrobe.entity.WardrobeArea;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.RestController;
//import com.tth.framework.base.BaseController;
//import com.tth.framework.base.BaseService;
//
///**
// * <p>
// * 衣柜区域表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2025-05-24
// */
//@Slf4j
//@RestController
//@Tag(name = "衣柜区域表接口【wardrobeArea】", description = "权限前缀：【wardrobeArea】")
//@RequestMapping("/wardrobeArea")
//public class WardrobeAreaController extends BaseController<WardrobeArea> {
//
//    @Resource
//    private WardrobeAreaService wardrobeAreaService;
//
//    @Override
//    protected String getModuleName() {
//        return "wardrobeArea";
//    }
//
//    @Override
//    public BaseService<WardrobeArea> getBaseService() {
//    return wardrobeAreaService;
//    }
//}
