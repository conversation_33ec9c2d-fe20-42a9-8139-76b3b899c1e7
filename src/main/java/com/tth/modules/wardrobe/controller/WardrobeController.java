//package com.tth.modules.wardrobe.controller;
//
//import cn.dev33.satoken.annotation.SaIgnore;
//import com.tth.framework.annotation.ApiLog;
//import org.springframework.web.bind.annotation.RequestMapping;
//import jakarta.annotation.Resource;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import com.tth.modules.wardrobe.service.WardrobeService;
//import com.tth.modules.wardrobe.entity.Wardrobe;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.RestController;
//import com.tth.framework.base.BaseController;
//import com.tth.framework.base.BaseService;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import jakarta.validation.Valid;
//import io.swagger.v3.oas.annotations.Operation;
//import com.tth.framework.response.R;
//import com.tth.framework.utils.AuthUtil;
//import com.tth.modules.wardrobe.model.dto.AddWardrobeDTO;
//import com.tth.modules.wardrobe.model.vo.WardrobeDetailVO;
//import com.tth.modules.wardrobe.model.vo.WardrobeListVO;
//import com.tth.framework.paging.PageResult;
//import com.tth.framework.paging.Paging;
//import com.tth.framework.paging.Condition;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//
//import java.util.ArrayList;
//
///**
// * <p>
// * 衣柜表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2025-05-24
// */
//@Slf4j
//@RestController
//@Tag(name = "衣柜表接口【wardrobe】", description = "权限前缀：【wardrobe】")
//@RequestMapping("/wardrobe")
//public class WardrobeController extends BaseController<Wardrobe> {
//
//    @Resource
//    private WardrobeService wardrobeService;
//
//    @Override
//    protected String getModuleName() {
//        return "wardrobe";
//    }
//
//    @Override
//    public BaseService<Wardrobe> getBaseService() {
//        return wardrobeService;
//    }
//
//    @SaIgnore
//    @ApiLog(module = "衣柜表", value = "添加衣柜")
//    @PostMapping("/add")
//    @Operation(summary = "添加衣柜", description = "APP用户添加衣柜，同时创建衣柜区域")
//    public R<Wardrobe> add(@RequestBody @Valid AddWardrobeDTO addWardrobeDTO) throws Exception {
//        log.info("用户添加衣柜，参数：{}", addWardrobeDTO);
//
//        // 调试：打印区域信息
//        if (addWardrobeDTO.getAreas() != null) {
//            for (int i = 0; i < addWardrobeDTO.getAreas().size(); i++) {
//                var area = addWardrobeDTO.getAreas().get(i);
//                log.info("区域[{}] - areaName: {}, relativeX: {}, relativeY: {}, relativeWidth: {}, relativeHeight: {}",
//                    i, area.getAreaName(), area.getRelativeX(), area.getRelativeY(),
//                    area.getRelativeWidth(), area.getRelativeHeight());
//            }
//        }
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层添加方法
//        Wardrobe wardrobe = wardrobeService.add(addWardrobeDTO, 10L);
//
//        log.info("用户添加衣柜成功，衣柜ID：{}", wardrobe.getId());
//        return R.success(wardrobe);
//    }
//
//    @SaIgnore
//    @PostMapping("/remove/{id}")
//    @Operation(summary = "删除衣柜", description = "APP用户删除衣柜")
//    public R<Boolean> remove(@PathVariable Long id) throws Exception {
//        log.info("用户删除衣柜，衣柜ID：{}", id);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 调用service层删除方法
//        boolean deleted = wardrobeService.removeByUser(id, 10L);
//
//        if (!deleted) {
//            return R.fail("衣柜不存在或无权删除");
//        }
//
//        log.info("用户删除衣柜成功，衣柜ID：{}", id);
//        return R.success();
//    }
//
//
//    @SaIgnore
//    @PostMapping("/list")
//    @Operation(summary = "分页查询衣柜列表", description = "APP用户分页查询自己的衣柜列表（包含图片URL）")
//    public R<PageResult<WardrobeListVO>> list(@RequestBody Paging paging) throws Exception {
//        log.info("用户查询衣柜列表，分页参数：{}", paging);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 添加用户ID过滤条件
//        if (paging.getConditions() == null) {
//            paging.setConditions(new ArrayList<>());
//        }
//        paging.getConditions().add(new Condition("t1.userId", 10L));
//
//        // 默认按创建时间倒序排列
//        if (paging.getOrders() == null || paging.getOrders().isEmpty()) {
//            paging.setOrders(new ArrayList<>());
//            com.tth.framework.paging.Order order = new com.tth.framework.paging.Order();
//            order.setProperty("t1.createdTime");
//            order.setDirection(com.tth.framework.enums.PagingOrderDirection.DESC);
//            paging.getOrders().add(order);
//        }
//
//        // 转换为包含图片URL的VO
//        PageResult<WardrobeListVO> pageResult = wardrobeService.list(paging);
//        log.info("用户查询衣柜列表成功，总数：{}", pageResult.getTotal());
//
//        return R.success(pageResult);
//    }
//
//
//    @SaIgnore
//    @GetMapping("/get/{id}")
//    @Operation(summary = "获取衣柜详情", description = "APP用户获取衣柜详情（包含区域信息）")
//    public R<WardrobeDetailVO> get(@PathVariable Long id) throws Exception {
//        log.info("用户获取衣柜详情，衣柜ID：{}", id);
//
//        // 获取当前登录用户ID
//        Long currentUserId = AuthUtil.getUserId();
//
//        // 获取衣柜详情
//        WardrobeDetailVO detailVO = wardrobeService.getWardrobeDetail(id, 10L);
//
//        log.info("用户获取衣柜详情成功，衣柜ID：{}，区域数量：{}", id,
//                detailVO.getAreas() != null ? detailVO.getAreas().size() : 0);
//
//        return R.success(detailVO);
//    }
//}
