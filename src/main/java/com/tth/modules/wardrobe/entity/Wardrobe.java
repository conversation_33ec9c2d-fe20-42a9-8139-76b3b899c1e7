package com.tth.modules.wardrobe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.annotation.MarkFileUrl;
import com.tth.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 衣柜表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_wardrobe")
@Schema(name = "Wardrobe", description = "衣柜表")
public class Wardrobe extends BaseEntity {

    @Schema(description = "用户ID（关联tth_user_base）")
    private Long userId;

    @Schema(description = "衣柜名称")
    private String wardrobeName;

    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣柜关闭时的图片ID（关联tth_oss_file）")
    private Long closedImageId;

    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣柜打开时的图片ID（关联tth_oss_file）")
    private Long openedImageId;

    @Schema(description = "排序")
    private Integer sort;

    @TableField(exist = false)
    @Schema(description = "衣柜区域列表")
    private List<WardrobeArea> areas;
}
