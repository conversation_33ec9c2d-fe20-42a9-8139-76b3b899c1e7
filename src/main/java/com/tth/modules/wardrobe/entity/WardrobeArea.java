package com.tth.modules.wardrobe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tth.framework.base.BaseEntity;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 衣柜区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tth_wardrobe_area")
@Schema(name = "WardrobeArea", description = "衣柜区域表")
public class WardrobeArea extends BaseEntity {

    @Schema(description = "衣柜ID（关联tth_wardrobe）")
    private Long wardrobeId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "区域左上角X坐标（百分比）")
    private BigDecimal relativeX;

    @Schema(description = "区域左上角Y坐标（百分比）")
    private BigDecimal relativeY;

    @Schema(description = "区域宽度（百分比）")
    private BigDecimal relativeWidth;

    @Schema(description = "区域高度（百分比）")
    private BigDecimal relativeHeight;

    @Schema(description = "原始X坐标（像素）")
    private Integer pixelX;

    @Schema(description = "原始Y坐标（像素）")
    private Integer pixelY;

    @Schema(description = "原始宽度（像素）")
    private Integer pixelWidth;

    @Schema(description = "原始高度（像素）")
    private Integer pixelHeight;

    private Integer color;

    @Schema(description = "排序")
    private Integer sort;
}
