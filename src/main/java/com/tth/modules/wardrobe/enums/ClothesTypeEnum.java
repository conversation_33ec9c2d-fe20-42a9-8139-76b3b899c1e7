package com.tth.modules.wardrobe.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 衣服类型枚举
 */
@MarkDictType("衣服类型")
@Schema(description = "衣服类型")
@Getter
@AllArgsConstructor
public enum ClothesTypeEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "裙子")
    DRESS(1, "裙子"),

    @Schema(description = "衬衫")
    SHIRT(2, "衬衫"),

    @Schema(description = "T恤")
    T_SHIRT(3, "T恤"),

    @Schema(description = "袜子")
    SOCKS(4, "袜子"),

    @Schema(description = "裤子")
    PANTS(5, "裤子"),

    @Schema(description = "外套")
    JACKET(6, "外套"),

    @Schema(description = "毛衣")
    SWEATER(7, "毛衣"),

    @Schema(description = "牛仔裤")
    JEANS(8, "牛仔裤"),

    @Schema(description = "短裤")
    SHORTS(9, "短裤"),

    @Schema(description = "内衣")
    UNDERWEAR(10, "内衣"),

    @Schema(description = "睡衣")
    PAJAMAS(11, "睡衣"),

    @Schema(description = "运动服")
    SPORTSWEAR(12, "运动服"),

    @Schema(description = "正装")
    FORMAL_WEAR(13, "正装"),

    @Schema(description = "鞋子")
    SHOES(14, "鞋子"),

    @Schema(description = "帽子")
    HAT(15, "帽子"),

    @Schema(description = "围巾")
    SCARF(16, "围巾"),

    @Schema(description = "手套")
    GLOVES(17, "手套"),

    @Schema(description = "腰带")
    BELT(18, "腰带"),

    @Schema(description = "其他")
    OTHER(99, "其他");

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    private final String desc;

    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值查找对应的枚举
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static ClothesTypeEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ClothesTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述查找对应的枚举
     * @param desc 枚举描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static ClothesTypeEnum fromDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (ClothesTypeEnum type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }
}
