package com.tth.modules.wardrobe.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 季节枚举
 */
@MarkDictType("季节")
@Schema(description = "季节")
@Getter
@AllArgsConstructor
public enum SeasonEnum implements IEnum<Integer>, IEnumDesc {

    @Schema(description = "春季")
    SPRING(1, "春季"),

    @Schema(description = "夏季")
    SUMMER(2, "夏季"),

    @Schema(description = "秋季")
    AUTUMN(3, "秋季"),

    @Schema(description = "冬季")
    WINTER(4, "冬季"),

    @Schema(description = "四季")
    ALL_SEASONS(5, "四季");

    /**
     * 枚举值，用于数据库存储
     */
    private final int value;

    /**
     * 枚举描述，用于前端显示
     */
    private final String desc;

    /**
     * 获取枚举值，实现IEnum接口
     * @return 枚举的整数值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据枚举值查找对应的枚举
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static SeasonEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (SeasonEnum season : values()) {
            if (season.getValue().equals(value)) {
                return season;
            }
        }
        return null;
    }

    /**
     * 根据描述查找对应的枚举
     * @param desc 枚举描述
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static SeasonEnum fromDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (SeasonEnum season : values()) {
            if (season.getDesc().equals(desc)) {
                return season;
            }
        }
        return null;
    }
}
