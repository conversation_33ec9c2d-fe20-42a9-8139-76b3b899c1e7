package com.tth.modules.wardrobe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 添加衣柜区域请求DTO
 */
@Data
@Schema(description = "添加衣柜区域请求")
public class AddWardrobeAreaDTO {

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "区域左上角X坐标（百分比）")
    private BigDecimal relativeX;

    @Schema(description = "区域左上角Y坐标（百分比）")
    private BigDecimal relativeY;

    @Schema(description = "区域宽度（百分比）")
    private BigDecimal relativeWidth;

    @Schema(description = "区域高度（百分比）")
    private BigDecimal relativeHeight;

    @Schema(description = "原始X坐标（像素）")
    private Integer pixelX;

    @Schema(description = "原始Y坐标（像素）")
    private Integer pixelY;

    @Schema(description = "原始宽度（像素）")
    private Integer pixelWidth;

    @Schema(description = "原始高度（像素）")
    private Integer pixelHeight;

    @Schema(description = "颜色")
    private Integer color;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;
}
