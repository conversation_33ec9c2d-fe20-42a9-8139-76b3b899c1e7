package com.tth.modules.wardrobe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.List;

/**
 * 添加衣柜请求DTO
 */
@Data
@Schema(description = "添加衣柜请求")
public class AddWardrobeDTO {

    @Schema(description = "衣柜名称")
    private String wardrobeName;

    @Schema(description = "衣柜关闭时的图片ID（关联tth_oss_file）")
    private Long closedImageId;

    @Schema(description = "衣柜打开时的图片ID（关联tth_oss_file）")
    private Long openedImageId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "衣柜区域列表")
    @Valid
    private List<AddWardrobeAreaDTO> areas;
}
