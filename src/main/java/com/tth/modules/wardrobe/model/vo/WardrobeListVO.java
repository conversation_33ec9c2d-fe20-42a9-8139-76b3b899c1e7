package com.tth.modules.wardrobe.model.vo;

import com.tth.framework.annotation.MarkFileUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣柜列表响应VO
 */
@Data
@Schema(description = "衣柜列表响应")
public class WardrobeListVO {

    @Schema(description = "衣柜ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "衣柜名称")
    private String wardrobeName;

    /**
     * 衣柜关闭时的图片ID
     * 序列化结果: {"fileId": 123, "url": "https://..."}
     */
    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣柜关闭时的图片信息")
    private Long closedImageId;

    /**
     * 衣柜打开时的图片ID
     * 序列化结果: {"fileId": 456, "url": "https://..."}
     */
    @MarkFileUrl(expireMinutes = 120)
    @Schema(description = "衣柜打开时的图片信息")
    private Long openedImageId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;

    @Schema(description = "衣柜区域列表")
    private List<WardrobeAreaVO> areas;

    /**
     * 衣柜区域VO
     */
    @Data
    @Schema(description = "衣柜区域")
    public static class WardrobeAreaVO {

        @Schema(description = "区域ID")
        private Long id;

        @Schema(description = "衣柜ID")
        private Long wardrobeId;

        @Schema(description = "区域名称")
        private String areaName;

        @Schema(description = "区域左上角X坐标（百分比）")
        private BigDecimal relativeX;

        @Schema(description = "区域左上角Y坐标（百分比）")
        private BigDecimal relativeY;

        @Schema(description = "区域宽度（百分比）")
        private BigDecimal relativeWidth;

        @Schema(description = "区域高度（百分比）")
        private BigDecimal relativeHeight;

        @Schema(description = "原始X坐标（像素）")
        private Integer pixelX;

        @Schema(description = "原始Y坐标（像素）")
        private Integer pixelY;

        @Schema(description = "原始宽度（像素）")
        private Integer pixelWidth;

        @Schema(description = "原始高度（像素）")
        private Integer pixelHeight;

        @Schema(description = "颜色")
        private Integer color;

        @Schema(description = "排序")
        private Integer sort;

        @Schema(description = "备注")
        private String remark;

        @Schema(description = "创建时间")
        private LocalDateTime createdTime;
    }
}
