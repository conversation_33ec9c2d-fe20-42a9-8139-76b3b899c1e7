package com.tth.modules.wardrobe.service;

import com.tth.modules.wardrobe.entity.Wardrobe;
import com.tth.framework.base.BaseService;
import com.tth.modules.wardrobe.model.vo.WardrobeDetailVO;
import com.tth.modules.wardrobe.model.vo.WardrobeListVO;
import com.tth.modules.wardrobe.model.dto.AddWardrobeDTO;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;

/**
 * <p>
 * 衣柜表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public interface WardrobeService extends BaseService<Wardrobe> {

    /**
     * 添加用户的衣柜（包含关联区域）
     * @param addWardrobeDTO 添加衣柜请求DTO
     * @param userId 用户ID
     * @return 创建的衣柜
     */
    Wardrobe add(AddWardrobeDTO addWardrobeDTO, Long userId);

    /**
     * 根据ID获取衣柜详情（包含区域信息）
     * @param id 衣柜ID
     * @param userId 用户ID（用于权限校验）
     * @return 衣柜详情
     * @throws Exception 异常
     */
    WardrobeDetailVO getWardrobeDetail(Long id, Long userId) throws Exception;


    /**
     * 删除用户的衣柜（包含关联区域）
     * @param id 衣柜ID
     * @param userId 用户ID
     * @return 是否删除成功
     * @throws Exception 异常
     */
    boolean removeByUser(Long id, Long userId) throws Exception;

    /**
     * 将衣柜分页结果转换为包含图片URL的VO
     * @return 包含图片URL的衣柜列表VO
     */
    PageResult<WardrobeListVO> list(Paging paging) throws Exception;
}
