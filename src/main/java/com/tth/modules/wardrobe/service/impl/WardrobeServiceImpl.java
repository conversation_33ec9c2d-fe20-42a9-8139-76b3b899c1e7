package com.tth.modules.wardrobe.service.impl;

import com.tth.modules.wardrobe.entity.Wardrobe;
import com.tth.modules.wardrobe.entity.WardrobeArea;
import com.tth.modules.wardrobe.mapper.WardrobeMapper;
import com.tth.modules.wardrobe.service.WardrobeAreaService;
import lombok.extern.slf4j.Slf4j;
import com.tth.modules.wardrobe.service.WardrobeService;
import com.tth.framework.base.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.tth.modules.wardrobe.model.vo.WardrobeDetailVO;
import com.tth.modules.wardrobe.model.vo.WardrobeListVO;
import com.tth.modules.wardrobe.model.dto.AddWardrobeDTO;
import com.tth.modules.wardrobe.model.dto.AddWardrobeAreaDTO;
import org.springframework.util.CollectionUtils;
import com.tth.framework.exception.code.ValidationExceptionCode;
import com.tth.framework.exception.core.ValidationException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.framework.exception.core.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import com.tth.framework.paging.PageResult;
import com.tth.framework.paging.Paging;
import com.tth.modules.system.service.OssFileService;

import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;

/**
 * <p>
 * 衣柜表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Slf4j
@Service
public class WardrobeServiceImpl extends BaseServiceImpl<WardrobeMapper, Wardrobe> implements WardrobeService {

    @Resource
    private WardrobeMapper wardrobeMapper;

    @Resource
    private WardrobeAreaService wardrobeAreaService;

    @Resource
    private OssFileService ossFileService;

    @Override
    public WardrobeDetailVO getWardrobeDetail(Long id, Long userId) throws Exception {
        if (id == null || userId == null) {
            throw new ValidationException(ValidationExceptionCode.PARAMS_REQUIRED, "衣柜ID和用户ID不能为空");
        }

        // 直接根据ID和用户ID查询衣柜信息
        Wardrobe wardrobe = this.getOne(this.lambdaQuery()
                .eq(Wardrobe::getId, id)
                .eq(Wardrobe::getUserId, userId));

        if (wardrobe == null) {
            throw new BizException(BaseResponseCode.RECORD_NOT_FOUND, "衣柜不存在或无权访问");
        }

        // 构建返回对象
        WardrobeDetailVO detailVO = new WardrobeDetailVO();
        BeanUtils.copyProperties(wardrobe, detailVO);

        // 查询衣柜区域
        List<WardrobeArea> areas = wardrobeAreaService.list(wardrobeAreaService.lambdaQuery()
                .eq(WardrobeArea::getWardrobeId, id)
                .orderByAsc(WardrobeArea::getSort, WardrobeArea::getId));

        // 转换区域信息
        List<WardrobeDetailVO.WardrobeAreaVO> areaVOs = new ArrayList<>();
        for (WardrobeArea area : areas) {
            WardrobeDetailVO.WardrobeAreaVO areaVO = new WardrobeDetailVO.WardrobeAreaVO();
            BeanUtils.copyProperties(area, areaVO);
            areaVOs.add(areaVO);
        }
        detailVO.setAreas(areaVOs);

        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Wardrobe add(AddWardrobeDTO addWardrobeDTO, Long userId) {
        if (addWardrobeDTO == null || userId == null) {
            throw new ValidationException(ValidationExceptionCode.PARAMS_REQUIRED, "参数不能为空");
        }

        log.info("添加用户衣柜，用户ID：{}，衣柜名称：{}", userId, addWardrobeDTO.getWardrobeName());

        // 创建衣柜实体
        Wardrobe wardrobe = new Wardrobe();
        wardrobe.setUserId(userId);
        wardrobe.setWardrobeName(addWardrobeDTO.getWardrobeName());
        wardrobe.setClosedImageId(addWardrobeDTO.getClosedImageId());
        wardrobe.setOpenedImageId(addWardrobeDTO.getOpenedImageId());
        wardrobe.setSort(addWardrobeDTO.getSort());
        wardrobe.setRemark(addWardrobeDTO.getRemark());

        // 保存衣柜
        this.save(wardrobe);
        log.info("保存衣柜成功，衣柜ID：{}", wardrobe.getId());

        // 处理衣柜区域
        if (!CollectionUtils.isEmpty(addWardrobeDTO.getAreas())) {
            List<WardrobeArea> wardrobeAreas = new ArrayList<>();
            for (AddWardrobeAreaDTO areaDTO : addWardrobeDTO.getAreas()) {
                log.info("处理区域DTO - areaName: {}, relativeX: {}, relativeY: {}",
                    areaDTO.getAreaName(), areaDTO.getRelativeX(), areaDTO.getRelativeY());

                WardrobeArea area = new WardrobeArea();
                area.setWardrobeId(wardrobe.getId());
                area.setAreaName(areaDTO.getAreaName());
                area.setRelativeX(areaDTO.getRelativeX());
                area.setRelativeY(areaDTO.getRelativeY());

                log.info("设置后的区域实体 - areaName: {}, relativeX: {}, relativeY: {}",
                    area.getAreaName(), area.getRelativeX(), area.getRelativeY());
                area.setRelativeWidth(areaDTO.getRelativeWidth());
                area.setRelativeHeight(areaDTO.getRelativeHeight());
                area.setPixelX(areaDTO.getPixelX());
                area.setPixelY(areaDTO.getPixelY());
                area.setPixelWidth(areaDTO.getPixelWidth());
                area.setPixelHeight(areaDTO.getPixelHeight());
                area.setColor(areaDTO.getColor());
                area.setSort(areaDTO.getSort());
                area.setRemark(areaDTO.getRemark());
                wardrobeAreas.add(area);
            }

            // 批量保存衣柜区域
            wardrobeAreaService.saveBatch(wardrobeAreas);
            log.info("保存衣柜区域成功，区域数量：{}", wardrobeAreas.size());
        }

        return wardrobe;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByUser(Long id, Long userId) throws Exception {
        if (id == null || userId == null) {
            throw new ValidationException(ValidationExceptionCode.PARAMS_REQUIRED, "衣柜ID和用户ID不能为空");
        }

        // 使用lambda查询构建删除条件
        boolean wardrobeDeleted = this.remove(this.lambdaQuery()
                .eq(Wardrobe::getId, id)
                .eq(Wardrobe::getUserId, userId));

        if (!wardrobeDeleted) {
            return false;
        }

        // 删除关联的衣柜区域
        wardrobeAreaService.remove(wardrobeAreaService.lambdaQuery()
                .eq(WardrobeArea::getWardrobeId, id));

        log.info("删除用户衣柜成功，衣柜ID：{}，用户ID：{}", id, userId);
        return true;
    }

    @Override
    public PageResult<WardrobeListVO> list(Paging paging) throws Exception {
        // 先获取基础的衣柜分页数据（包含区域数据）
        PageResult<Wardrobe> wardrobePageResult = this.listOrPage(paging);

        // 转换为WardrobeListVO（图片URL由@MarkFileUrl注解自动处理）
        List<WardrobeListVO> wardrobeListVOs = new ArrayList<>();
        for (Wardrobe wardrobe : wardrobePageResult.getList()) {
            WardrobeListVO wardrobeListVO = new WardrobeListVO();
            BeanUtils.copyProperties(wardrobe, wardrobeListVO);

            // 转换区域数据
            if (!CollectionUtils.isEmpty(wardrobe.getAreas())) {
                List<WardrobeListVO.WardrobeAreaVO> areaVOs = new ArrayList<>();
                for (WardrobeArea area : wardrobe.getAreas()) {
                    WardrobeListVO.WardrobeAreaVO areaVO = new WardrobeListVO.WardrobeAreaVO();
                    BeanUtils.copyProperties(area, areaVO);
                    areaVOs.add(areaVO);
                }
                wardrobeListVO.setAreas(areaVOs);
            }

            wardrobeListVOs.add(wardrobeListVO);
        }

        // 构建新的分页结果
        return new PageResult<>(
            wardrobePageResult.getPageNum(),
            wardrobePageResult.getPageSize(),
            wardrobePageResult.getTotal(),
            wardrobeListVOs
        );
    }
}
