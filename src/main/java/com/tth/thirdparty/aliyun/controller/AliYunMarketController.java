package com.tth.thirdparty.aliyun.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.response.R;
import com.tth.modules.system.service.OssFileService;
import com.tth.thirdparty.aliyun.dto.OcrIdCardDTO;
import com.tth.thirdparty.aliyun.service.AliYunMarketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "阿里云市场接口", description = "")
@RequestMapping("/aliYunMarket")
public class AliYunMarketController {

    @Resource
    private AliYunMarketService aliYunMarketService;

    @Resource
    private OssFileService ossFileService;

    @ApiLog(module = "阿里云市场模块", value = "身份证OCR识别")
    @Operation(summary = "身份证OCR识别", description = "根据文件ID和身份证面进行OCR识别")
    @PostMapping("/ocrIdCard")
    public R<JSONObject> ocrIdCard(@RequestBody @Valid OcrIdCardDTO dto) {
        // 根据文件ID获取文件URL
        String fileUrl = ossFileService.getFileUrlById(dto.getFileId());
        if (StrUtil.isBlank(fileUrl)) {
            return R.fail("文件不存在或无法获取文件URL");
        }

        // 调用OCR识别服务
        JSONObject result = aliYunMarketService.ocrIdCard(fileUrl, dto.getSide());

        return R.success(result);
    }
}
