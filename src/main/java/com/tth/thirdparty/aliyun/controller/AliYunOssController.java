package com.tth.thirdparty.aliyun.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.tth.framework.annotation.ApiLog;
import com.tth.framework.response.R;
import com.tth.thirdparty.aliyun.service.AliYunOssService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestClient;

import java.util.Map;

@Slf4j
@RestController
@Tag(name = "阿里云OSS接口", description = "")
@RequestMapping("/aliYunOss")
public class AliYunOssController {

    @Resource
    private AliYunOssService aliYunOssService;

    /**
     * 获取OSS上传签名
     * 前端可以使用这个签名直接上传文件到OSS
     *
     * @param fileName 文件名
     * @return 上传签名信息
     */
    @ApiLog(module = "阿里云OSS模块", value = "获取OSS上传签名")
    @Operation(summary = "获取OSS上传签名", description = "获取阿里云OSS上传签名，用于前端直传")
    @GetMapping("/signature")
    public R<Map<String, String>> getSignature(@Parameter(description = "文件名", required = true) @RequestParam String fileName) {
        // 验证文件名
        if (StrUtil.isBlank(fileName)) {
            return R.fail("文件名不能为空");
        }

        // 检查文件名是否包含不安全字符
        if (fileName.matches(".*[\\\\/:*?\"<>|].*")) {
            return R.fail("文件名包含不安全字符");
        }
        return R.success(aliYunOssService.getPostSignatureForOssUpload(fileName));

    }


    public static void main(String[] args) {
        String uploadFileName = "jhls.ttf";
        String uploadFilePath = "D:\\Quick\\Downloads\\jhls.ttf";


        RestClient restClient1 = RestClient.create();

        JSONObject loginDTO = new JSONObject();
        loginDTO.set("identityType", "USERNAME");
        loginDTO.set("identifier", "superadmin");
        loginDTO.set("credential", "123456");
        loginDTO.set("userType", "ADMIN");
        JSONObject loginResponse = restClient1.post()
                .uri("http://api.heartecho.xin:8999/auth/login")
                .body(loginDTO)
                .retrieve()
                .body(JSONObject.class);
        if(loginResponse.getInt("code") != 200000){
            log.error("登录失败: {}", loginResponse);
            return;
        }

        JSONObject signatureResponse = restClient1.get()
                .uri("http://api.heartecho.xin:8999/aliYunOss/signature",
                        uriBuilder -> uriBuilder
                                .queryParam("fileName", uploadFileName)
                                .build()
                )
                .header("Authorization", loginResponse.getJSONObject("data").getStr("accessToken"))
                .retrieve()
                .body(JSONObject.class);

        if(signatureResponse.getInt("code") != 200000){
            log.error("获取OSS上传签名失败: {}", signatureResponse);
            return;
        }

        JSONObject jsonObject = signatureResponse.getJSONObject("data");
        // 创建表单数据
        MultipartBodyBuilder formData = new MultipartBodyBuilder();
        formData.part("success_action_status", "200");
        formData.part("policy", jsonObject.getStr("policy"));
        formData.part("x-oss-signature", jsonObject.getStr("x-oss-signature"));
        formData.part("x-oss-signature-version", jsonObject.getStr("x-oss-signature-version"));
        formData.part("x-oss-credential", jsonObject.getStr("x-oss-credential"));
        formData.part("x-oss-date", jsonObject.getStr("x-oss-date"));
        formData.part("key", jsonObject.getStr("key"));
        formData.part("x-oss-security-token", jsonObject.getStr("x-oss-security-token"));
        formData.part("callback", jsonObject.getStr("callback"));

        // 添加文件
        org.springframework.core.io.Resource fileResource = new FileSystemResource(uploadFilePath);
        formData.part("file", fileResource);


// 发送请求
        String response = restClient1.post()
                .uri(jsonObject.getStr("host"))
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(formData.build())
                .retrieve()
                .body(String.class);

        log.info(response);
    }
}
