package com.tth.thirdparty.aliyun.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 身份证OCR识别请求DTO
 */
@Getter
@Setter
@Schema(name = "OcrIdCardDTO", description = "身份证OCR识别请求")
public class OcrIdCardDTO {

    @NotNull(message = "文件ID不能为空")
    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fileId;

    @NotNull(message = "身份证面不能为空")
    @Pattern(regexp = "^(face|back)$", message = "身份证面只能是face或back")
    @Schema(description = "身份证面（face-人脸面，back-国徽面）", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"face", "back"})
    private String side;
}
