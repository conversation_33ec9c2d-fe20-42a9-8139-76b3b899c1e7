package com.tth.thirdparty.aliyun.enums;

import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("阿里云OSS配置")
@Schema(description = "阿里云OSS配置")
public enum AliYunOssProperty implements IEnumDesc {

    @Schema(description = "Endpoint")
    ENDPOINT("Endpoint"),

    @Schema(description = "Bucket")
    BUCKET("Bucket"),

    @Schema(description = "自定义域名")
    CUSTOM_DOMAIN("自定义域名"),

    @Schema(description = "Region")
    REGION("Region"),

    @Schema(description = "AccessKey ID")
    ACCESS_KEY_ID("AccessKey ID"),

    @Schema(description = "AccessKey Secret")
    ACCESS_KEY_SECRET("AccessKey Secret"),

    @Schema(description = "RoleArn")
    ROLE_ARN("RoleArn"),

    @Schema(description = "STS Endpoint")
    STS_ENDPOINT("STS Endpoint"),

    @Schema(description = "回调域名")
    CALLBACK_HOST("回调域名"),
    ;

    private final String desc;
}
