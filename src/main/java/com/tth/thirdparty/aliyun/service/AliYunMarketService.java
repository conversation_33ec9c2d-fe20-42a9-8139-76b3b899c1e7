package com.tth.thirdparty.aliyun.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

@Slf4j
@Component
public class AliYunMarketService {

    private static final String OCR_ID_CARD_URL = "https://cardnumber.market.alicloudapi.com/rest/160601/ocr/ocr_idcard.json";

    @Resource
    private RestClient restClient;

    public JSONObject ocrIdCard(String imgUrl, String side) {
        String appCode = "6995c7d1e6b440b78c469988e1968679";

        JSONObject requestBody = new JSONObject();
        requestBody.set("image", imgUrl);
        JSONObject configure = new JSONObject();
        configure.set("side", side);
        configure.set("quality_info", true);
        requestBody.set("configure", configure);

        JSONObject response = restClient.post()
                .uri(OCR_ID_CARD_URL)
                .headers(headers -> {
                    headers.set("Authorization", "APPCODE " + appCode);
                })
                .body(requestBody)
                .retrieve()
                .body(JSONObject.class);
        if(ObjUtil.isNull(response)){
            log.error("调用身份证OCR文字识别接口返回结果为null");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        if (!response.getBool("success")) {
            log.error("调用身份证OCR文字识别接口返回success为false。返回结果：{}", response);
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        // 成功返回
        return response;
    }
}
