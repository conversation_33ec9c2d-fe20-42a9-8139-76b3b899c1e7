package com.tth.thirdparty.aliyun.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.Callback;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.framework.invoker.SdkInvoker;
import com.tth.framework.invoker.SdkResultChecker;
import com.tth.framework.utils.AuthUtil;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.aliyun.enums.AliYunOssProperty;
import com.tth.thirdparty.volcengine.enums.VolcengineProperty;
import com.tth.common.utils.RedisUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class AliYunOssService {

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private RestClient restClient;

    // 签名有效时长
    private static final int expireSeconds = 10000;

    // OSS签名缓存前缀
    private static final String OSS_SIGNATURE_CACHE_PREFIX = "oss:signature:";

    /**
     * 获取OSS上传签名
     *
     * @param fileName 文件名
     * @return 签名信息
     */
    public Map<String, String> getPostSignatureForOssUpload(String fileName) {
        // 生成唯一的签名ID
        String signatureId = IdUtil.fastSimpleUUID();
        Map<String, String> aliYunOssConfig = systemConfigService.getAllConfigValues(SystemConfigProviderEnum.ALI_YUN_OSS);
        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials sts_data = getCredential(aliYunOssConfig);

        String accessKeyId =  sts_data.accessKeyId;
        String accessKeySecret =  sts_data.accessKeySecret;
        String securityToken =  sts_data.securityToken;

        //获取x-oss-credential里的date，当前UTC日期，格式为yyyyMMdd
        String date = LocalDate.now(ZoneOffset.UTC).format(DateTimeFormatter.BASIC_ISO_DATE);

        //获取x-oss-date
        ZonedDateTime now = ZonedDateTime.now().withZoneSameInstant(java.time.ZoneOffset.UTC);
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
        String x_oss_date = now.format(formatter2);

        // 步骤1：创建policy。
        String region = aliYunOssConfig.get(AliYunOssProperty.REGION.name());
        String x_oss_credential = accessKeyId + "/" + date + "/" + region + "/oss/aliyun_v4_request";

        ObjectMapper mapper = new ObjectMapper();

        Map<String, Object> policy = new HashMap<>();
        policy.put("expiration", generateExpiration());

        List<Object> conditions = new ArrayList<>();

        Map<String, String> bucketCondition = new HashMap<>();
        bucketCondition.put("bucket", aliYunOssConfig.get(AliYunOssProperty.BUCKET.name()));
        conditions.add(bucketCondition);

        Map<String, String> securityTokenCondition = new HashMap<>();
        securityTokenCondition.put("x-oss-security-token", securityToken);
        conditions.add(securityTokenCondition);

        Map<String, String> signatureVersionCondition = new HashMap<>();
        signatureVersionCondition.put("x-oss-signature-version", "OSS4-HMAC-SHA256");
        conditions.add(signatureVersionCondition);

        Map<String, String> credentialCondition = new HashMap<>();
        credentialCondition.put("x-oss-credential", x_oss_credential); // 替换为实际的 access key id
        conditions.add(credentialCondition);

        Map<String, String> dateCondition = new HashMap<>();
        dateCondition.put("x-oss-date", x_oss_date);
        conditions.add(dateCondition);

        // 使用指定的对象名
        conditions.add(Arrays.asList("content-length-range", 1, 200 * 1024000)); // 最大上传大小：200M
        conditions.add(Arrays.asList("eq", "$success_action_status", "200"));

        // 根据是否指定了精确的对象名来决定使用eq还是starts-with条件
        Long userId = AuthUtil.getUserId();
        String key = StrUtil.format("{}/{}/{}/{}/{}", DigestUtil.md5Hex(fileName).substring(0, 4),
                IdUtil.getSnowflake().nextId(),
                HexUtil.toHex(userId),
                LocalDateTimeUtil.now().format(DateTimeFormatter.ISO_LOCAL_DATE),
                fileName);
        conditions.add(Arrays.asList("eq", "$key", key));

        policy.put("conditions", conditions);

        String jsonPolicy = null;
        try {
            jsonPolicy = mapper.writeValueAsString(policy);
        } catch (JsonProcessingException e) {
            log.error("json 处理异常", e);
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        // 步骤2：构造待签名字符串（StringToSign）。
        String stringToSign = new String(org.apache.commons.codec.binary.Base64.encodeBase64(jsonPolicy.getBytes()));

        // 步骤3：计算SigningKey。
        byte[] dateKey = hmacsha256(("aliyun_v4" + accessKeySecret).getBytes(), date);
        byte[] dateRegionKey = hmacsha256(dateKey, region);
        byte[] dateRegionServiceKey = hmacsha256(dateRegionKey, "oss");
        byte[] signingKey = hmacsha256(dateRegionServiceKey, "aliyun_v4_request");

        // 步骤4：计算Signature。
        byte[] result = hmacsha256(signingKey, stringToSign);
        String signature = BinaryUtil.toHex(result);

        // 步骤5：设置回调。
        // 将签名ID添加到回调URL中
        String callbackUrl = aliYunOssConfig.get(AliYunOssProperty.CALLBACK_HOST.name()) + "/ossFile/callback?signatureId=" + signatureId; // 设置回调请求的服务器地址
        JSONObject jasonCallback = new JSONObject();
        jasonCallback.set("callbackUrl", callbackUrl);
        jasonCallback.set("callbackBody", callbackBody());
        jasonCallback.set("callbackBodyType", "application/x-www-form-urlencoded");

        String base64CallbackBody = BinaryUtil.toBase64String(jasonCallback.toString().getBytes());

        // 构建OSS主机地址
        String host = "https://" + aliYunOssConfig.get(AliYunOssProperty.BUCKET.name()) + "." + aliYunOssConfig.get(AliYunOssProperty.ENDPOINT.name());
        Map<String, String> response = new HashMap<>();
        // 将数据添加到 map 中
        response.put("host", host);
        response.put("success_action_status", "200");
        response.put("policy", stringToSign);
        response.put("x-oss-signature", signature);
        response.put("x-oss-signature-version", "OSS4-HMAC-SHA256");
        response.put("x-oss-credential", x_oss_credential);
        response.put("x-oss-date", x_oss_date);
        response.put("key", key);  // 返回完整的对象名
        response.put("x-oss-security-token", securityToken);
        response.put("callback", base64CallbackBody);

        // 将签名信息存入Redis缓存，设置过期时间为签名有效期的两倍，确保回调时签名仍然有效
        String cacheKey = OSS_SIGNATURE_CACHE_PREFIX + signatureId;
        RedisUtil.set(cacheKey, key, expireSeconds * 2);
        log.info("已缓存OSS签名，ID: {}, 对象名: {}", signatureId, key);

        // 返回带有状态码 200 (OK) 的 ResponseEntity，返回给Web端，进行PostObject操作
        return response;
    }

    private String generateExpiration() {
        // 获取当前时间戳（以秒为单位）
        long now = Instant.now().getEpochSecond();
        // 计算过期时间的时间戳
        long expirationTime = now + expireSeconds;
        // 将时间戳转换为Instant对象，并格式化为ISO8601格式
        Instant instant = Instant.ofEpochSecond(expirationTime);
        // 定义时区为UTC
        ZoneId zone = ZoneOffset.UTC;
        // 将 Instant 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(zone);
        // 定义日期时间格式，例如2023-12-03T13:00:00.000Z
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        // 格式化日期时间
        // 输出结果
        return zonedDateTime.format(formatter);
    }

    //初始化STS Client
    //ram角色的accessKeyId和accessKeySecret
    private com.aliyun.sts20150401.Client createStsClient(Map<String, String> aliYunOssConfig)  {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 OSS_ACCESS_KEY_ID。
                .setAccessKeyId(aliYunOssConfig.get(AliYunOssProperty.ACCESS_KEY_ID.name()))
                // 必填，请确保代码运行环境设置了环境变量 OSS_ACCESS_KEY_SECRET。
                // 必填，请确保代码运行环境设置了环境变量 OSS_ACCESS_KEY_SECRET。
                .setAccessKeySecret(aliYunOssConfig.get(AliYunOssProperty.ACCESS_KEY_SECRET.name()));
        // Endpoint 请参考 https://api.aliyun.com/product/Sts
        config.endpoint = aliYunOssConfig.get(AliYunOssProperty.STS_ENDPOINT.name());
        try {
            return new com.aliyun.sts20150401.Client(config);
        } catch (Exception e) {
            log.error("Failed to create STS client", e);
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }
    }

    //获取STS临时凭证
    private AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials getCredential(Map<String, String> aliYunOssConfig) {
        com.aliyun.sts20150401.Client client = this.createStsClient(aliYunOssConfig);
        com.aliyun.sts20150401.models.AssumeRoleRequest assumeRoleRequest = new com.aliyun.sts20150401.models.AssumeRoleRequest()
                // 必填，请确保代码运行环境设置了环境变量 OSS_STS_ROLE_ARN
                .setRoleArn(aliYunOssConfig.get(AliYunOssProperty.ROLE_ARN.name()))
                .setDurationSeconds(43200L) // 设置凭证有效期为12小时
                .setRoleSessionName("yourRoleSessionName");// 自定义会话名称
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

        // 使用SdkInvoker包装API调用，记录日志
        AssumeRoleResponse response = SdkInvoker.invoke(
                "阿里云", // SDK名称
                "调用阿里云STS服务", // 操作描述
                assumeRoleRequest, // 请求参数
                () -> {
                    try {
                        return client.assumeRoleWithOptions(assumeRoleRequest, runtime);
                    } catch (Exception e) {
                        throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, e);
                    }
                },
                // 结果检查器，检查响应的状态码是否为200
                result -> {
                    // 保存真实的响应码
                    int statusCode = result != null ? result.statusCode : -1;
                    if (statusCode == HttpStatus.OK.value()) {
                        // 成功时保存完整的状态信息
                        return SdkResultChecker.ResultCheckOutcome.success(statusCode, statusCode);
                    } else {
                        // 失败时保存完整的状态信息
                        return SdkResultChecker.ResultCheckOutcome.failure("阿里云STS服务返回非200状态码: " + statusCode, statusCode, statusCode);
                    }
                }
        );
        // credentials里包含了后续要用到的AccessKeyId、AccessKeySecret和SecurityToken。
        return response.body.credentials;
        // 返回一个默认的错误响应对象，避免返回null
//        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials defaultCredentials = new AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials();
//        defaultCredentials.accessKeyId = "ERROR_ACCESS_KEY_ID";
//        defaultCredentials.accessKeySecret = "ERROR_ACCESS_KEY_SECRET";
//        defaultCredentials.securityToken = "ERROR_SECURITY_TOKEN";
//        return defaultCredentials;
    }

    /**
     * 验证签名是否有效
     *
     * @param signatureId 签名ID
     * @param objectName 对象名
     * @return 是否有效
     */
    public boolean verifyAndRemoveSignature(String signatureId, String objectName) {
        if (StrUtil.isBlank(signatureId) || StrUtil.isBlank(objectName)) {
            log.warn("验证签名失败：签名ID或对象名为空");
            return false;
        }

        String cacheKey = OSS_SIGNATURE_CACHE_PREFIX + signatureId;
        Object cachedObjectName = RedisUtil.get(cacheKey);

        if (cachedObjectName == null) {
            log.warn("验证签名失败：签名不存在或已过期，签名ID: {}", signatureId);
            return false;
        }

        // 验证对象名是否匹配
        boolean isValid = objectName.equals(cachedObjectName.toString());

        // 无论验证结果如何，都删除缓存中的签名，确保一次性使用
        RedisUtil.delete(cacheKey);
        log.info("签名已从缓存中删除，ID: {}, 验证结果: {}", signatureId, isValid ? "成功" : "失败");

        return isValid;
    }

    private byte[] hmacsha256(byte[] key, String data) {
        try {
            // 初始化HMAC密钥规格，指定算法为HMAC-SHA256并使用提供的密钥。
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");

            // 获取Mac实例，并通过getInstance方法指定使用HMAC-SHA256算法。
            Mac mac = Mac.getInstance("HmacSHA256");
            // 使用密钥初始化Mac对象。
            mac.init(secretKeySpec);

            // 执行HMAC计算，通过doFinal方法接收需要计算的数据并返回计算结果的数组。
            return mac.doFinal(data.getBytes());
        } catch (Exception e) {
            log.error("Failed to calculate HMAC-SHA256", e);
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }
    }

    public String generatePreSignedUrl(String objectName) {
        return this.generatePreSignedUrl(objectName, 30);
    }

    public String generatePreSignedUrl(String objectName, int expireMinutes) {
        Map<String, String> aliYunOssConfig = systemConfigService.getAllConfigValues(SystemConfigProviderEnum.ALI_YUN_OSS);

        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials sts_data = getCredential(aliYunOssConfig);

        String endpoint = "https://" + aliYunOssConfig.get(AliYunOssProperty.ENDPOINT.name());
        if(StrUtil.isNotBlank(aliYunOssConfig.get(AliYunOssProperty.CUSTOM_DOMAIN.name()))){
            endpoint = aliYunOssConfig.get(AliYunOssProperty.CUSTOM_DOMAIN.name());
        }

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, sts_data.accessKeyId, sts_data.accessKeySecret, sts_data.securityToken);

        try {
            // 设置签名URL过期时间，单位为秒，本示例以设置过期时间为30天为例。您可以根据实际业务场景，设置合理的过期时间。

            // 获取当前时间的毫秒数。
            long currentTimeMillis = System.currentTimeMillis();
            // 计算30天后的时间，用秒数表示;
            long expirationSeconds = (currentTimeMillis / 1000) + (expireMinutes * 60);

            // 将秒数转换回毫秒用于Date构造
            Date expiration = new Date(expirationSeconds * 1000);

            // 生成以GET方法访问的签名URL，在签名URL有效期内访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(aliYunOssConfig.get(AliYunOssProperty.BUCKET.name()), objectName, expiration);
            return url.toString();
        } catch (OSSException oe) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, oe);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 生成对象名称（带用户ID和时间戳的路径）
     *
     * @param fileName 原始文件名
     * @param userId   用户ID
     * @return 生成的对象名称
     */
    public String generateObjectName(String fileName, Long userId) {
        return StrUtil.format("{}/{}/{}/{}/{}",
            DigestUtil.md5Hex(fileName).substring(0, 4),
            IdUtil.getSnowflake().nextId(),
            HexUtil.toHex(userId),
            LocalDateTimeUtil.now().format(DateTimeFormatter.ISO_LOCAL_DATE),
            fileName);
    }

    /**
     * 生成对象名称（从当前线程获取用户ID）
     *
     * @param fileName 原始文件名
     * @return 生成的对象名称
     */
    public String generateObjectName(String fileName) {
        Long userId = AuthUtil.getUserId();
        if (userId == null) {
            throw new IllegalStateException("无法获取当前用户ID，请使用generateObjectName(fileName, userId)方法");
        }
        return generateObjectName(fileName, userId);
    }

    /**
     * 生成对象名称（自定义路径前缀）
     *
     * @param pathPrefix 路径前缀
     * @param fileName   原始文件名
     * @return 生成的对象名称
     */
    public String generateObjectName(String pathPrefix, String fileName) {
        return StrUtil.format("{}/{}", pathPrefix, fileName);
    }

    /**
     * 创建自定义的上传回调配置
     *
     * @param callbackUrl  回调URL
     * @param callbackBody 回调Body内容
     * @param callbackVars 自定义回调变量
     * @return 回调配置
     */
    public Callback createCustomCallback(String callbackUrl, String callbackBody, Map<String, String> callbackVars) {
        Callback callback = new Callback();
        callback.setCallbackUrl(callbackUrl);
        callback.setCalbackBodyType(Callback.CalbackBodyType.JSON);
        callback.setCallbackBody(callbackBody);

        // 添加自定义变量
        if (callbackVars != null && !callbackVars.isEmpty()) {
            for (Map.Entry<String, String> entry : callbackVars.entrySet()) {
                callback.addCallbackVar(entry.getKey(), entry.getValue());
            }
        }

        return callback;
    }

    /**
     * 通用上传方法 - 支持多种数据源类型
     *
     * @param objectName 对象名称（文件在OSS中的路径）
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @param metadata   文件元数据（可选）
     * @param callback   上传回调配置（可选）
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObject(String objectName, Object dataSource, ObjectMetadata metadata, Callback callback) {
        Map<String, String> aliYunOssConfig = systemConfigService.getAllConfigValues(SystemConfigProviderEnum.ALI_YUN_OSS);
        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials stsData = getCredential(aliYunOssConfig);

        String endpoint = "https://" + aliYunOssConfig.get(AliYunOssProperty.ENDPOINT.name());
        String bucketName = aliYunOssConfig.get(AliYunOssProperty.BUCKET.name());

        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, stsData.accessKeyId, stsData.accessKeySecret, stsData.securityToken);

        return SdkInvoker.invoke("阿里云OSS", "通用上传文件", objectName, () -> {
            InputStream inputStream = null;
            boolean needCloseStream = false;

            try {
                // 根据数据源类型创建PutObjectRequest
                PutObjectRequest putObjectRequest;

                if (dataSource instanceof File) {
                    // 文件上传
                    putObjectRequest = new PutObjectRequest(bucketName, objectName, (File) dataSource);
                } else if (dataSource instanceof InputStream) {
                    // 输入流上传
                    putObjectRequest = new PutObjectRequest(bucketName, objectName, (InputStream) dataSource);
                } else if (dataSource instanceof byte[]) {
                    // 字节数组上传
                    inputStream = new ByteArrayInputStream((byte[]) dataSource);
                    needCloseStream = true;
                    putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);
                } else if (dataSource instanceof URL) {
                    // 网络URL上传
                    inputStream = ((URL) dataSource).openStream();
                    needCloseStream = true;
                    putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);
                } else {
                    throw new IllegalArgumentException("不支持的数据源类型: " + dataSource.getClass().getName() +
                        "，支持的类型：File、InputStream、byte[]、URL");
                }

                // 设置元数据
                if (metadata != null) {
                    putObjectRequest.setMetadata(metadata);
                }

                // 设置回调
                if (callback != null) {
                    putObjectRequest.setCallback(callback);
                }

                // 上传文件
                PutObjectResult result = ossClient.putObject(putObjectRequest);

                // 如果设置了回调，OSS会在上传完成后主动请求我们的回调接口
                if (callback != null) {
                    log.info("OSS上传完成，已设置回调URL: {}", callback.getCallbackUrl());
                }

                return result;
            } catch (IOException | OSSException e) {
                log.error("OSS上传失败", e);
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
            } finally {
                // 关闭我们创建的输入流
                if (needCloseStream && inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        log.warn("关闭输入流失败: {}", e.getMessage());
                    }
                }
                // 关闭OSS客户端
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
        }, result -> {
            // 检查上传结果
            if (result == null) {
                return SdkResultChecker.ResultCheckOutcome.failure("OSS上传返回结果为null");
            }
            if (StrUtil.isBlank(result.getETag())) {
                return SdkResultChecker.ResultCheckOutcome.failure("OSS上传失败，ETag为空");
            }
            return SdkResultChecker.ResultCheckOutcome.success();
        });
    }

    /**
     * 通用上传方法 - 带回调版本
     *
     * @param objectName 对象名称（文件在OSS中的路径）
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @param callback   上传回调配置
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObject(String objectName, Object dataSource, Callback callback) {
        return putObject(objectName, dataSource, null, callback);
    }

    /**
     * 通用上传方法 - 带元数据版本
     *
     * @param objectName 对象名称（文件在OSS中的路径）
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @param metadata   文件元数据
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObject(String objectName, Object dataSource, ObjectMetadata metadata) {
        return putObject(objectName, dataSource, metadata, null);
    }

    /**
     * 通用上传方法 - 只有数据源和回调（自动生成对象名），使用默认回调
     *
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObject(Object dataSource) {
        // 根据数据源类型生成文件名和对象名
        String fileName = generateFileNameFromDataSource(dataSource);
        String objectName = generateObjectName(fileName);
        return putObject(objectName, dataSource, null, createCallback());
    }

    /**
     * 通用上传方法 - 数据源和自定义回调（自动生成对象名）
     *
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @param callback   上传回调配置
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObject(Object dataSource, Callback callback) {
        // 根据数据源类型生成文件名和对象名
        String fileName = generateFileNameFromDataSource(dataSource);
        String objectName = generateObjectName(fileName, 0L);
        return putObject(objectName, dataSource, null, callback);
    }

    /**
     * 上传文件并使用默认回调（自动生成对象名）
     *
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObjectWithCallback(Object dataSource) {
        // 根据数据源类型生成文件名和对象名
        String fileName = generateFileNameFromDataSource(dataSource);
        String objectName = generateObjectName(fileName, 0L);
        return putObject(objectName, dataSource, null, createCallback());
    }

    /**
     * 上传文件并使用默认回调（自动生成对象名）
     *
     * @param dataSource 数据源（可以是File、InputStream、byte[]、URL）
     * @param extName 文件名的扩展名
     * @return 上传结果，包含ETag等信息
     */
    public PutObjectResult putObjectWithCallback(Object dataSource, String extName) {
        // 根据文件名生成对象名
        String fileName = generateFileNameFromDataSource(dataSource, extName);
        String objectName = generateObjectName(fileName, 0L);
        return putObject(objectName, dataSource, null, createCallback());
    }

    /**
     * 根据数据源生成文件名
     *
     * @param dataSource 数据源
     * @return 文件名
     */
    private String generateFileNameFromDataSource(Object dataSource) {
        return this.generateFileNameFromDataSource(dataSource, null);
    }

    /**
     * 根据数据源生成文件名
     *
     * @param dataSource 数据源
     * @return 文件名
     */
    private String generateFileNameFromDataSource(Object dataSource, String extName) {
        String fileName;
        if (dataSource instanceof File) {
            fileName = ((File) dataSource).getName();
        } else if (dataSource instanceof URL) {
            String path = ((URL) dataSource).getPath();
            fileName = path.substring(path.lastIndexOf('/') + 1);
            // 移除查询参数
            if (fileName.contains("?")) {
                fileName = fileName.split("\\?")[0];
            }
            // 如果没有文件名或文件名为空，生成一个默认文件名
            if (StrUtil.isBlank(fileName) || !fileName.contains(".")) {
                fileName = "network_file_" + System.currentTimeMillis();
            }
        } else {
            // 对于byte[]和InputStream，生成默认文件名
            fileName = "upload_file_" + System.currentTimeMillis();
        }
        if(StrUtil.isNotBlank(fileName)){
            return fileName + "." + extName;
        }
        return fileName;
    }

    public boolean verifyOSSCallbackRequest(HttpServletRequest request, String ossCallbackBody) {
        String authorizationInput = request.getHeader("Authorization");
        String pubKeyInput = request.getHeader("x-oss-pub-key-url");
        byte[] authorization = BinaryUtil.fromBase64String(authorizationInput);
        byte[] pubKey = BinaryUtil.fromBase64String(pubKeyInput);
        String pubKeyAddr = new String(pubKey);
        if (!pubKeyAddr.startsWith("http://gosspublic.alicdn.com/") && !pubKeyAddr.startsWith("https://gosspublic.alicdn.com/")) {
            log.error("pub key addr must be oss addrss");
            return false;
        }
        String retString = restClient.get().uri(pubKeyAddr).retrieve().body(String.class);
        retString = retString.replace("-----BEGIN PUBLIC KEY-----", "");
        retString = retString.replace("-----END PUBLIC KEY-----", "");
        String queryString = request.getQueryString();
        String uri = request.getRequestURI();
        String authStr = java.net.URLDecoder.decode(uri, StandardCharsets.UTF_8);
        if (queryString != null && !queryString.isEmpty()) {
            authStr += "?" + queryString;
        }
        authStr += "\n" + ossCallbackBody;
        return doCheck(authStr, authorization, retString);
    }

    private boolean doCheck(String content, byte[] sign, String publicKey) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = BinaryUtil.fromBase64String(publicKey);
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            java.security.Signature signature = java.security.Signature.getInstance("MD5withRSA");
            signature.initVerify(pubKey);
            signature.update(content.getBytes());
            return signature.verify(sign);
        } catch (Exception e) {
            log.error("OSS回调验证失败", e);
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }
    }

    public String callbackBody(){
        return "bucket=${bucket}&object=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}" +
                "&height=${imageInfo.height}&width=${imageInfo.width}&format=${imageInfo.format}" +
                "&clientIp=${clientIp}&operation=${operation}";
    }

    public Callback createCallback() {
        String callbackHost = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_CALLBACK_HOST);
        String callbackUrl = callbackHost + "/ossFile/callback";

        Callback callback = new Callback();
        callback.setCallbackUrl(callbackUrl);
        callback.setCallbackHost(callbackHost.replace("https://", "").replace("http://", ""));
        // 设置回调体为JSON格式，包含OSS内置参数和自定义参数
        callback.setCallbackBody(callbackBody());
        return callback;
    }
}
