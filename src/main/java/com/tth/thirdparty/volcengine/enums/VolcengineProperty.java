package com.tth.thirdparty.volcengine.enums;

import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@MarkDictType("火山引擎配置")
@Schema(description = "火山引擎配置")
public enum VolcengineProperty implements IEnumDesc {

    @Schema(description = "Access Key ID")
    ACCESS_KEY_ID("Access Key ID"),

    @Schema(description = "Secret Access Key")
    SECRET_ACCESS_KEY("Secret Access Key"),

    @Schema(description = "Region")
    REGION("Region"),

    @Schema(description = "Secret Access Key")
    region_id("Secret Access Key"),

    @Schema(description = "语音技术AppID")
    VOL_TECH_APP_ID("语音技术AppID"),

    @Schema(description = "语音技术Access Token")
    VOL_TECH_ACCESS_TOKEN("语音技术Access Token"),

    @Schema(description = "语音技术Secret Key")
    VOL_TECH_SECRET_KEY("语音技术Secret Key"),

    @Schema(description = "语音技术合成任务回调域名")
    VOL_TECH_CALLBACK_HOST("语音技术合成任务回调域名"),
    ;

    private final String desc;
}
