package com.tth.thirdparty.volcengine.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.framework.response.BaseResponseCode;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.volcengine.enums.VolcengineProperty;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Component
public class TTSAsyncService {

    /**
     * 普通版语音合成提交任务接口
     */
    private static final String TTS_ASYNC_SUBMIT_URL = "https://openspeech.bytedance.com/api/v1/tts_async/submit";

    /**
     * 带情感版语音合成提交任务接口
     */
    private static final String TTS_ASYNC_WITH_EMOTION_SUBMIT_URL = "https://openspeech.bytedance.com/api/v1/tts_async_with_emotion/submit";

    /**
     * 普通版语音合成任务查询接口
     */
    private static final String TTS_ASYNC_QUERY_URL = "https://openspeech.bytedance.com/api/v1/tts_async/query";

    /**
     * 带情感版语音合成任务查询接口
     */
    private static final String TTS_ASYNC_WITH_EMOTION_QUERY_URL = "https://openspeech.bytedance.com/api/v1/tts_async_with_emotion/query";

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private RestClient restClient;

    public JSONObject submitTTSAsyncTask(JSONObject requestBody) {
        // 获取配置
        String accessToken = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_ACCESS_TOKEN);
        if (StrUtil.isBlank(accessToken)) {
            throw new BizException(BaseResponseCode.FAIL, "配置不完整");
        }

        JSONObject res = restClient.post()
                .uri(TTS_ASYNC_SUBMIT_URL)
                .headers(headers -> {
                    headers.set("Authorization", "Bearer; " + accessToken);
                    headers.set("Resource-Id", "volc.tts_async.default");
                })
                .body(requestBody)
                .retrieve()
                .body(JSONObject.class);
        if(ObjUtil.isNull(res)){
            log.error("调用普通版语音合成提交任务接口返回结果为null");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        if (ObjUtil.isNotNull(res.getInt("code"))) {
            log.error("调用普通版语音合成提交任务接口返回结果有错误。错误码：{}, 错误信息：{}", res.getStr("code"), res.getStr("message"));
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        // 成功返回
        return res;
    }

    public JSONObject submitTTSAsyncEmotionTask(JSONObject requestBody) { // 获取配置
        String accessToken = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_ACCESS_TOKEN);
        if (StrUtil.isBlank(accessToken)) {
            throw new BizException(BaseResponseCode.FAIL, "配置不完整");
        }

        JSONObject res = restClient.post()
                .uri(TTS_ASYNC_WITH_EMOTION_SUBMIT_URL)
                .headers(headers -> {
                    headers.set("Authorization", "Bearer; " + accessToken);
                    headers.set("Resource-Id", "volc.tts_async.emotion");
                })
                .body(requestBody)
                .retrieve()
                .body(JSONObject.class);

        if(ObjUtil.isNull(res)){
            log.error("调用带情感版语音合成提交任务接口返回结果为null");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        if (ObjUtil.isNotNull(res.getInt("code"))) {
            log.error("调用带情感版语音合成提交任务接口返回结果有错误。错误码：{}, 错误信息：{}", res.getStr("code"), res.getStr("message"));
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }
        // 成功返回
        return res;
    }

    public JSONObject ttsAsyncQuery(JSONObject requestBody) {
        // 获取配置
        String accessToken = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_ACCESS_TOKEN);
        if (StrUtil.isBlank(accessToken)) {
            throw new BizException(BaseResponseCode.FAIL, "配置不完整");
        }

        // 使用UriComponentsBuilder构建查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(TTS_ASYNC_QUERY_URL);
        if (requestBody != null && !requestBody.isEmpty()) {
            requestBody.forEach((key, value) -> {
                builder.queryParam(key, value);
            });
        }
        String uri = builder.build().toUriString();

        JSONObject res = restClient.get()
                .uri(uri)
                .headers(headers -> {
                    headers.set("Authorization", "Bearer; " + accessToken);
                    headers.set("Resource-Id", "volc.tts_async.default");
                })
                .retrieve()
                .body(JSONObject.class);
        if(ObjUtil.isNull(res)){
            log.error("调用普通版语音合成查询任务接口返回结果为null");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        if (ObjUtil.isNotNull(res.getInt("code"))) {
            log.error("调用普通版语音合成查询任务接口返回结果有错误。错误码：{}, 错误信息：{}", res.getStr("code"), res.getStr("message"));
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        // 成功返回
        return res;
    }

    public JSONObject ttsAsyncWithEmotionQuery(JSONObject requestBody) {
        // 获取配置
        String accessToken = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.VOL_TECH_ACCESS_TOKEN);
        if (StrUtil.isBlank(accessToken)) {
            throw new BizException(BaseResponseCode.FAIL, "配置不完整");
        }

        // 使用UriComponentsBuilder构建查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(TTS_ASYNC_WITH_EMOTION_QUERY_URL);
        if (requestBody != null && !requestBody.isEmpty()) {
            requestBody.forEach((key, value) -> {
                builder.queryParam(key, value);
            });
        }
        String uri = builder.build().toUriString();

        JSONObject res = restClient.get()
                .uri(uri)
                .headers(headers -> {
                    headers.set("Authorization", "Bearer; " + accessToken);
                    headers.set("Resource-Id", "volc.tts_async.default");
                })
                .retrieve()
                .body(JSONObject.class);
        if(ObjUtil.isNull(res)){
            log.error("调用带情感版语音合成查询任务接口返回结果为null");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        if (ObjUtil.isNotNull(res.getInt("code"))) {
            log.error("调用带情感版语音合成查询任务接口返回结果有错误。错误码：{}, 错误信息：{}", res.getStr("code"), res.getStr("message"));
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        // 成功返回
        return res;
    }

}
