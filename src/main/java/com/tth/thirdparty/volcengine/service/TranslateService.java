package com.tth.thirdparty.volcengine.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.BizException;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.framework.invoker.SdkInvoker;
import com.tth.framework.invoker.SdkResultChecker;
import com.tth.framework.response.BaseResponseCode;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.volcengine.enums.VolcengineProperty;
import com.volcengine.ApiClient;
import com.volcengine.ApiException;
import com.volcengine.sign.Credentials;
import com.volcengine.translate20250301.Translate20250301Api;
import com.volcengine.translate20250301.model.TranslateTextRequest;
import com.volcengine.translate20250301.model.TranslateTextResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TranslateService {

    @Resource
    private SystemConfigService systemConfigService;

    public TranslateTextResponse translateText(List<String> textList, String sourceLanguage, String targetLanguage) {
        String accessKeyId = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.ACCESS_KEY_ID);
        String secretAccessKey = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.SECRET_ACCESS_KEY);
        String region = systemConfigService.getConfigValue(SystemConfigProviderEnum.VOLCENGINE, VolcengineProperty.REGION);
        if (StrUtil.isBlank(accessKeyId) || StrUtil.isBlank(secretAccessKey) || StrUtil.isBlank(region)) {
            throw new BizException(BaseResponseCode.FAIL, "翻译配置不完整");
        }

        ApiClient apiClient = new ApiClient()
                .setCredentials(Credentials.getCredentials(accessKeyId, secretAccessKey))
                .setRegion(region);
        Translate20250301Api translate20250301Api = new Translate20250301Api(apiClient);
        TranslateTextRequest request = new TranslateTextRequest();
        request.setTextList(textList);
        request.setSourceLanguage(sourceLanguage);
        request.setTargetLanguage(targetLanguage);

        // 返回的结果一定是成功调用的。
        return SdkInvoker.invoke("火山引擎", "机器翻译", request, () -> {
            try {
                return translate20250301Api.translateText(request);
            } catch (ApiException e) {
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
            }
        }, result -> {
            // 检查响应是否为null
            if (result == null) {
                log.error("翻译接口返回结果为null");
                return SdkResultChecker.ResultCheckOutcome.failure("翻译接口返回结果为null");
            }
            // 检查是否有错误信息
            if (result.getResponseMetadata() != null && result.getResponseMetadata().getError() != null) {
                log.error("翻译接口返回错误: {}", JSONUtil.toJsonStr(result.getResponseMetadata().getError()));
                return SdkResultChecker.ResultCheckOutcome.failure(JSONUtil.toJsonStr(result));
            }
            // 成功
            return SdkResultChecker.ResultCheckOutcome.success();
        });
    }

}
