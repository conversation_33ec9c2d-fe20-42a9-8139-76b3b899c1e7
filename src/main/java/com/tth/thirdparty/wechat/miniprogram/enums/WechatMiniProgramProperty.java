package com.tth.thirdparty.wechat.miniprogram.enums;

import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信小程序配置属性枚举
 * 用于定义微信小程序所需的配置项
 */
@Getter
@AllArgsConstructor
@MarkDictType("微信小程序配置")
@Schema(description = "微信小程序配置")
public enum WechatMiniProgramProperty implements IEnumDesc {

    @Schema(description = "小程序AppID")
    APP_ID("小程序AppID"),

    @Schema(description = "小程序AppSecret")
    APP_SECRET("小程序AppSecret"),
    ;

    private final String desc;
}
