package com.tth.thirdparty.wechat.miniprogram.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.tth.common.constant.RedisKeyConstants;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.wechat.miniprogram.enums.WechatMiniProgramProperty;
import com.tth.common.utils.RedisUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序认证服务类
 * 提供微信小程序登录和接口调用凭据相关功能
 */
@Slf4j
@Component
public class MpAuthService {

    /**
     * 小程序登录凭证校验URL
     */
    private static final String JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";

    /**
     * 获取稳定版接口调用凭据URL
     */
    private static final String STABLE_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/stable_token";

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private RestClient restClient;

    /**
     * 小程序登录凭证校验
     * 通过 wx.login 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程
     *
     * @param jsCode 登录时获取的 code，可通过 wx.login 获取
     * @return 微信小程序会话信息
     */
    public JSONObject jscode2session(String jsCode) {
        if (StrUtil.isBlank(jsCode)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "小程序登录凭证code不能为空");
        }

        // 获取小程序配置
        String appId = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_MINI_PROGRAM, WechatMiniProgramProperty.APP_ID);
        String appSecret = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_MINI_PROGRAM, WechatMiniProgramProperty.APP_SECRET);

        if (StrUtil.isBlank(appId) || StrUtil.isBlank(appSecret)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "微信小程序配置不完整");
        }

        log.info("调用微信小程序登录凭证校验接口, appId: {}", appId);

        // 调用微信小程序API
        String response = restClient.get()
                .uri(JSCODE2SESSION_URL, uriBuilder -> uriBuilder
                        .queryParam("appid", appId)
                        .queryParam("secret", appSecret)
                        .queryParam("js_code", jsCode)
                        .queryParam("grant_type", "authorization_code")
                        .build()
                )
                .retrieve()
                .body(String.class);

        log.info("微信小程序登录凭证校验响应: {}", response);

        if(ObjUtil.isNull(response)) {
            log.error("调用微信小程序登录凭证校验接口响应为空");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        // 将String响应转换为JSONObject
        JSONObject responseJson = JSONUtil.parseObj(response);

        // 检查响应中是否包含错误码
        if(ObjUtil.isNotNull(responseJson.getInt("errcode"))){
            log.error("调用微信小程序登录凭证校验接口响应有错误。错误码：{}, 错误信息：{}", responseJson.getStr("errcode"), responseJson.getStr("errmsg"));
            if(responseJson.getInt("errcode").equals(40029)){
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "code无效");
            }
            if(responseJson.getInt("errcode").equals(45011)){
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, responseJson.getStr("errmsg"));
            }
            if(responseJson.getInt("errcode").equals(40226)){
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "禁止登录");
            }
            if(responseJson.getInt("errcode").equals(-1)){
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "系统繁忙，请稍候再试");
            }
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        return null;
    }

    /**
     * 获取稳定版接口调用凭据
     * 获取小程序全局后台接口调用凭据，有效期最长为7200s
     *
     * @param forceRefresh 是否强制刷新。默认false为普通模式，true为强制刷新模式
     * @return 微信小程序接口调用凭据
     */
    public JSONObject getStableAccessToken(boolean forceRefresh) {
        // 获取小程序配置
        String appId = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_MINI_PROGRAM, WechatMiniProgramProperty.APP_ID);
        String appSecret = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_MINI_PROGRAM, WechatMiniProgramProperty.APP_SECRET);

        if (StrUtil.isBlank(appId) || StrUtil.isBlank(appSecret)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "微信小程序配置不完整");
        }

        log.info("调用微信小程序获取稳定版接口调用凭据, appId: {}, forceRefresh: {}", appId, forceRefresh);

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("grant_type", "client_credential");
        requestBody.put("appid", appId);
        requestBody.put("secret", appSecret);
        requestBody.put("force_refresh", forceRefresh);

        // 调用微信小程序API
        JSONObject response = restClient.post()
                .uri(STABLE_ACCESS_TOKEN_URL)
                .header("Content-Type", "application/json")
                .body(requestBody)
                .retrieve()
                .body(JSONObject.class);

        log.info("微信小程序获取稳定版接口调用凭据响应: {}", response);

        if(ObjUtil.isNull(response)) {
            log.error("微信小程序获取稳定版接口调用凭据响应为空");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        if(ObjUtil.isNotNull(response.getInt("errcode"))){
            log.error("微信小程序获取稳定版接口调用凭据响应有错误。错误码：{}, 错误信息：{}", response.getStr("errcode"), response.getStr("errmsg"));
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR);
        }

        return response;
    }

    /**
     * 获取稳定版接口调用凭据（带缓存）
     * 优先从Redis缓存中获取accessToken，如果缓存不存在或已过期，则调用微信API获取并缓存
     *
     * @return 微信小程序接口调用凭据
     */
    public JSONObject getCachedStableAccessToken() {
        // 获取小程序配置
        String appId = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_MINI_PROGRAM, WechatMiniProgramProperty.APP_ID);

        if (StrUtil.isBlank(appId)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "微信小程序配置不完整");
        }

        // 构建缓存key
        String cacheKey = RedisKeyConstants.buildKey(RedisKeyConstants.ThirdParty.Wechat.MINI_PROGRAM_ACCESS_TOKEN, appId);

        // 先尝试从缓存获取
        JSONObject cachedToken = RedisUtil.get(cacheKey, JSONObject.class);
        if (cachedToken != null) {
            log.debug("从缓存获取微信小程序accessToken成功, appId: {}", appId);
            return cachedToken;
        }

        // 缓存不存在，调用微信API获取
        JSONObject response = getStableAccessToken(false);

        // 将结果缓存到Redis，设置过期时间为7000秒（比微信的7200秒少200秒，确保提前刷新）
        if (response != null && response.containsKey("access_token")) {
            RedisUtil.set(cacheKey, response.toString(), RedisKeyConstants.ThirdParty.Wechat.ACCESS_TOKEN_EXPIRE_SECONDS);
            log.info("微信小程序accessToken已缓存, appId: {}, expireTime: {}秒", appId, RedisKeyConstants.ThirdParty.Wechat.ACCESS_TOKEN_EXPIRE_SECONDS);
        }

        return response;
    }
}
