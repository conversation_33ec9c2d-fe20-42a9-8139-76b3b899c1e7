package com.tth.thirdparty.wechat.miniprogram.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;

import com.tth.thirdparty.wechat.miniprogram.enums.WechatMiniProgramProperty;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序用户服务类
 * 提供微信小程序用户相关功能
 */
@Slf4j
@Component
public class MpUserService {

    /**
     * 获取手机号URL
     */
    private static final String GET_PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private RestClient restClient;

    @Resource
    private MpAuthService mpAuthService;

    /**
     * 获取手机号
     * 通过微信小程序的 code 获取用户手机号信息
     *
     * @param code 手机号获取凭证，通过前端 button 组件 open-type="getPhoneNumber" 返回
     * @return 微信接口原始响应
     */
    public JSONObject getPhoneNumber(String code) {
        if (StrUtil.isBlank(code)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "手机号获取凭证不能为空");
        }

        log.info("调用微信小程序获取手机号接口, code: {}", code);

        // 1. 获取access_token
        JSONObject tokenResponse = mpAuthService.getCachedStableAccessToken();
        String accessToken = tokenResponse.getStr("access_token");

        if (StrUtil.isBlank(accessToken)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "获取微信小程序access_token失败");
        }

        // 2. 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("code", code);

        // 3. 调用微信小程序API
        JSONObject response = restClient.post()
                .uri(GET_PHONE_NUMBER_URL + "?access_token=" + accessToken)
                .header("Content-Type", "application/json")
                .body(requestBody)
                .retrieve()
                .body(JSONObject.class);

        log.info("微信小程序获取手机号响应: {}", response);

        if (ObjUtil.isNull(response)) {
            log.error("微信小程序获取手机号响应为空");
            throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "获取手机号失败");
        }

        // 4. 检查错误码
        Integer errcode = response.getInt("errcode");
        String errmsg = response.getStr("errmsg");

        if (ObjUtil.isNotNull(errcode) && errcode != 0) {
            log.error("微信小程序获取手机号失败。错误码：{}, 错误信息：{}", errcode, errmsg);
            handlePhoneNumberError(errcode, errmsg);
        }

        return response;
    }

    /**
     * 获取纯手机号（去除区号）
     * 便捷方法，直接返回纯手机号字符串
     *
     * @param code 手机号获取凭证
     * @return 纯手机号（如：13800138000）
     */
    public String getPurePhoneNumber(String code) {
        JSONObject response = getPhoneNumber(code);
        JSONObject phoneInfo = response.getJSONObject("phone_info");

        if (ObjUtil.isNotNull(phoneInfo)) {
            return phoneInfo.getStr("purePhoneNumber");
        }

        return null;
    }

    /**
     * 获取完整手机号（包含区号）
     * 便捷方法，直接返回完整手机号字符串
     *
     * @param code 手机号获取凭证
     * @return 完整手机号（如：+8613800138000）
     */
    public String getFullPhoneNumber(String code) {
        JSONObject response = getPhoneNumber(code);
        JSONObject phoneInfo = response.getJSONObject("phone_info");

        if (ObjUtil.isNotNull(phoneInfo)) {
            return phoneInfo.getStr("phoneNumber");
        }

        return null;
    }

    /**
     * 处理获取手机号的错误
     */
    private void handlePhoneNumberError(Integer errcode, String errmsg) {
        switch (errcode) {
            case 40001:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "access_token无效或已过期");
            case 40013:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "AppID无效");
            case 40029:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "code无效");
            case 45011:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "API调用太频繁，请稍候再试");
            case 40226:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "用户拒绝授权");
            case -1:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR, "系统繁忙，请稍候再试");
            default:
                throw new ThirdPartyException(ThirdPartyExceptionCode.THIRD_PARTY_ERROR,
                    StrUtil.isNotBlank(errmsg) ? errmsg : "获取手机号失败");
        }
    }

    /**
     * 验证手机号水印
     * 验证返回的手机号数据是否来自当前小程序
     *
     * @param phoneResponse 微信获取手机号的完整响应
     * @return 是否验证通过
     */
    public boolean validatePhoneWatermark(JSONObject phoneResponse) {
        JSONObject phoneInfo = phoneResponse.getJSONObject("phone_info");
        if (ObjUtil.isNull(phoneInfo)) {
            return false;
        }

        JSONObject watermark = phoneInfo.getJSONObject("watermark");
        if (ObjUtil.isNull(watermark)) {
            return false;
        }

        // 获取当前小程序的AppID
        String currentAppId = systemConfigService.getConfigValue(
            SystemConfigProviderEnum.WECHAT_MINI_PROGRAM,
            WechatMiniProgramProperty.APP_ID
        );

        if (StrUtil.isBlank(currentAppId)) {
            log.warn("未配置微信小程序AppID，无法验证水印");
            return false;
        }

        // 验证AppID是否匹配
        String watermarkAppId = watermark.getStr("appid");
        boolean appIdMatch = currentAppId.equals(watermarkAppId);

        // 验证时间戳是否在合理范围内（5分钟内）
        long currentTime = System.currentTimeMillis() / 1000;
        Long watermarkTime = watermark.getLong("timestamp");
        boolean timeValid = watermarkTime != null && Math.abs(currentTime - watermarkTime) <= 300; // 5分钟

        log.debug("手机号水印验证: appIdMatch={}, timeValid={}, currentAppId={}, watermarkAppId={}",
            appIdMatch, timeValid, currentAppId, watermarkAppId);

        return appIdMatch && timeValid;
    }
}
