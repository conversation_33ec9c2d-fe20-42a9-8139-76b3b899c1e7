package com.tth.thirdparty.wechat.open.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信访问令牌DTO
 * 用于封装微信授权后返回的访问令牌信息
 */
@Data
@Schema(description = "微信访问令牌")
public class WechatAccessTokenDTO {

    @Schema(description = "接口调用凭证")
    private String accessToken;

    @Schema(description = "access_token接口调用凭证超时时间，单位（秒）")
    private Integer expiresIn;

    @Schema(description = "用户刷新access_token")
    private String refreshToken;

    @Schema(description = "授权用户唯一标识")
    private String openid;

    @Schema(description = "用户授权的作用域，使用逗号（,）分隔")
    private String scope;

    @Schema(description = "当且仅当该移动应用已获得该用户的userinfo授权时，才会出现该字段")
    private String unionid;
}
