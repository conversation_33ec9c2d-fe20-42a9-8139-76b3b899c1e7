package com.tth.thirdparty.wechat.open.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信用户信息DTO
 * 用于封装微信用户的基本信息
 */
@Data
@Schema(description = "微信用户信息")
public class WechatUserInfoDTO {

    @Schema(description = "普通用户的标识，对当前开发者账号唯一")
    private String openid;

    @Schema(description = "普通用户昵称")
    private String nickname;

    @Schema(description = "普通用户性别，1为男性，2为女性")
    private Integer sex;

    @Schema(description = "普通用户个人资料填写的省份")
    private String province;

    @Schema(description = "普通用户个人资料填写的城市")
    private String city;

    @Schema(description = "国家，如中国为CN")
    private String country;

    @Schema(description = "用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空")
    private String headimgurl;

    @Schema(description = "用户特权信息，json数组，如微信沃卡用户为（chinaunicom）")
    private String[] privilege;

    @Schema(description = "用户统一标识。针对一个微信开放平台账号下的应用，同一用户的unionid是唯一的。")
    private String unionid;
}
