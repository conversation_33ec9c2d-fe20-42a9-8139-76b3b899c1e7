package com.tth.thirdparty.wechat.open.enums;

import com.tth.framework.annotation.MarkDictType;
import com.tth.framework.interfaces.IEnumDesc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信开放平台配置属性枚举
 * 用于定义微信开放平台所需的配置项
 */
@Getter
@AllArgsConstructor
@MarkDictType("微信开放平台配置")
@Schema(description = "微信开放平台配置")
public enum WechatOpenProperty implements IEnumDesc {

    @Schema(description = "AppID")
    APP_ID("AppID"),

    @Schema(description = "AppSecret")
    APP_SECRET("AppSecret"),

    @Schema(description = "授权回调域")
    REDIRECT_URI("授权回调域"),
    ;

    private final String desc;
}
