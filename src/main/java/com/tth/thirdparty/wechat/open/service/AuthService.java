package com.tth.thirdparty.wechat.open.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tth.framework.exception.code.ThirdPartyExceptionCode;
import com.tth.framework.exception.core.ThirdPartyException;
import com.tth.modules.system.enums.SystemConfigProviderEnum;
import com.tth.modules.system.service.SystemConfigService;
import com.tth.thirdparty.wechat.open.dto.WechatAccessTokenDTO;
import com.tth.thirdparty.wechat.open.dto.WechatUserInfoDTO;
import com.tth.thirdparty.wechat.open.enums.WechatOpenProperty;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信开放平台服务类
 * 提供微信开放平台相关接口的调用功能
 */
@Slf4j
//@Component
public class AuthService {

    /**
     * 微信开放平台API基础URL
     */
    private static final String WECHAT_API_BASE_URL = "https://api.weixin.qq.com";

    /**
     * 获取访问令牌的URL
     */
    private static final String ACCESS_TOKEN_URL = WECHAT_API_BASE_URL + "/sns/oauth2/access_token";

    /**
     * 刷新访问令牌的URL
     */
    private static final String REFRESH_TOKEN_URL = WECHAT_API_BASE_URL + "/sns/oauth2/refresh_token";

    /**
     * 获取用户信息的URL
     */
    private static final String USER_INFO_URL = WECHAT_API_BASE_URL + "/sns/userinfo";

    /**
     * 检查访问令牌有效性的URL
     */
    private static final String CHECK_TOKEN_URL = WECHAT_API_BASE_URL + "/sns/auth";

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private RestClient restClient;

    /**
     * 通过code获取访问令牌
     *
     * @param code 授权临时票据
     * @return 微信访问令牌DTO
     */
    public WechatAccessTokenDTO getAccessToken(String code) {
        if (StrUtil.isBlank(code)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "授权临时票据code不能为空");
        }

        // 获取微信开放平台配置
        String appId = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_PAY, WechatOpenProperty.APP_ID);
        String appSecret = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_PAY, WechatOpenProperty.APP_SECRET);

        if (StrUtil.isBlank(appId) || StrUtil.isBlank(appSecret)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "微信开放平台配置不完整");
        }

        // 直接调用微信API获取访问令牌
        String response = restClient.get()
                .uri(ACCESS_TOKEN_URL, uriBuilder -> uriBuilder
                        .queryParam("appid", appId)
                        .queryParam("secret", appSecret)
                        .queryParam("code", code)
                        .queryParam("grant_type", "authorization_code")
                        .build())
                .retrieve()
                .body(String.class);

        return parseWechatResponse(response, WechatAccessTokenDTO.class);
    }

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 微信访问令牌DTO
     */
    public WechatAccessTokenDTO refreshAccessToken(String refreshToken) {
        if (StrUtil.isBlank(refreshToken)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "刷新令牌不能为空");
        }

        // 获取微信开放平台配置
        String appId = systemConfigService.getConfigValue(SystemConfigProviderEnum.WECHAT_PAY, WechatOpenProperty.APP_ID);

        if (StrUtil.isBlank(appId)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "微信开放平台配置不完整");
        }

        // 直接调用微信API刷新访问令牌
        String response = restClient.get()
                .uri(REFRESH_TOKEN_URL, uriBuilder -> uriBuilder
                        .queryParam("appid", appId)
                        .queryParam("grant_type", "refresh_token")
                        .queryParam("refresh_token", refreshToken)
                        .build())
                .retrieve()
                .body(String.class);

        return parseWechatResponse(response, WechatAccessTokenDTO.class);
    }

    /**
     * 获取用户信息
     *
     * @param accessToken 访问令牌
     * @param openid 用户的OpenID
     * @return 微信用户信息DTO
     */
    public WechatUserInfoDTO getUserInfo(String accessToken, String openid) {
        if (StrUtil.isBlank(accessToken) || StrUtil.isBlank(openid)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "访问令牌或OpenID不能为空");
        }

        // 直接调用微信API获取用户信息
        String response = restClient.get()
                .uri(USER_INFO_URL, uriBuilder -> uriBuilder
                        .queryParam("access_token", accessToken)
                        .queryParam("openid", openid)
                        .queryParam("lang", "zh_CN")
                        .build())
                .retrieve()
                .body(String.class);

        return parseWechatResponse(response, WechatUserInfoDTO.class);
    }

    /**
     * 检查访问令牌是否有效
     *
     * @param accessToken 访问令牌
     * @param openid 用户的OpenID
     * @return 是否有效
     */
    public boolean checkAccessToken(String accessToken, String openid) {
        if (StrUtil.isBlank(accessToken) || StrUtil.isBlank(openid)) {
            return false;
        }

        try {
            // 直接调用微信API检查访问令牌
            String response = restClient.get()
                    .uri(CHECK_TOKEN_URL, uriBuilder -> uriBuilder
                            .queryParam("access_token", accessToken)
                            .queryParam("openid", openid)
                            .build())
                    .retrieve()
                    .body(String.class);

            Map<String, Object> result = JSONUtil.parseObj(response);
            return !result.containsKey("errcode") || "0".equals(result.get("errcode").toString());
        } catch (Exception e) {
            log.warn("检查微信访问令牌有效性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析微信API响应
     *
     * @param response 响应字符串
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 解析后的对象
     */
    private <T> T parseWechatResponse(String response, Class<T> clazz) {
        if (StrUtil.isBlank(response)) {
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED, "微信API响应为空");
        }

        // 检查是否包含错误码
        if (response.contains("errcode") && !response.contains("\"errcode\":0")) {
            Map<String, Object> errorMap = JSONUtil.parseObj(response);
            String errcode = errorMap.getOrDefault("errcode", "").toString();
            String errmsg = errorMap.getOrDefault("errmsg", "未知错误").toString();
            throw new ThirdPartyException(ThirdPartyExceptionCode.SOCIAL_AUTH_FAILED,
                    String.format("微信API调用失败: errcode=%s, errmsg=%s", errcode, errmsg));
        }

        // 将下划线命名转换为驼峰命名
        Map<String, Object> map = JSONUtil.parseObj(response);
        Map<String, Object> camelCaseMap = new HashMap<>();
        map.forEach((key, value) -> {
            String camelKey = StrUtil.toCamelCase(key);
            camelCaseMap.put(camelKey, value);
        });

        return JSONUtil.toBean(JSONUtil.toJsonStr(camelCaseMap), clazz);
    }


}
