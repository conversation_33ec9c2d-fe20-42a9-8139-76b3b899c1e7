server:
  port: 8088

spring:
  main:
    banner-mode: off
  servlet:
    multipart: # 文件上传配置
      max-file-size: 500MB  # 单个文件上传的最大大小
      max-request-size: 500MB  # 单个请求上传的最大总大小
  datasource:
    # 数据库连接 URL，指向 MySQL 数据库
    url: jdbc:mysql://**************:3306/tth?useSSL=false&serverTimezone=UTC&characterEncoding=UTF-8&allowPublicKeyRetrieval=true
    username: tth  # 数据库用户名
    password: 7aCS5CZhjbkpAfy2   # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver  # 数据库驱动类名
    hikari: # HikariCP 连接池配置
      maximum-pool-size: 10  # 连接池中最大连接数
      minimum-idle: 5  # 连接池中最小空闲连接数
      idle-timeout: 600000  # 连接空闲超时时间，单位毫秒（10分钟）
      connection-timeout: 30000  # 连接超时时间，单位毫秒（30秒）
      max-lifetime: 1800000  # 连接的最大生命周期，单位毫秒（30分钟）
      keepalive-time: 30000  # 心跳检测时间，单位毫秒（30秒）
      connection-test-query: SELECT 1  # 连接测试查询
      validation-timeout: 5000  # 连接验证超时时间，单位毫秒（5秒）
      leak-detection-threshold: 60000  # 连接泄漏检测阈值，单位毫秒（60秒）
  data:
    redis:
      database: 8
      port: 6379
      host: 127.0.0.1
      password: 123456
      lettuce:
        pool:
          enabled: true           # 启用连接池
          max-active: 8          # 最大活跃连接数
          max-idle: 8            # 最大空闲连接数
          min-idle: 0            # 最小空闲连接数
          max-wait: -1ms         # 最大等待时间
          time-between-eviction-runs: 1h  # 空闲连接检查间隔

# 日志配置
logging:
  level:
    root: info
    com.tth: debug
  file:
    name: d://CodeProjectsLogs/tth/tth-server
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30