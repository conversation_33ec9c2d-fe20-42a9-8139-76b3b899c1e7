spring:
  application:
    name: tth-server
  profiles:
    active: local

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 下划线转驼峰
    map-underscore-to-camel-case: true
    # 开启自动映射枚举
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 关闭MyBatis-Plus Banner
    banner: false
    db-config:
      # 主键类型
      id-type: ASSIGN_ID
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期，单位s - AccessToken的有效期，设置为较短的时间，如30分钟
  timeout: 21600
  # token临时有效期 (指定时间内无操作就过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: random-64
  # 是否输出操作日志
  is-log: false
  # 是否在请求头中获取token
  is-read-header: true
  # 是否在cookie中获取token
  is-read-cookie: false
  # 是否在请求体中获取token
  is-read-body: false
  # 是否在响应头中返回token
  is-write-header: true
  # 是否开启自动续签（如果为true，则每次访问都会刷新token有效期）
  auto-renew: false
  log-level: debug
  # 关闭Sa-Token Banner
  is-print: false

# swagger-ui custom path http://localhost:8080/doc.html
# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'api'
      paths-to-match: '/**'
      packages-to-scan: com.tth # 扫描接口所在的包

# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn