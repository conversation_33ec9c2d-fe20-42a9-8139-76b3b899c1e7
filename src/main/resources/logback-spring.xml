<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入Spring Boot默认的基础配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- 定义变量 -->
    <!-- 从Spring属性中读取日志文件路径，注意这里的logging.file.name是没有.log后缀的路径 -->
    <springProperty scope="context" name="LOG_NAME" source="logging.file.name" defaultValue="logs"/>

    <!-- 彩色日志格式（控制台） -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wex"/>

    <!-- 文件日志格式 -->
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 所有日志文件 -->
    <appender name="FILE_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_NAME}.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 按天滚动，保留30天的历史记录，单个文件最大10MB -->
            <fileNamePattern>${LOG_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 错误日志文件，单独记录错误 -->
    <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_NAME}-error.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_NAME}-error-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!-- 只记录ERROR级别日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- API请求日志文件 -->
    <appender name="FILE_API" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_NAME}-api.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_NAME}-api-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- SQL日志文件 -->
    <appender name="FILE_SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_NAME}-sql.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_NAME}-sql-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 异步输出 -->
    <appender name="ASYNC_ALL" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="FILE_ALL"/>
    </appender>

    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE_ERROR"/>
    </appender>

    <appender name="ASYNC_API" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="FILE_API"/>
    </appender>

    <appender name="ASYNC_SQL" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="FILE_SQL"/>
    </appender>

    <!-- 自定义日志级别 -->
    <!-- API日志 -->
    <logger name="com.tth.framework.aspect.WebLogAspect" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_API"/>
    </logger>

    <logger name="com.tth.framework.aspect.ApiLogAspect" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_API"/>
    </logger>

    <!-- SQL日志 -->
    <logger name="com.tth.modules.*.mapper" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_SQL"/>
    </logger>

    <!-- MyBatis SQL日志 -->
    <logger name="org.apache.ibatis" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_SQL"/>
    </logger>

    <!-- 项目日志 -->
    <logger name="com.tth" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_ALL"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </logger>

    <!-- 第三方框架日志级别 -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.mybatis" level="INFO"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="org.apache.http" level="INFO"/>
    <logger name="cn.dev33.satoken" level="INFO"/>
    <logger name="io.lettuce" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="springfox" level="INFO"/>
    <logger name="com.github.xiaoymin" level="INFO"/>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_ALL"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </root>

    <!-- 不同环境的配置 -->
    <springProfile name="dev,local">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_ALL"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
        <!-- 开发环境下SQL日志级别调整为DEBUG -->
        <logger name="com.tth.modules.*.mapper" level="DEBUG"/>
        <logger name="org.apache.ibatis" level="DEBUG"/>
    </springProfile>

    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_ALL"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <!-- 生产环境下关闭控制台日志 -->
        <root level="INFO">
            <appender-ref ref="ASYNC_ALL"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
        <!-- 生产环境下SQL日志级别调整为INFO -->
        <logger name="com.tth.modules.*.mapper" level="INFO"/>
        <logger name="org.apache.ibatis" level="INFO"/>
    </springProfile>
</configuration>
