<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.clothes.mapper.ClothesMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.clothes.entity.Clothes">
        <result column="id"                        property="id" />
        <result column="createdBy"                 property="createdBy" />
        <result column="createdTime"               property="createdTime" />
        <result column="modifiedBy"                property="modifiedBy" />
        <result column="modifiedTime"              property="modifiedTime" />
        <result column="deletedBy"                 property="deletedBy" />
        <result column="deletedTime"               property="deletedTime" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="userId"                    property="userId" />
        <result column="wardrobeId"                property="wardrobeId" />
        <result column="wardrobeAreaId"            property="wardrobeAreaId" />
        <result column="imageId"                   property="imageId" />
        <result column="clothesName"               property="clothesName" />
        <result column="tags"                      property="tags" />
        <result column="purchasePrice"             property="purchasePrice" />
        <result column="purchaseTime"              property="purchaseTime" />
        <result column="purchaseLocation"          property="purchaseLocation" />
        <result column="clothesType"               property="clothesType" />
        <result column="story"                     property="story" />
        <result column="season"                    property="season" />
        <result column="color"                     property="color" />
        <result column="onSofa"                    property="onSofa" />
        <result column="sofaStartTime"             property="sofaStartTime" />
        <result column="sort"                      property="sort" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除（0-否，1-是） -->

            ,t1.user_id                   AS userId                      <!-- 用户ID（关联tth_user_base） -->
            ,t1.wardrobe_id               AS wardrobeId                  <!-- 衣柜ID（关联tth_wardrobe） -->
            ,t1.wardrobe_area_id          AS wardrobeAreaId              <!-- 衣柜区域ID（关联tth_wardrobe_area） -->
            ,t1.image_id                  AS imageId                     <!-- 衣服图片ID（关联tth_oss_file） -->
            ,t1.clothes_name              AS clothesName                 <!-- 衣服名称 -->
            ,t1.tags                      AS tags                        <!-- 标签（JSON格式存储多个标签） -->
            ,t1.purchase_price            AS purchasePrice               <!-- 购买价格 -->
            ,t1.purchase_time             AS purchaseTime                <!-- 购买时间 -->
            ,t1.purchase_location         AS purchaseLocation            <!-- 购买地点 -->
            ,t1.clothes_type              AS clothesType                 <!-- 衣服类型（1-裙子，2-衬衫，3-T恤，4-袜子，5-裤子，6-外套，7-毛衣，8-牛仔裤，9-短裤，10-内衣，11-睡衣，12-运动服，13-正装，14-鞋子，15-帽子，16-围巾，17-手套，18-腰带，99-其他） -->
            ,t1.story                     AS story                       <!-- 衣服的故事 -->
            ,t1.season                    AS season                      <!-- 适合的季节（1-春季，2-夏季，3-秋季，4-冬季，5-四季） -->
            ,t1.color                     AS color                       <!-- 颜色（中文显示） -->
            ,t1.on_sofa                AS onSofa                      <!-- 是否在沙发（0-否，1-是） -->
            ,t1.sofa_start_time           AS sofaStartTime               <!-- 开始在沙发的时间 -->
            ,t1.sort                      AS sort                        <!-- 排序 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_clothes t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.clothes.entity.Clothes">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.clothes.entity.Clothes">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.clothes.entity.Clothes">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.clothes.entity.Clothes">
        DELETE FROM tth_clothes
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.clothes.entity.Clothes">
        UPDATE tth_clothes
        <set>
            <if test="userId                    != null or (canNullFields != null and canNullFields.contains('userId'))">
                user_id              = #{userId},
            </if>
            <if test="wardrobeId                != null or (canNullFields != null and canNullFields.contains('wardrobeId'))">
                wardrobe_id          = #{wardrobeId},
            </if>
            <if test="wardrobeAreaId            != null or (canNullFields != null and canNullFields.contains('wardrobeAreaId'))">
                wardrobe_area_id     = #{wardrobeAreaId},
            </if>
            <if test="imageId                   != null or (canNullFields != null and canNullFields.contains('imageId'))">
                image_id             = #{imageId},
            </if>
            <if test="clothesName               != null or (canNullFields != null and canNullFields.contains('clothesName'))">
                clothes_name         = #{clothesName},
            </if>
            <if test="tags                      != null or (canNullFields != null and canNullFields.contains('tags'))">
                tags                 = #{tags},
            </if>
            <if test="purchasePrice             != null or (canNullFields != null and canNullFields.contains('purchasePrice'))">
                purchase_price       = #{purchasePrice},
            </if>
            <if test="purchaseTime              != null or (canNullFields != null and canNullFields.contains('purchaseTime'))">
                purchase_time        = #{purchaseTime},
            </if>
            <if test="purchaseLocation          != null or (canNullFields != null and canNullFields.contains('purchaseLocation'))">
                purchase_location    = #{purchaseLocation},
            </if>
            <if test="clothesType               != null or (canNullFields != null and canNullFields.contains('clothesType'))">
                clothes_type         = #{clothesType},
            </if>
            <if test="story                     != null or (canNullFields != null and canNullFields.contains('story'))">
                story                = #{story},
            </if>
            <if test="season                    != null or (canNullFields != null and canNullFields.contains('season'))">
                season               = #{season},
            </if>
            <if test="color                     != null or (canNullFields != null and canNullFields.contains('color'))">
                color                = #{color},
            </if>
            <if test="onSofa                    != null or (canNullFields != null and canNullFields.contains('onSofa'))">
                on_sofa           = #{onSofa},
            </if>
            <if test="sofaStartTime             != null or (canNullFields != null and canNullFields.contains('sofaStartTime'))">
                sofa_start_time      = #{sofaStartTime},
            </if>
            <if test="sort                      != null or (canNullFields != null and canNullFields.contains('sort'))">
                sort                 = #{sort},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_clothes
            <set>
                <if test="userId          != null">
                    user_id         = #{userId},
                </if>
                <if test="wardrobeId      != null">
                    wardrobe_id     = #{wardrobeId},
                </if>
                <if test="wardrobeAreaId  != null">
                    wardrobe_area_id = #{wardrobeAreaId},
                </if>
                <if test="imageId         != null">
                    image_id        = #{imageId},
                </if>
                <if test="clothesName     != null">
                    clothes_name    = #{clothesName},
                </if>
                <if test="tags            != null">
                    tags            = #{tags},
                </if>
                <if test="purchasePrice   != null">
                    purchase_price  = #{purchasePrice},
                </if>
                <if test="purchaseTime    != null">
                    purchase_time   = #{purchaseTime},
                </if>
                <if test="purchaseLocation != null">
                    purchase_location = #{purchaseLocation},
                </if>
                <if test="clothesType     != null">
                    clothes_type    = #{clothesType},
                </if>
                <if test="story           != null">
                    story           = #{story},
                </if>
                <if test="season          != null">
                    season          = #{season},
                </if>
                <if test="color           != null">
                    color           = #{color},
                </if>
                <if test="onSofa          != null">
                    on_sofa      = #{onSofa},
                </if>
                <if test="sofaStartTime   != null">
                    sofa_start_time = #{sofaStartTime},
                </if>
                <if test="sort            != null">
                    sort            = #{sort},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
