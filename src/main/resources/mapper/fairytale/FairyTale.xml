<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.fairytale.mapper.FairyTaleMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.fairytale.entity.FairyTale">
        <result column="id"                                  property="id" />
        <result column="createdBy"                          property="createdBy" />
        <result column="createdTime"                        property="createdTime" />
        <result column="modifiedBy"                         property="modifiedBy" />
        <result column="modifiedTime"                       property="modifiedTime" />
        <result column="deletedBy"                          property="deletedBy" />
        <result column="deletedTime"                        property="deletedTime" />
        <result column="version"                             property="version" />
        <result column="remark"                              property="remark" />
        <result column="deleted"                             property="deleted" />

        <result column="storyNumber"                        property="storyNumber" />
        <result column="chineseTitle"                       property="chineseTitle" />
        <result column="chineseContent"                     property="chineseContent" />
        <result column="englishTitle"                       property="englishTitle" />
        <result column="englishContent"                     property="englishContent" />
        <result column="publishStatus"                      property="publishStatus" />
        <result column="needEnglishVersion"                property="needEnglishVersion" />
        <result column="needBilingualVersion"              property="needBilingualVersion" />
        <result column="apiVersion"                         property="apiVersion" />
        <result column="chineseTitleAudioId"              property="chineseTitleAudioId" />
        <result column="chineseContentAudioId"            property="chineseContentAudioId" />
        <result column="chineseTitleContentAudioId"      property="chineseTitleContentAudioId" />
        <result column="englishTitleAudioId"              property="englishTitleAudioId" />
        <result column="englishContentAudioId"            property="englishContentAudioId" />
        <result column="englishTitleContentAudioId"      property="englishTitleContentAudioId" />
        <result column="translationStatus"                  property="translationStatus" />
        <result column="translationStartTime"              property="translationStartTime" />
        <result column="translationCompleteTime"           property="translationCompleteTime" />
        <result column="chineseTitleTtsStatus"            property="chineseTitleTtsStatus" />
        <result column="chineseTitleTtsStartTime"        property="chineseTitleTtsStartTime" />
        <result column="chineseTitleTtsCompleteTime"     property="chineseTitleTtsCompleteTime" />
        <result column="chineseTitleTaskId"               property="chineseTitleTaskId" />
        <result column="chineseTitleRequestId"            property="chineseTitleRequestId" />
        <result column="chineseContentTtsStatus"          property="chineseContentTtsStatus" />
        <result column="chineseContentTtsStartTime"      property="chineseContentTtsStartTime" />
        <result column="chineseContentTtsCompleteTime"   property="chineseContentTtsCompleteTime" />
        <result column="chineseContentTaskId"             property="chineseContentTaskId" />
        <result column="chineseContentRequestId"          property="chineseContentRequestId" />
        <result column="chineseTitleContentTtsStatus"    property="chineseTitleContentTtsStatus" />
        <result column="chineseTitleContentTtsStartTime" property="chineseTitleContentTtsStartTime" />
        <result column="chineseTitleContentTtsCompleteTime" property="chineseTitleContentTtsCompleteTime" />
        <result column="chineseTitleContentTaskId"       property="chineseTitleContentTaskId" />
        <result column="chineseTitleContentRequestId"    property="chineseTitleContentRequestId" />
        <result column="englishTitleTtsStatus"            property="englishTitleTtsStatus" />
        <result column="englishTitleTtsStartTime"        property="englishTitleTtsStartTime" />
        <result column="englishTitleTtsCompleteTime"     property="englishTitleTtsCompleteTime" />
        <result column="englishTitleTaskId"               property="englishTitleTaskId" />
        <result column="englishTitleRequestId"            property="englishTitleRequestId" />
        <result column="englishContentTtsStatus"          property="englishContentTtsStatus" />
        <result column="englishContentTtsStartTime"      property="englishContentTtsStartTime" />
        <result column="englishContentTtsCompleteTime"   property="englishContentTtsCompleteTime" />
        <result column="englishContentTaskId"             property="englishContentTaskId" />
        <result column="englishContentRequestId"          property="englishContentRequestId" />
        <result column="englishTitleContentTtsStatus"    property="englishTitleContentTtsStatus" />
        <result column="englishTitleContentTtsStartTime" property="englishTitleContentTtsStartTime" />
        <result column="englishTitleContentTtsCompleteTime" property="englishTitleContentTtsCompleteTime" />
        <result column="englishTitleContentTaskId"       property="englishTitleContentTaskId" />
        <result column="englishTitleContentRequestId"    property="englishTitleContentRequestId" />
        <result column="chineseTitleTtsResult"            property="chineseTitleTtsResult" />
        <result column="chineseContentTtsResult"          property="chineseContentTtsResult" />
        <result column="chineseTitleContentTtsResult"    property="chineseTitleContentTtsResult" />
        <result column="englishTitleTtsResult"            property="englishTitleTtsResult" />
        <result column="englishContentTtsResult"          property="englishContentTtsResult" />
        <result column="englishTitleContentTtsResult"    property="englishTitleContentTtsResult" />
        <result column="chineseVoiceType"                  property="chineseVoiceType" />
        <result column="englishVoiceType"                  property="englishVoiceType" />
        <result column="audioFormat"                        property="audioFormat" />
        <result column="sampleRate"                         property="sampleRate" />
        <result column="chineseVoiceStyle"                 property="chineseVoiceStyle" />
        <result column="englishVoiceStyle"                 property="englishVoiceStyle" />
        <result column="chineseVolume"                      property="chineseVolume" />
        <result column="chineseSpeed"                       property="chineseSpeed" />
        <result column="chinesePitch"                       property="chinesePitch" />
        <result column="chineseSentenceInterval"           property="chineseSentenceInterval" />
        <result column="englishVolume"                      property="englishVolume" />
        <result column="englishSpeed"                       property="englishSpeed" />
        <result column="englishPitch"                       property="englishPitch" />
        <result column="englishSentenceInterval"           property="englishSentenceInterval" />
        <result column="chineseFavoriteCount"              property="chineseFavoriteCount" />
        <result column="englishFavoriteCount"              property="englishFavoriteCount" />
        <result column="bilingualFavoriteCount"            property="bilingualFavoriteCount" />
        <result column="appId"                              property="appId" />
        <result column="chineseScreenshots"                property="chineseScreenshots" />
        <result column="englishScreenshots"                property="englishScreenshots" />
        <result column="lastSynthesizeTime"                property="lastSynthesizeTime" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除 -->

            ,t1.story_number              AS storyNumber                 <!-- 故事编号（格式：YYYYMMDD001） -->
            ,t1.chinese_title             AS chineseTitle                <!-- 中文童话名称 -->
            ,t1.chinese_content           AS chineseContent              <!-- 中文童话内容 -->
            ,t1.english_title             AS englishTitle                <!-- 英文童话名称 -->
            ,t1.english_content           AS englishContent              <!-- 英文童话内容 -->
            ,t1.publish_status            AS publishStatus               <!-- 发布状态（0-未发布，1-已发布） -->
            ,t1.need_english_version      AS needEnglishVersion          <!-- 是否需要英文版 -->
            ,t1.need_bilingual_version    AS needBilingualVersion        <!-- 是否需要双语版 -->
            ,t1.api_version               AS apiVersion                  <!-- 火山引擎精品长文本语音合成接口版本（1-普通版，2-情感预测版） -->
            ,t1.chinese_title_audio_id    AS chineseTitleAudioId         <!-- 中文标题音频文件ID（关联tth_oss_file） -->
            ,t1.chinese_content_audio_id  AS chineseContentAudioId       <!-- 中文内容音频文件ID（关联tth_oss_file） -->
            ,t1.chinese_title_content_audio_id AS chineseTitleContentAudioId   <!-- 中文标题+内容合成音频文件ID（关联tth_oss_file） -->
            ,t1.english_title_audio_id    AS englishTitleAudioId         <!-- 英文标题音频文件ID（关联tth_oss_file） -->
            ,t1.english_content_audio_id  AS englishContentAudioId       <!-- 英文内容音频文件ID（关联tth_oss_file） -->
            ,t1.english_title_content_audio_id AS englishTitleContentAudioId   <!-- 英文标题+内容合成音频文件ID（关联tth_oss_file） -->
            ,t1.translation_status        AS translationStatus           <!-- 英文翻译状态（0-待翻译，1-翻译中，2-翻译成功，3-翻译失败） -->
            ,t1.translation_start_time    AS translationStartTime        <!-- 翻译开始时间 -->
            ,t1.translation_complete_time AS translationCompleteTime     <!-- 翻译完成时间 -->
            ,t1.chinese_title_tts_status  AS chineseTitleTtsStatus       <!-- 中文标题语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败） -->
            ,t1.chinese_title_tts_start_time AS chineseTitleTtsStartTime    <!-- 中文标题语音合成开始时间 -->
            ,t1.chinese_title_tts_complete_time AS chineseTitleTtsCompleteTime   <!-- 中文标题语音合成完成时间 -->
            ,t1.chinese_title_task_id     AS chineseTitleTaskId          <!-- 中文标题语音合成任务ID -->
            ,t1.chinese_title_request_id  AS chineseTitleRequestId       <!-- 中文标题语音合成请求ID -->
            ,t1.chinese_content_tts_status AS chineseContentTtsStatus     <!-- 中文内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败） -->
            ,t1.chinese_content_tts_start_time AS chineseContentTtsStartTime   <!-- 中文内容语音合成开始时间 -->
            ,t1.chinese_content_tts_complete_time AS chineseContentTtsCompleteTime   <!-- 中文内容语音合成完成时间 -->
            ,t1.chinese_content_task_id   AS chineseContentTaskId        <!-- 中文内容语音合成任务ID -->
            ,t1.chinese_content_request_id AS chineseContentRequestId     <!-- 中文内容语音合成请求ID -->
            ,t1.chinese_title_content_tts_status AS chineseTitleContentTtsStatus   <!-- 中文标题+内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败） -->
            ,t1.chinese_title_content_tts_start_time AS chineseTitleContentTtsStartTime   <!-- 中文标题+内容语音合成开始时间 -->
            ,t1.chinese_title_content_tts_complete_time AS chineseTitleContentTtsCompleteTime   <!-- 中文标题+内容语音合成完成时间 -->
            ,t1.chinese_title_content_task_id AS chineseTitleContentTaskId   <!-- 中文标题+内容语音合成任务ID -->
            ,t1.chinese_title_content_request_id AS chineseTitleContentRequestId   <!-- 中文标题+内容语音合成请求ID -->
            ,t1.english_title_tts_status  AS englishTitleTtsStatus       <!-- 英文标题语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败） -->
            ,t1.english_title_tts_start_time AS englishTitleTtsStartTime    <!-- 英文标题语音合成开始时间 -->
            ,t1.english_title_tts_complete_time AS englishTitleTtsCompleteTime   <!-- 英文标题语音合成完成时间 -->
            ,t1.english_title_task_id     AS englishTitleTaskId          <!-- 英文标题语音合成任务ID -->
            ,t1.english_title_request_id  AS englishTitleRequestId       <!-- 英文标题语音合成请求ID -->
            ,t1.english_content_tts_status AS englishContentTtsStatus     <!-- 英文内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败） -->
            ,t1.english_content_tts_start_time AS englishContentTtsStartTime   <!-- 英文内容语音合成开始时间 -->
            ,t1.english_content_tts_complete_time AS englishContentTtsCompleteTime   <!-- 英文内容语音合成完成时间 -->
            ,t1.english_content_task_id   AS englishContentTaskId        <!-- 英文内容语音合成任务ID -->
            ,t1.english_content_request_id AS englishContentRequestId     <!-- 英文内容语音合成请求ID -->
            ,t1.english_title_content_tts_status AS englishTitleContentTtsStatus   <!-- 英文标题+内容语音合成状态（0-待合成，1-合成中，2-上传中，3-合成成功，4-合成失败） -->
            ,t1.english_title_content_tts_start_time AS englishTitleContentTtsStartTime   <!-- 英文标题+内容语音合成开始时间 -->
            ,t1.english_title_content_tts_complete_time AS englishTitleContentTtsCompleteTime   <!-- 英文标题+内容语音合成完成时间 -->
            ,t1.english_title_content_task_id AS englishTitleContentTaskId   <!-- 英文标题+内容语音合成任务ID -->
            ,t1.english_title_content_request_id AS englishTitleContentRequestId   <!-- 英文标题+内容语音合成请求ID -->
            ,t1.chinese_title_tts_result  AS chineseTitleTtsResult       <!-- 中文标题语音合成结果（JSON格式） -->
            ,t1.chinese_content_tts_result AS chineseContentTtsResult     <!-- 中文内容语音合成结果（JSON格式） -->
            ,t1.chinese_title_content_tts_result AS chineseTitleContentTtsResult   <!-- 中文标题+内容语音合成结果（JSON格式） -->
            ,t1.english_title_tts_result  AS englishTitleTtsResult       <!-- 英文标题语音合成结果（JSON格式） -->
            ,t1.english_content_tts_result AS englishContentTtsResult     <!-- 英文内容语音合成结果（JSON格式） -->
            ,t1.english_title_content_tts_result AS englishTitleContentTtsResult   <!-- 英文标题+内容语音合成结果（JSON格式） -->
            ,t1.chinese_voice_type        AS chineseVoiceType            <!-- 中文语音音色类型，标题和内容使用同一音色类型 -->
            ,t1.english_voice_type        AS englishVoiceType            <!-- 英文语音音色类型，标题和内容使用同一音色类型 -->
            ,t1.audio_format              AS audioFormat                 <!-- 音频格式（mp3、wav、pcm、flac），中英文统一音频格式 -->
            ,t1.sample_rate               AS sampleRate                  <!-- 音频采样率（Hz），中英文统一音频采样率 -->
            ,t1.chinese_voice_style       AS chineseVoiceStyle           <!-- 中文童话语音情感，标题和内容使用同一情感 -->
            ,t1.english_voice_style       AS englishVoiceStyle           <!-- 英文童话语音情感，标题和内容使用同一情感 -->
            ,t1.chinese_volume            AS chineseVolume               <!-- 中文语音音量（0.1-3.0） -->
            ,t1.chinese_speed             AS chineseSpeed                <!-- 中文语音语速（0.2-3.0） -->
            ,t1.chinese_pitch             AS chinesePitch                <!-- 中文语音语调（0.1-3.0） -->
            ,t1.chinese_sentence_interval AS chineseSentenceInterval     <!-- 中文句间停顿时长（毫秒，0-3000） -->
            ,t1.english_volume            AS englishVolume               <!-- 英文语音音量（0.1-3.0） -->
            ,t1.english_speed             AS englishSpeed                <!-- 英文语音语速（0.2-3.0） -->
            ,t1.english_pitch             AS englishPitch                <!-- 英文语音语调（0.1-3.0） -->
            ,t1.english_sentence_interval AS englishSentenceInterval     <!-- 英文句间停顿时长（毫秒，0-3000） -->
            ,t1.chinese_favorite_count    AS chineseFavoriteCount        <!-- 中文版收藏数 -->
            ,t1.english_favorite_count    AS englishFavoriteCount        <!-- 英文版收藏数 -->
            ,t1.bilingual_favorite_count  AS bilingualFavoriteCount      <!-- 双语版收藏数 -->
            ,t1.app_id                    AS appId                       <!-- 第三方服务应用ID -->
            ,t1.chinese_screenshots        AS chineseScreenshots         <!-- 中文故事截图（多个图片ID，逗号分隔） -->
            ,t1.english_screenshots        AS englishScreenshots         <!-- 英文故事截图（多个图片ID，逗号分隔） -->
            ,t1.last_synthesize_time      AS lastSynthesizeTime         <!-- 最近合成时间 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_fairy_tale t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.fairytale.entity.FairyTale">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.fairytale.entity.FairyTale">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.fairytale.entity.FairyTale">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.fairytale.entity.FairyTale">
        DELETE FROM tth_fairy_tale
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.fairytale.entity.FairyTale">
        UPDATE tth_fairy_tale
        <set>
            <if test="storyNumber               != null or (canNullFields != null and canNullFields.contains('storyNumber'))">
                story_number         = #{storyNumber},
            </if>
            <if test="chineseTitle              != null or (canNullFields != null and canNullFields.contains('chineseTitle'))">
                chinese_title        = #{chineseTitle},
            </if>
            <if test="chineseContent            != null or (canNullFields != null and canNullFields.contains('chineseContent'))">
                chinese_content      = #{chineseContent},
            </if>
            <if test="englishTitle              != null or (canNullFields != null and canNullFields.contains('englishTitle'))">
                english_title        = #{englishTitle},
            </if>
            <if test="englishContent            != null or (canNullFields != null and canNullFields.contains('englishContent'))">
                english_content      = #{englishContent},
            </if>
            <if test="publishStatus             != null or (canNullFields != null and canNullFields.contains('publishStatus'))">
                publish_status       = #{publishStatus},
            </if>
            <if test="needEnglishVersion        != null or (canNullFields != null and canNullFields.contains('needEnglishVersion'))">
                need_english_version = #{needEnglishVersion},
            </if>
            <if test="needBilingualVersion      != null or (canNullFields != null and canNullFields.contains('needBilingualVersion'))">
                need_bilingual_version = #{needBilingualVersion},
            </if>
            <if test="apiVersion                != null or (canNullFields != null and canNullFields.contains('apiVersion'))">
                api_version          = #{apiVersion},
            </if>
            <if test="chineseTitleAudioId       != null or (canNullFields != null and canNullFields.contains('chineseTitleAudioId'))">
                chinese_title_audio_id = #{chineseTitleAudioId},
            </if>
            <if test="chineseContentAudioId     != null or (canNullFields != null and canNullFields.contains('chineseContentAudioId'))">
                chinese_content_audio_id = #{chineseContentAudioId},
            </if>
            <if test="chineseTitleContentAudioId != null or (canNullFields != null and canNullFields.contains('chineseTitleContentAudioId'))">
                chinese_title_content_audio_id = #{chineseTitleContentAudioId},
            </if>
            <if test="englishTitleAudioId       != null or (canNullFields != null and canNullFields.contains('englishTitleAudioId'))">
                english_title_audio_id = #{englishTitleAudioId},
            </if>
            <if test="englishContentAudioId     != null or (canNullFields != null and canNullFields.contains('englishContentAudioId'))">
                english_content_audio_id = #{englishContentAudioId},
            </if>
            <if test="englishTitleContentAudioId != null or (canNullFields != null and canNullFields.contains('englishTitleContentAudioId'))">
                english_title_content_audio_id = #{englishTitleContentAudioId},
            </if>
            <if test="translationStatus         != null or (canNullFields != null and canNullFields.contains('translationStatus'))">
                translation_status   = #{translationStatus},
            </if>
            <if test="translationStartTime      != null or (canNullFields != null and canNullFields.contains('translationStartTime'))">
                translation_start_time = #{translationStartTime},
            </if>
            <if test="translationCompleteTime   != null or (canNullFields != null and canNullFields.contains('translationCompleteTime'))">
                translation_complete_time = #{translationCompleteTime},
            </if>
            <if test="chineseTitleTtsStatus     != null or (canNullFields != null and canNullFields.contains('chineseTitleTtsStatus'))">
                chinese_title_tts_status = #{chineseTitleTtsStatus},
            </if>
            <if test="chineseTitleTtsStartTime  != null or (canNullFields != null and canNullFields.contains('chineseTitleTtsStartTime'))">
                chinese_title_tts_start_time = #{chineseTitleTtsStartTime},
            </if>
            <if test="chineseTitleTtsCompleteTime != null or (canNullFields != null and canNullFields.contains('chineseTitleTtsCompleteTime'))">
                chinese_title_tts_complete_time = #{chineseTitleTtsCompleteTime},
            </if>
            <if test="chineseTitleTaskId        != null or (canNullFields != null and canNullFields.contains('chineseTitleTaskId'))">
                chinese_title_task_id = #{chineseTitleTaskId},
            </if>
            <if test="chineseTitleRequestId     != null or (canNullFields != null and canNullFields.contains('chineseTitleRequestId'))">
                chinese_title_request_id = #{chineseTitleRequestId},
            </if>
            <if test="chineseContentTtsStatus   != null or (canNullFields != null and canNullFields.contains('chineseContentTtsStatus'))">
                chinese_content_tts_status = #{chineseContentTtsStatus},
            </if>
            <if test="chineseContentTtsStartTime != null or (canNullFields != null and canNullFields.contains('chineseContentTtsStartTime'))">
                chinese_content_tts_start_time = #{chineseContentTtsStartTime},
            </if>
            <if test="chineseContentTtsCompleteTime != null or (canNullFields != null and canNullFields.contains('chineseContentTtsCompleteTime'))">
                chinese_content_tts_complete_time = #{chineseContentTtsCompleteTime},
            </if>
            <if test="chineseContentTaskId      != null or (canNullFields != null and canNullFields.contains('chineseContentTaskId'))">
                chinese_content_task_id = #{chineseContentTaskId},
            </if>
            <if test="chineseContentRequestId   != null or (canNullFields != null and canNullFields.contains('chineseContentRequestId'))">
                chinese_content_request_id = #{chineseContentRequestId},
            </if>
            <if test="chineseTitleContentTtsStatus != null or (canNullFields != null and canNullFields.contains('chineseTitleContentTtsStatus'))">
                chinese_title_content_tts_status = #{chineseTitleContentTtsStatus},
            </if>
            <if test="chineseTitleContentTtsStartTime != null or (canNullFields != null and canNullFields.contains('chineseTitleContentTtsStartTime'))">
                chinese_title_content_tts_start_time = #{chineseTitleContentTtsStartTime},
            </if>
            <if test="chineseTitleContentTtsCompleteTime != null or (canNullFields != null and canNullFields.contains('chineseTitleContentTtsCompleteTime'))">
                chinese_title_content_tts_complete_time = #{chineseTitleContentTtsCompleteTime},
            </if>
            <if test="chineseTitleContentTaskId != null or (canNullFields != null and canNullFields.contains('chineseTitleContentTaskId'))">
                chinese_title_content_task_id = #{chineseTitleContentTaskId},
            </if>
            <if test="chineseTitleContentRequestId != null or (canNullFields != null and canNullFields.contains('chineseTitleContentRequestId'))">
                chinese_title_content_request_id = #{chineseTitleContentRequestId},
            </if>
            <if test="englishTitleTtsStatus     != null or (canNullFields != null and canNullFields.contains('englishTitleTtsStatus'))">
                english_title_tts_status = #{englishTitleTtsStatus},
            </if>
            <if test="englishTitleTtsStartTime  != null or (canNullFields != null and canNullFields.contains('englishTitleTtsStartTime'))">
                english_title_tts_start_time = #{englishTitleTtsStartTime},
            </if>
            <if test="englishTitleTtsCompleteTime != null or (canNullFields != null and canNullFields.contains('englishTitleTtsCompleteTime'))">
                english_title_tts_complete_time = #{englishTitleTtsCompleteTime},
            </if>
            <if test="englishTitleTaskId        != null or (canNullFields != null and canNullFields.contains('englishTitleTaskId'))">
                english_title_task_id = #{englishTitleTaskId},
            </if>
            <if test="englishTitleRequestId     != null or (canNullFields != null and canNullFields.contains('englishTitleRequestId'))">
                english_title_request_id = #{englishTitleRequestId},
            </if>
            <if test="englishContentTtsStatus   != null or (canNullFields != null and canNullFields.contains('englishContentTtsStatus'))">
                english_content_tts_status = #{englishContentTtsStatus},
            </if>
            <if test="englishContentTtsStartTime != null or (canNullFields != null and canNullFields.contains('englishContentTtsStartTime'))">
                english_content_tts_start_time = #{englishContentTtsStartTime},
            </if>
            <if test="englishContentTtsCompleteTime != null or (canNullFields != null and canNullFields.contains('englishContentTtsCompleteTime'))">
                english_content_tts_complete_time = #{englishContentTtsCompleteTime},
            </if>
            <if test="englishContentTaskId      != null or (canNullFields != null and canNullFields.contains('englishContentTaskId'))">
                english_content_task_id = #{englishContentTaskId},
            </if>
            <if test="englishContentRequestId   != null or (canNullFields != null and canNullFields.contains('englishContentRequestId'))">
                english_content_request_id = #{englishContentRequestId},
            </if>
            <if test="englishTitleContentTtsStatus != null or (canNullFields != null and canNullFields.contains('englishTitleContentTtsStatus'))">
                english_title_content_tts_status = #{englishTitleContentTtsStatus},
            </if>
            <if test="englishTitleContentTtsStartTime != null or (canNullFields != null and canNullFields.contains('englishTitleContentTtsStartTime'))">
                english_title_content_tts_start_time = #{englishTitleContentTtsStartTime},
            </if>
            <if test="englishTitleContentTtsCompleteTime != null or (canNullFields != null and canNullFields.contains('englishTitleContentTtsCompleteTime'))">
                english_title_content_tts_complete_time = #{englishTitleContentTtsCompleteTime},
            </if>
            <if test="englishTitleContentTaskId != null or (canNullFields != null and canNullFields.contains('englishTitleContentTaskId'))">
                english_title_content_task_id = #{englishTitleContentTaskId},
            </if>
            <if test="englishTitleContentRequestId != null or (canNullFields != null and canNullFields.contains('englishTitleContentRequestId'))">
                english_title_content_request_id = #{englishTitleContentRequestId},
            </if>
            <if test="chineseTitleTtsResult     != null or (canNullFields != null and canNullFields.contains('chineseTitleTtsResult'))">
                chinese_title_tts_result = #{chineseTitleTtsResult},
            </if>
            <if test="chineseContentTtsResult   != null or (canNullFields != null and canNullFields.contains('chineseContentTtsResult'))">
                chinese_content_tts_result = #{chineseContentTtsResult},
            </if>
            <if test="chineseTitleContentTtsResult != null or (canNullFields != null and canNullFields.contains('chineseTitleContentTtsResult'))">
                chinese_title_content_tts_result = #{chineseTitleContentTtsResult},
            </if>
            <if test="englishTitleTtsResult     != null or (canNullFields != null and canNullFields.contains('englishTitleTtsResult'))">
                english_title_tts_result = #{englishTitleTtsResult},
            </if>
            <if test="englishContentTtsResult   != null or (canNullFields != null and canNullFields.contains('englishContentTtsResult'))">
                english_content_tts_result = #{englishContentTtsResult},
            </if>
            <if test="englishTitleContentTtsResult != null or (canNullFields != null and canNullFields.contains('englishTitleContentTtsResult'))">
                english_title_content_tts_result = #{englishTitleContentTtsResult},
            </if>
            <if test="chineseVoiceType          != null or (canNullFields != null and canNullFields.contains('chineseVoiceType'))">
                chinese_voice_type   = #{chineseVoiceType},
            </if>
            <if test="englishVoiceType          != null or (canNullFields != null and canNullFields.contains('englishVoiceType'))">
                english_voice_type   = #{englishVoiceType},
            </if>
            <if test="audioFormat               != null or (canNullFields != null and canNullFields.contains('audioFormat'))">
                audio_format         = #{audioFormat},
            </if>
            <if test="sampleRate                != null or (canNullFields != null and canNullFields.contains('sampleRate'))">
                sample_rate          = #{sampleRate},
            </if>
            <if test="chineseVoiceStyle         != null or (canNullFields != null and canNullFields.contains('chineseVoiceStyle'))">
                chinese_voice_style  = #{chineseVoiceStyle},
            </if>
            <if test="englishVoiceStyle         != null or (canNullFields != null and canNullFields.contains('englishVoiceStyle'))">
                english_voice_style  = #{englishVoiceStyle},
            </if>
            <if test="chineseVolume             != null or (canNullFields != null and canNullFields.contains('chineseVolume'))">
                chinese_volume       = #{chineseVolume},
            </if>
            <if test="chineseSpeed              != null or (canNullFields != null and canNullFields.contains('chineseSpeed'))">
                chinese_speed        = #{chineseSpeed},
            </if>
            <if test="chinesePitch              != null or (canNullFields != null and canNullFields.contains('chinesePitch'))">
                chinese_pitch        = #{chinesePitch},
            </if>
            <if test="chineseSentenceInterval   != null or (canNullFields != null and canNullFields.contains('chineseSentenceInterval'))">
                chinese_sentence_interval = #{chineseSentenceInterval},
            </if>
            <if test="englishVolume             != null or (canNullFields != null and canNullFields.contains('englishVolume'))">
                english_volume       = #{englishVolume},
            </if>
            <if test="englishSpeed              != null or (canNullFields != null and canNullFields.contains('englishSpeed'))">
                english_speed        = #{englishSpeed},
            </if>
            <if test="englishPitch              != null or (canNullFields != null and canNullFields.contains('englishPitch'))">
                english_pitch        = #{englishPitch},
            </if>
            <if test="englishSentenceInterval   != null or (canNullFields != null and canNullFields.contains('englishSentenceInterval'))">
                english_sentence_interval = #{englishSentenceInterval},
            </if>
            <if test="chineseFavoriteCount      != null or (canNullFields != null and canNullFields.contains('chineseFavoriteCount'))">
                chinese_favorite_count = #{chineseFavoriteCount},
            </if>
            <if test="englishFavoriteCount      != null or (canNullFields != null and canNullFields.contains('englishFavoriteCount'))">
                english_favorite_count = #{englishFavoriteCount},
            </if>
            <if test="bilingualFavoriteCount    != null or (canNullFields != null and canNullFields.contains('bilingualFavoriteCount'))">
                bilingual_favorite_count = #{bilingualFavoriteCount},
            </if>
            <if test="appId                     != null or (canNullFields != null and canNullFields.contains('appId'))">
                app_id               = #{appId},
            </if>
            <if test="chineseScreenshots        != null or (canNullFields != null and canNullFields.contains('chineseScreenshots'))">
                chinese_screenshots  = #{chineseScreenshots},
            </if>
            <if test="englishScreenshots        != null or (canNullFields != null and canNullFields.contains('englishScreenshots'))">
                english_screenshots  = #{englishScreenshots},
            </if>
            <if test="lastSynthesizeTime        != null or (canNullFields != null and canNullFields.contains('lastSynthesizeTime'))">
                last_synthesize_time = #{lastSynthesizeTime},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_fairy_tale
            <set>
                <if test="storyNumber     != null">
                    story_number    = #{storyNumber},
                </if>
                <if test="chineseTitle    != null">
                    chinese_title   = #{chineseTitle},
                </if>
                <if test="chineseContent  != null">
                    chinese_content = #{chineseContent},
                </if>
                <if test="englishTitle    != null">
                    english_title   = #{englishTitle},
                </if>
                <if test="englishContent  != null">
                    english_content = #{englishContent},
                </if>
                <if test="publishStatus   != null">
                    publish_status  = #{publishStatus},
                </if>
                <if test="needEnglishVersion != null">
                    need_english_version = #{needEnglishVersion},
                </if>
                <if test="needBilingualVersion != null">
                    need_bilingual_version = #{needBilingualVersion},
                </if>
                <if test="apiVersion      != null">
                    api_version     = #{apiVersion},
                </if>
                <if test="chineseTitleAudioId != null">
                    chinese_title_audio_id = #{chineseTitleAudioId},
                </if>
                <if test="chineseContentAudioId != null">
                    chinese_content_audio_id = #{chineseContentAudioId},
                </if>
                <if test="chineseTitleContentAudioId != null">
                    chinese_title_content_audio_id = #{chineseTitleContentAudioId},
                </if>
                <if test="englishTitleAudioId != null">
                    english_title_audio_id = #{englishTitleAudioId},
                </if>
                <if test="englishContentAudioId != null">
                    english_content_audio_id = #{englishContentAudioId},
                </if>
                <if test="englishTitleContentAudioId != null">
                    english_title_content_audio_id = #{englishTitleContentAudioId},
                </if>
                <if test="translationStatus != null">
                    translation_status = #{translationStatus},
                </if>
                <if test="translationStartTime != null">
                    translation_start_time = #{translationStartTime},
                </if>
                <if test="translationCompleteTime != null">
                    translation_complete_time = #{translationCompleteTime},
                </if>
                <if test="chineseTitleTtsStatus != null">
                    chinese_title_tts_status = #{chineseTitleTtsStatus},
                </if>
                <if test="chineseTitleTtsStartTime != null">
                    chinese_title_tts_start_time = #{chineseTitleTtsStartTime},
                </if>
                <if test="chineseTitleTtsCompleteTime != null">
                    chinese_title_tts_complete_time = #{chineseTitleTtsCompleteTime},
                </if>
                <if test="chineseTitleTaskId != null">
                    chinese_title_task_id = #{chineseTitleTaskId},
                </if>
                <if test="chineseTitleRequestId != null">
                    chinese_title_request_id = #{chineseTitleRequestId},
                </if>
                <if test="chineseContentTtsStatus != null">
                    chinese_content_tts_status = #{chineseContentTtsStatus},
                </if>
                <if test="chineseContentTtsStartTime != null">
                    chinese_content_tts_start_time = #{chineseContentTtsStartTime},
                </if>
                <if test="chineseContentTtsCompleteTime != null">
                    chinese_content_tts_complete_time = #{chineseContentTtsCompleteTime},
                </if>
                <if test="chineseContentTaskId != null">
                    chinese_content_task_id = #{chineseContentTaskId},
                </if>
                <if test="chineseContentRequestId != null">
                    chinese_content_request_id = #{chineseContentRequestId},
                </if>
                <if test="chineseTitleContentTtsStatus != null">
                    chinese_title_content_tts_status = #{chineseTitleContentTtsStatus},
                </if>
                <if test="chineseTitleContentTtsStartTime != null">
                    chinese_title_content_tts_start_time = #{chineseTitleContentTtsStartTime},
                </if>
                <if test="chineseTitleContentTtsCompleteTime != null">
                    chinese_title_content_tts_complete_time = #{chineseTitleContentTtsCompleteTime},
                </if>
                <if test="chineseTitleContentTaskId != null">
                    chinese_title_content_task_id = #{chineseTitleContentTaskId},
                </if>
                <if test="chineseTitleContentRequestId != null">
                    chinese_title_content_request_id = #{chineseTitleContentRequestId},
                </if>
                <if test="englishTitleTtsStatus != null">
                    english_title_tts_status = #{englishTitleTtsStatus},
                </if>
                <if test="englishTitleTtsStartTime != null">
                    english_title_tts_start_time = #{englishTitleTtsStartTime},
                </if>
                <if test="englishTitleTtsCompleteTime != null">
                    english_title_tts_complete_time = #{englishTitleTtsCompleteTime},
                </if>
                <if test="englishTitleTaskId != null">
                    english_title_task_id = #{englishTitleTaskId},
                </if>
                <if test="englishTitleRequestId != null">
                    english_title_request_id = #{englishTitleRequestId},
                </if>
                <if test="englishContentTtsStatus != null">
                    english_content_tts_status = #{englishContentTtsStatus},
                </if>
                <if test="englishContentTtsStartTime != null">
                    english_content_tts_start_time = #{englishContentTtsStartTime},
                </if>
                <if test="englishContentTtsCompleteTime != null">
                    english_content_tts_complete_time = #{englishContentTtsCompleteTime},
                </if>
                <if test="englishContentTaskId != null">
                    english_content_task_id = #{englishContentTaskId},
                </if>
                <if test="englishContentRequestId != null">
                    english_content_request_id = #{englishContentRequestId},
                </if>
                <if test="englishTitleContentTtsStatus != null">
                    english_title_content_tts_status = #{englishTitleContentTtsStatus},
                </if>
                <if test="englishTitleContentTtsStartTime != null">
                    english_title_content_tts_start_time = #{englishTitleContentTtsStartTime},
                </if>
                <if test="englishTitleContentTtsCompleteTime != null">
                    english_title_content_tts_complete_time = #{englishTitleContentTtsCompleteTime},
                </if>
                <if test="englishTitleContentTaskId != null">
                    english_title_content_task_id = #{englishTitleContentTaskId},
                </if>
                <if test="englishTitleContentRequestId != null">
                    english_title_content_request_id = #{englishTitleContentRequestId},
                </if>
                <if test="chineseTitleTtsResult != null">
                    chinese_title_tts_result = #{chineseTitleTtsResult},
                </if>
                <if test="chineseContentTtsResult != null">
                    chinese_content_tts_result = #{chineseContentTtsResult},
                </if>
                <if test="chineseTitleContentTtsResult != null">
                    chinese_title_content_tts_result = #{chineseTitleContentTtsResult},
                </if>
                <if test="englishTitleTtsResult != null">
                    english_title_tts_result = #{englishTitleTtsResult},
                </if>
                <if test="englishContentTtsResult != null">
                    english_content_tts_result = #{englishContentTtsResult},
                </if>
                <if test="englishTitleContentTtsResult != null">
                    english_title_content_tts_result = #{englishTitleContentTtsResult},
                </if>
                <if test="chineseVoiceType != null">
                    chinese_voice_type = #{chineseVoiceType},
                </if>
                <if test="englishVoiceType != null">
                    english_voice_type = #{englishVoiceType},
                </if>
                <if test="audioFormat     != null">
                    audio_format    = #{audioFormat},
                </if>
                <if test="sampleRate      != null">
                    sample_rate     = #{sampleRate},
                </if>
                <if test="chineseVoiceStyle != null">
                    chinese_voice_style = #{chineseVoiceStyle},
                </if>
                <if test="englishVoiceStyle != null">
                    english_voice_style = #{englishVoiceStyle},
                </if>
                <if test="chineseVolume   != null">
                    chinese_volume  = #{chineseVolume},
                </if>
                <if test="chineseSpeed    != null">
                    chinese_speed   = #{chineseSpeed},
                </if>
                <if test="chinesePitch    != null">
                    chinese_pitch   = #{chinesePitch},
                </if>
                <if test="chineseSentenceInterval != null">
                    chinese_sentence_interval = #{chineseSentenceInterval},
                </if>
                <if test="englishVolume   != null">
                    english_volume  = #{englishVolume},
                </if>
                <if test="englishSpeed    != null">
                    english_speed   = #{englishSpeed},
                </if>
                <if test="englishPitch    != null">
                    english_pitch   = #{englishPitch},
                </if>
                <if test="englishSentenceInterval != null">
                    english_sentence_interval = #{englishSentenceInterval},
                </if>
                <if test="chineseFavoriteCount != null">
                    chinese_favorite_count = #{chineseFavoriteCount},
                </if>
                <if test="englishFavoriteCount != null">
                    english_favorite_count = #{englishFavoriteCount},
                </if>
                <if test="bilingualFavoriteCount != null">
                    bilingual_favorite_count = #{bilingualFavoriteCount},
                </if>
                <if test="appId           != null">
                    app_id          = #{appId},
                </if>
                <if test="chineseScreenshots != null">
                    chinese_screenshots = #{chineseScreenshots},
                </if>
                <if test="englishScreenshots != null">
                    english_screenshots = #{englishScreenshots},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

    <!-- 获取指定日期的最大故事编号 -->
    <select id="getMaxStoryNumberByDate" resultType="java.lang.String">
        SELECT MAX(story_number)
        FROM tth_fairy_tale
        WHERE story_number LIKE CONCAT(#{datePrefix}, '%')
    </select>

</mapper>
