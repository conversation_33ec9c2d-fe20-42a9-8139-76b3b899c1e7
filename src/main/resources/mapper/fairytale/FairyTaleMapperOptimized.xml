<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.fairytale.mapper.FairyTaleMapperOptimized">

    <!-- 基础结果映射（只包含列表需要的字段，减少数据传输） -->
    <resultMap id="FairyTaleListVO" type="com.tth.modules.fairytale.model.vo.FairyTaleListVOV3">
        <id column="id" property="id"/>
        <result column="story_number" property="storyNumber"/>
        <result column="chinese_title" property="chineseTitle"/>
        <result column="english_title" property="englishTitle"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="need_english_version" property="needEnglishVersion"/>
        <result column="need_bilingual_version" property="needBilingualVersion"/>
        
        <!-- 音频文件ID -->
        <result column="chinese_title_audio_id" property="chineseTitleAudioId"/>
        <result column="chinese_content_audio_id" property="chineseContentAudioId"/>
        <result column="chinese_title_content_audio_id" property="chineseTitleContentAudioId"/>
        <result column="english_title_audio_id" property="englishTitleAudioId"/>
        <result column="english_content_audio_id" property="englishContentAudioId"/>
        <result column="english_title_content_audio_id" property="englishTitleContentAudioId"/>
        
        <!-- 截图文件ID -->
        <result column="chinese_screenshots" property="chineseScreenshots"/>
        <result column="english_screenshots" property="englishScreenshots"/>
        
        <!-- 收藏数 -->
        <result column="chinese_favorite_count" property="chineseFavoriteCount"/>
        <result column="english_favorite_count" property="englishFavoriteCount"/>
        <result column="bilingual_favorite_count" property="bilingualFavoriteCount"/>
        
        <!-- 音频配置 -->
        <result column="audio_format" property="audioFormat"/>
        <result column="sample_rate" property="sampleRate"/>
        <result column="chinese_voice_type" property="chineseVoiceType"/>
        <result column="english_voice_type" property="englishVoiceType"/>
        
        <!-- 时间字段 -->
        <result column="created_time" property="createdTime"/>
        <result column="modified_time" property="modifiedTime"/>
    </resultMap>

    <!-- 基础查询字段（覆盖索引字段） -->
    <sql id="BaseListColumns">
        id, story_number, chinese_title, english_title,
        publish_status, need_english_version, need_bilingual_version,
        chinese_title_audio_id, chinese_content_audio_id, chinese_title_content_audio_id,
        english_title_audio_id, english_content_audio_id, english_title_content_audio_id,
        chinese_screenshots, english_screenshots,
        chinese_favorite_count, english_favorite_count, bilingual_favorite_count,
        audio_format, sample_rate, chinese_voice_type, english_voice_type,
        created_time, modified_time
    </sql>

    <!-- 优化的分页查询 -->
    <select id="selectOptimizedPage" resultMap="FairyTaleListVO">
        SELECT 
        <include refid="BaseListColumns"/>
        FROM tth_fairy_tale
        <where>
            <if test="dto.publishStatus != null">
                AND publish_status = #{dto.publishStatus}
            </if>
            <if test="dto.needEnglishVersion != null">
                AND need_english_version = #{dto.needEnglishVersion}
            </if>
            <if test="dto.needBilingualVersion != null">
                AND need_bilingual_version = #{dto.needBilingualVersion}
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (chinese_title LIKE CONCAT('%', #{dto.keyword}, '%') 
                     OR english_title LIKE CONCAT('%', #{dto.keyword}, '%'))
            </if>
            <if test="dto.startTime != null">
                AND created_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND created_time &lt;= #{dto.endTime}
            </if>
        </where>
        <choose>
            <when test="dto.orderBy != null and dto.orderBy == 'chineseFavoriteCount'">
                ORDER BY chinese_favorite_count DESC, created_time DESC
            </when>
            <when test="dto.orderBy != null and dto.orderBy == 'englishFavoriteCount'">
                ORDER BY english_favorite_count DESC, created_time DESC
            </when>
            <when test="dto.orderBy != null and dto.orderBy == 'bilingualFavoriteCount'">
                ORDER BY bilingual_favorite_count DESC, created_time DESC
            </when>
            <otherwise>
                ORDER BY created_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询已发布的故事（使用最优索引） -->
    <select id="selectPublishedPage" resultMap="FairyTaleListVO">
        SELECT 
        <include refid="BaseListColumns"/>
        FROM tth_fairy_tale
        WHERE publish_status = 1
        ORDER BY created_time DESC
    </select>

    <!-- 热门故事查询（按收藏数排序） -->
    <select id="selectPopularPage" resultMap="FairyTaleListVO">
        SELECT 
        <include refid="BaseListColumns"/>
        FROM tth_fairy_tale
        WHERE publish_status = 1
        <choose>
            <when test="languageVersion == 'chinese'">
                ORDER BY chinese_favorite_count DESC, created_time DESC
            </when>
            <when test="languageVersion == 'english'">
                ORDER BY english_favorite_count DESC, created_time DESC
            </when>
            <when test="languageVersion == 'bilingual'">
                ORDER BY bilingual_favorite_count DESC, created_time DESC
            </when>
            <otherwise>
                ORDER BY (chinese_favorite_count + english_favorite_count + bilingual_favorite_count) DESC, created_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 标题搜索（使用标题索引） -->
    <select id="searchByTitle" resultMap="FairyTaleListVO">
        SELECT 
        <include refid="BaseListColumns"/>
        FROM tth_fairy_tale
        WHERE publish_status = 1
        AND (chinese_title LIKE CONCAT('%', #{keyword}, '%') 
             OR english_title LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY 
            CASE 
                WHEN chinese_title = #{keyword} OR english_title = #{keyword} THEN 1
                WHEN chinese_title LIKE CONCAT(#{keyword}, '%') OR english_title LIKE CONCAT(#{keyword}, '%') THEN 2
                ELSE 3
            END,
            created_time DESC
    </select>

    <!-- 统计查询（使用索引） -->
    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(1) FROM tth_fairy_tale WHERE publish_status = #{publishStatus}
    </select>

    <!-- 批量查询（使用主键索引） -->
    <select id="selectBatchByIds" resultMap="FairyTaleListVO">
        SELECT 
        <include refid="BaseListColumns"/>
        FROM tth_fairy_tale
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY FIELD(id, 
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

</mapper>
