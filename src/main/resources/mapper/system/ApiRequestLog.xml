<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.system.mapper.ApiRequestLogMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.system.entity.ApiRequestLog">
        <result column="id"                        property="id" />
        <result column="created_time"              property="createdTime" />
        <result column="created_by"                property="createdBy" />
        <result column="modified_time"             property="modifiedTime" />
        <result column="modified_by"               property="modifiedBy" />
        <result column="deleted_time"              property="deletedTime" />
        <result column="deleted_by"                property="deletedBy" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="log_type"                  property="logType" />
        <result column="request_id"                property="requestId" />
        <result column="module"                    property="module" />
        <result column="operation"                 property="operation" />
        <result column="user_id"                   property="userId" />
        <result column="url"                       property="url" />
        <result column="method"                    property="method" />
        <result column="header_params"             property="headerParams" />
        <result column="query_params"              property="queryParams" />
        <result column="request_body"              property="requestBody" />
        <result column="response_status"           property="responseStatus" />
        <result column="biz_status"                property="bizStatus" />
        <result column="response_body"             property="responseBody" />
        <result column="ip"                        property="ip" />
        <result column="ip_position"               property="ipPosition" />
        <result column="user_agent"                property="userAgent" />
        <result column="duration"                  property="duration" />
        <result column="http_status"               property="httpStatus" />
        <result column="error_msg"                 property="errorMsg" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除 -->

            ,t1.log_type                  AS logType                     <!-- 日志类型：1-系统API日志，2-第三方API日志 -->
            ,t1.request_id                AS requestId                   <!-- 请求ID，用于链路追踪 -->
            ,t1.module                    AS module                      <!-- 模块名称 -->
            ,t1.operation                 AS operation                   <!-- 操作描述 -->
            ,t1.user_id                   AS userId                      <!-- 操作用户ID -->
            ,t1.url                       AS url                         <!-- 请求URL -->
            ,t1.method                    AS method                      <!-- 请求方法(GET/POST等) -->
            ,t1.header_params             AS headerParams                <!-- 请求头参数 -->
            ,t1.query_params              AS queryParams                 <!-- 查询参数(URL参数) -->
            ,t1.request_body              AS requestBody                 <!-- 请求体内容 -->
            ,t1.response_status           AS responseStatus              <!-- 响应体响应状态码 -->
            ,t1.biz_status                AS bizStatus                   <!-- 业务成功状态状态，0：成功，1：失败 -->
            ,t1.response_body             AS responseBody                <!-- 响应体内容 -->
            ,t1.ip                        AS ip                          <!-- 客户端IP -->
            ,t1.ip_position               AS ipPosition                  <!-- 客户端IP实际地址 -->
            ,t1.user_agent                AS userAgent                   <!-- 用户代理 -->
            ,t1.duration                  AS duration                    <!-- 请求耗时(毫秒) -->
            ,t1.http_status               AS httpStatus                  <!-- http状态码 -->
            ,t1.error_msg                 AS errorMsg                    <!-- 错误信息，包含异常堆栈 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_api_request_log t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.ApiRequestLog">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.system.entity.ApiRequestLog">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.ApiRequestLog">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.system.entity.ApiRequestLog">
        DELETE FROM tth_api_request_log
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.system.entity.ApiRequestLog">
        UPDATE tth_api_request_log
        <set>
            <if test="logType                   != null or (canNullFields != null and canNullFields.contains('logType'))">
                log_type             = #{logType},
            </if>
            <if test="requestId                 != null or (canNullFields != null and canNullFields.contains('requestId'))">
                request_id           = #{requestId},
            </if>
            <if test="module                    != null or (canNullFields != null and canNullFields.contains('module'))">
                module               = #{module},
            </if>
            <if test="operation                 != null or (canNullFields != null and canNullFields.contains('operation'))">
                operation            = #{operation},
            </if>
            <if test="userId                    != null or (canNullFields != null and canNullFields.contains('userId'))">
                user_id              = #{userId},
            </if>
            <if test="url                       != null or (canNullFields != null and canNullFields.contains('url'))">
                url                  = #{url},
            </if>
            <if test="method                    != null or (canNullFields != null and canNullFields.contains('method'))">
                method               = #{method},
            </if>
            <if test="headerParams              != null or (canNullFields != null and canNullFields.contains('headerParams'))">
                header_params        = #{headerParams},
            </if>
            <if test="queryParams               != null or (canNullFields != null and canNullFields.contains('queryParams'))">
                query_params         = #{queryParams},
            </if>
            <if test="requestBody               != null or (canNullFields != null and canNullFields.contains('requestBody'))">
                request_body         = #{requestBody},
            </if>
            <if test="responseStatus            != null or (canNullFields != null and canNullFields.contains('responseStatus'))">
                response_status      = #{responseStatus},
            </if>
            <if test="bizStatus                 != null or (canNullFields != null and canNullFields.contains('bizStatus'))">
                biz_status           = #{bizStatus},
            </if>
            <if test="responseBody              != null or (canNullFields != null and canNullFields.contains('responseBody'))">
                response_body        = #{responseBody},
            </if>
            <if test="ip                        != null or (canNullFields != null and canNullFields.contains('ip'))">
                ip                   = #{ip},
            </if>
            <if test="ipPosition                != null or (canNullFields != null and canNullFields.contains('ipPosition'))">
                ip_position          = #{ipPosition},
            </if>
            <if test="userAgent                 != null or (canNullFields != null and canNullFields.contains('userAgent'))">
                user_agent           = #{userAgent},
            </if>
            <if test="duration                  != null or (canNullFields != null and canNullFields.contains('duration'))">
                duration             = #{duration},
            </if>
            <if test="httpStatus                != null or (canNullFields != null and canNullFields.contains('httpStatus'))">
                http_status          = #{httpStatus},
            </if>
            <if test="errorMsg                  != null or (canNullFields != null and canNullFields.contains('errorMsg'))">
                error_msg            = #{errorMsg},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_api_request_log
            <set>
                <if test="logType         != null">
                    log_type        = #{logType},
                </if>
                <if test="requestId       != null">
                    request_id      = #{requestId},
                </if>
                <if test="module          != null">
                    module          = #{module},
                </if>
                <if test="operation       != null">
                    operation       = #{operation},
                </if>
                <if test="userId          != null">
                    user_id         = #{userId},
                </if>
                <if test="url             != null">
                    url             = #{url},
                </if>
                <if test="method          != null">
                    method          = #{method},
                </if>
                <if test="headerParams    != null">
                    header_params   = #{headerParams},
                </if>
                <if test="queryParams     != null">
                    query_params    = #{queryParams},
                </if>
                <if test="requestBody     != null">
                    request_body    = #{requestBody},
                </if>
                <if test="responseStatus  != null">
                    response_status = #{responseStatus},
                </if>
                <if test="bizStatus       != null">
                    biz_status      = #{bizStatus},
                </if>
                <if test="responseBody    != null">
                    response_body   = #{responseBody},
                </if>
                <if test="ip              != null">
                    ip              = #{ip},
                </if>
                <if test="ipPosition      != null">
                    ip_position     = #{ipPosition},
                </if>
                <if test="userAgent       != null">
                    user_agent      = #{userAgent},
                </if>
                <if test="duration        != null">
                    duration        = #{duration},
                </if>
                <if test="httpStatus      != null">
                    http_status     = #{httpStatus},
                </if>
                <if test="errorMsg        != null">
                    error_msg       = #{errorMsg},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
