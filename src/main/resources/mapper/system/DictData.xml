<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.system.mapper.DictDataMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.system.entity.DictData">
        <result column="id"                        property="id" />
        <result column="createdBy"                 property="createdBy" />
        <result column="createdTime"               property="createdTime" />
        <result column="modifiedBy"                property="modifiedBy" />
        <result column="modifiedTime"              property="modifiedTime" />
        <result column="deletedBy"                 property="deletedBy" />
        <result column="deletedTime"               property="deletedTime" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="dictType"                  property="dictType" />
        <result column="dictLabel"                 property="dictLabel" />
        <result column="dictValue"                 property="dictValue" />
        <result column="storeValue"                property="storeValue" />
        <result column="dictSort"                  property="dictSort" />
        <result column="cssClass"                  property="cssClass" />
        <result column="listClass"                 property="listClass" />
        <result column="isDefault"                 property="isDefault" />
        <result column="status"                    property="status" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除 -->

            ,t1.dict_type                 AS dictType                    <!-- 字典类型编码 -->
            ,t1.dict_label                AS dictLabel                   <!-- 字典标签（显示值） -->
            ,t1.dict_value                AS dictValue                   <!-- 字典键值（实际存储值） -->
            ,t1.store_value               AS storeValue                  <!-- 枚举存储值（如PublishStatusEnum的0或1） -->
            ,t1.dict_sort                 AS dictSort                    <!-- 排序 -->
            ,t1.css_class                 AS cssClass                    <!-- CSS样式 -->
            ,t1.list_class                AS listClass                   <!-- 表格回显样式 -->
            ,t1.is_default                AS isDefault                   <!-- 是否默认（Y-是，N-否） -->
            ,t1.status                    AS status                      <!-- 状态（0-禁用，1-启用） -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_dict_data t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.DictData">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.system.entity.DictData">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.DictData">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.system.entity.DictData">
        DELETE FROM tth_dict_data
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.system.entity.DictData">
        UPDATE tth_dict_data
        <set>
            <if test="dictType                  != null or (canNullFields != null and canNullFields.contains('dictType'))">
                dict_type            = #{dictType},
            </if>
            <if test="dictLabel                 != null or (canNullFields != null and canNullFields.contains('dictLabel'))">
                dict_label           = #{dictLabel},
            </if>
            <if test="dictValue                 != null or (canNullFields != null and canNullFields.contains('dictValue'))">
                dict_value           = #{dictValue},
            </if>
            <if test="dictSort                  != null or (canNullFields != null and canNullFields.contains('dictSort'))">
                dict_sort            = #{dictSort},
            </if>
            <if test="cssClass                  != null or (canNullFields != null and canNullFields.contains('cssClass'))">
                css_class            = #{cssClass},
            </if>
            <if test="listClass                 != null or (canNullFields != null and canNullFields.contains('listClass'))">
                list_class           = #{listClass},
            </if>
            <if test="isDefault                 != null or (canNullFields != null and canNullFields.contains('isDefault'))">
                is_default           = #{isDefault},
            </if>
            <if test="status                    != null or (canNullFields != null and canNullFields.contains('status'))">
                status               = #{status},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_dict_data
            <set>
                <if test="dictType        != null">
                    dict_type       = #{dictType},
                </if>
                <if test="dictLabel       != null">
                    dict_label      = #{dictLabel},
                </if>
                <if test="dictValue       != null">
                    dict_value      = #{dictValue},
                </if>
                <if test="dictSort        != null">
                    dict_sort       = #{dictSort},
                </if>
                <if test="cssClass        != null">
                    css_class       = #{cssClass},
                </if>
                <if test="listClass       != null">
                    list_class      = #{listClass},
                </if>
                <if test="isDefault       != null">
                    is_default      = #{isDefault},
                </if>
                <if test="status          != null">
                    status          = #{status},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
