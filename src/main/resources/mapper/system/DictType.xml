<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.system.mapper.DictTypeMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.system.entity.DictType">
        <result column="id"                        property="id" />
        <result column="createdBy"                 property="createdBy" />
        <result column="createdTime"               property="createdTime" />
        <result column="modifiedBy"                property="modifiedBy" />
        <result column="modifiedTime"              property="modifiedTime" />
        <result column="deletedBy"                 property="deletedBy" />
        <result column="deletedTime"               property="deletedTime" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="dictName"                  property="dictName" />
        <result column="dictType"                  property="dictType" />
        <result column="status"                    property="status" />
        <result column="sort"                      property="sort" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除 -->

            ,t1.dict_name                 AS dictName                    <!-- 字典类型名称 -->
            ,t1.dict_type                 AS dictType                    <!-- 字典类型编码 -->
            ,t1.status                    AS status                      <!-- 状态（0-禁用，1-启用） -->
            ,t1.sort                      AS sort                        <!-- 排序 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_dict_type t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.DictType">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.system.entity.DictType">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.DictType">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.system.entity.DictType">
        DELETE FROM tth_dict_type
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.system.entity.DictType">
        UPDATE tth_dict_type
        <set>
            <if test="dictName                  != null or (canNullFields != null and canNullFields.contains('dictName'))">
                dict_name            = #{dictName},
            </if>
            <if test="dictType                  != null or (canNullFields != null and canNullFields.contains('dictType'))">
                dict_type            = #{dictType},
            </if>
            <if test="status                    != null or (canNullFields != null and canNullFields.contains('status'))">
                status               = #{status},
            </if>
            <if test="sort                      != null or (canNullFields != null and canNullFields.contains('sort'))">
                sort                 = #{sort},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_dict_type
            <set>
                <if test="dictName        != null">
                    dict_name       = #{dictName},
                </if>
                <if test="dictType        != null">
                    dict_type       = #{dictType},
                </if>
                <if test="status          != null">
                    status          = #{status},
                </if>
                <if test="sort            != null">
                    sort            = #{sort},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
