<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.system.mapper.OssFileMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.system.entity.OssFile">
        <result column="id"                        property="id" />
        <result column="created_by"                property="createdBy" />
        <result column="created_time"              property="createdTime" />
        <result column="modified_by"               property="modifiedBy" />
        <result column="modified_time"             property="modifiedTime" />
        <result column="deleted_by"                property="deletedBy" />
        <result column="deleted_time"              property="deletedTime" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="file_name"                 property="fileName" />
        <result column="user_id"                   property="userId" />
        <result column="file_path"                 property="filePath" />
        <result column="file_url"                  property="fileUrl" />
        <result column="file_ext"                  property="fileExt" />
        <result column="file_size"                 property="fileSize" />
        <result column="mime_type"                 property="mimeType" />
        <result column="file_type"                 property="fileType" />
        <result column="group_name"                property="groupName" />
        <result column="width"                     property="width" />
        <result column="height"                    property="height" />
        <result column="format"                    property="format" />
        <result column="bucket_name"               property="bucketName" />
        <result column="object_name"               property="objectName" />
        <result column="etag"                      property="etag" />
        <result column="client_ip"                 property="clientIp" />
        <result column="ip_position"               property="ipPosition" />
        <result column="operation"                 property="operation" />
        <result column="business_id"               property="businessId" />
        <result column="business_type"             property="businessType" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除 -->

            ,t1.file_name                 AS fileName                    <!-- 文件名称 -->
            ,t1.user_id                   AS userId                      <!--  -->
            ,t1.file_path                 AS filePath                    <!-- 文件路径 -->
            ,t1.file_url                  AS fileUrl                     <!-- 文件访问URL -->
            ,t1.file_ext                  AS fileExt                     <!-- 文件扩展名 -->
            ,t1.file_size                 AS fileSize                    <!-- 文件大小(字节) -->
            ,t1.mime_type                 AS mimeType                    <!-- 文件MIME类型 -->
            ,t1.file_type                 AS fileType                    <!-- 资源类型(1-图片,2-视频,3-音频,4-文档,5-压缩包,99-其他) -->
            ,t1.group_name                AS groupName                   <!-- 分组名称 -->
            ,t1.width                     AS width                       <!-- 图片宽度 -->
            ,t1.height                    AS height                      <!-- 图片高度 -->
            ,t1.format                    AS format                      <!-- 图片格式，例如JPG、PNG等。该变量仅适用于图片格式，对于非图片格式，该变量的值为空。 -->
            ,t1.bucket_name               AS bucketName                  <!-- OSS桶名称 -->
            ,t1.object_name               AS objectName                  <!-- OSS对象名称 -->
            ,t1.etag                      AS etag                        <!-- OSS ETag -->
            ,t1.client_ip                 AS clientIp                    <!-- 发起请求的客户端IP地址。 -->
            ,t1.ip_position               AS ipPosition                  <!-- ip中文地址 -->
            ,t1.operation                 AS operation                   <!-- 发起请求的接口名称，例如PutObject、PostObject等。 -->
            ,t1.business_id               AS businessId                  <!-- 关联业务ID -->
            ,t1.business_type             AS businessType                <!-- 关联业务类型 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_oss_file t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.OssFile">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.system.entity.OssFile">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.OssFile">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.system.entity.OssFile">
        DELETE FROM tth_oss_file
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.system.entity.OssFile">
        UPDATE tth_oss_file
        <set>
            <if test="fileName                  != null or (canNullFields != null and canNullFields.contains('fileName'))">
                file_name            = #{fileName},
            </if>
            <if test="userId                    != null or (canNullFields != null and canNullFields.contains('userId'))">
                user_id              = #{userId},
            </if>
            <if test="filePath                  != null or (canNullFields != null and canNullFields.contains('filePath'))">
                file_path            = #{filePath},
            </if>
            <if test="fileUrl                   != null or (canNullFields != null and canNullFields.contains('fileUrl'))">
                file_url             = #{fileUrl},
            </if>
            <if test="fileExt                   != null or (canNullFields != null and canNullFields.contains('fileExt'))">
                file_ext             = #{fileExt},
            </if>
            <if test="fileSize                  != null or (canNullFields != null and canNullFields.contains('fileSize'))">
                file_size            = #{fileSize},
            </if>
            <if test="mimeType                  != null or (canNullFields != null and canNullFields.contains('mimeType'))">
                mime_type            = #{mimeType},
            </if>
            <if test="fileType                  != null or (canNullFields != null and canNullFields.contains('fileType'))">
                file_type            = #{fileType},
            </if>
            <if test="groupName                 != null or (canNullFields != null and canNullFields.contains('groupName'))">
                group_name           = #{groupName},
            </if>
            <if test="width                     != null or (canNullFields != null and canNullFields.contains('width'))">
                width                = #{width},
            </if>
            <if test="height                    != null or (canNullFields != null and canNullFields.contains('height'))">
                height               = #{height},
            </if>
            <if test="format                    != null or (canNullFields != null and canNullFields.contains('format'))">
                format               = #{format},
            </if>
            <if test="bucketName                != null or (canNullFields != null and canNullFields.contains('bucketName'))">
                bucket_name          = #{bucketName},
            </if>
            <if test="objectName                != null or (canNullFields != null and canNullFields.contains('objectName'))">
                object_name          = #{objectName},
            </if>
            <if test="etag                      != null or (canNullFields != null and canNullFields.contains('etag'))">
                etag                 = #{etag},
            </if>
            <if test="clientIp                  != null or (canNullFields != null and canNullFields.contains('clientIp'))">
                client_ip            = #{clientIp},
            </if>
            <if test="ipPosition                != null or (canNullFields != null and canNullFields.contains('ipPosition'))">
                ip_position          = #{ipPosition},
            </if>
            <if test="operation                 != null or (canNullFields != null and canNullFields.contains('operation'))">
                operation            = #{operation},
            </if>
            <if test="businessId                != null or (canNullFields != null and canNullFields.contains('businessId'))">
                business_id          = #{businessId},
            </if>
            <if test="businessType              != null or (canNullFields != null and canNullFields.contains('businessType'))">
                business_type        = #{businessType},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_oss_file
            <set>
                <if test="fileName        != null">
                    file_name       = #{fileName},
                </if>
                <if test="userId          != null">
                    user_id         = #{userId},
                </if>
                <if test="filePath        != null">
                    file_path       = #{filePath},
                </if>
                <if test="fileUrl         != null">
                    file_url        = #{fileUrl},
                </if>
                <if test="fileExt         != null">
                    file_ext        = #{fileExt},
                </if>
                <if test="fileSize        != null">
                    file_size       = #{fileSize},
                </if>
                <if test="mimeType        != null">
                    mime_type       = #{mimeType},
                </if>
                <if test="fileType        != null">
                    file_type       = #{fileType},
                </if>
                <if test="groupName       != null">
                    group_name      = #{groupName},
                </if>
                <if test="width           != null">
                    width           = #{width},
                </if>
                <if test="height          != null">
                    height          = #{height},
                </if>
                <if test="format          != null">
                    format          = #{format},
                </if>
                <if test="bucketName      != null">
                    bucket_name     = #{bucketName},
                </if>
                <if test="objectName      != null">
                    object_name     = #{objectName},
                </if>
                <if test="etag            != null">
                    etag            = #{etag},
                </if>
                <if test="clientIp        != null">
                    client_ip       = #{clientIp},
                </if>
                <if test="ipPosition      != null">
                    ip_position     = #{ipPosition},
                </if>
                <if test="operation       != null">
                    operation       = #{operation},
                </if>
                <if test="businessId      != null">
                    business_id     = #{businessId},
                </if>
                <if test="businessType    != null">
                    business_type   = #{businessType},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
