<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.system.mapper.SystemConfigMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.system.entity.SystemConfig">
        <result column="id"                                  property="id" />
        <result column="createdBy"                          property="createdBy" />
        <result column="createdTime"                        property="createdTime" />
        <result column="modifiedBy"                         property="modifiedBy" />
        <result column="modifiedTime"                       property="modifiedTime" />
        <result column="deletedBy"                          property="deletedBy" />
        <result column="deletedTime"                        property="deletedTime" />
        <result column="version"                             property="version" />
        <result column="remark"                              property="remark" />
        <result column="deleted"                             property="deleted" />

        <result column="groupType"                          property="groupType" />
        <result column="provider"                            property="provider" />
        <result column="configKey"                          property="configKey" />
        <result column="valueType"                          property="valueType" />
        <result column="configValue"                        property="configValue" />
        <result column="ossFileId"                         property="ossFileId" />
        <result column="description"                         property="description" />
        <result column="isSensitive"                        property="isSensitive" />
        <result column="sort"                                property="sort" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除 -->

            ,t1.group_type                AS groupType                   <!-- 分组类型 -->
            ,t1.provider                  AS provider                    <!-- 服务提供商(0-基础,1-支付宝,2-微信,3-阿里云,4-腾讯云,5-高德地图) -->
            ,t1.config_key                AS configKey                   <!-- 配置键 -->
            ,t1.value_type                AS valueType                   <!-- 配置值类型：1-字符串值,2-文档文件 -->
            ,t1.config_value              AS configValue                 <!-- 配置值 -->
            ,t1.oss_file_id               AS ossFileId                   <!-- 关联OSS文件ID（valueType=2时使用） -->
            ,t1.description               AS description                 <!-- 配置描述 -->
            ,t1.is_sensitive              AS isSensitive                 <!-- 是否敏感信息（0-否，1-是） -->
            ,t1.sort                      AS sort                        <!-- 排序 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_system_config t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.SystemConfig">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.system.entity.SystemConfig">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.system.entity.SystemConfig">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.system.entity.SystemConfig">
        DELETE FROM tth_system_config
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.system.entity.SystemConfig">
        UPDATE tth_system_config
        <set>
            <if test="groupType                 != null or (canNullFields != null and canNullFields.contains('groupType'))">
                group_type           = #{groupType},
            </if>
            <if test="provider                  != null or (canNullFields != null and canNullFields.contains('provider'))">
                provider             = #{provider},
            </if>
            <if test="configKey                 != null or (canNullFields != null and canNullFields.contains('configKey'))">
                config_key           = #{configKey},
            </if>
            <if test="valueType                 != null or (canNullFields != null and canNullFields.contains('valueType'))">
                value_type           = #{valueType},
            </if>
            <if test="configValue               != null or (canNullFields != null and canNullFields.contains('configValue'))">
                config_value         = #{configValue},
            </if>
            <if test="ossFileId                 != null or (canNullFields != null and canNullFields.contains('ossFileId'))">
                oss_file_id          = #{ossFileId},
            </if>
            <if test="description               != null or (canNullFields != null and canNullFields.contains('description'))">
                description          = #{description},
            </if>
            <if test="isSensitive               != null or (canNullFields != null and canNullFields.contains('isSensitive'))">
                is_sensitive         = #{isSensitive},
            </if>
            <if test="sort                      != null or (canNullFields != null and canNullFields.contains('sort'))">
                sort                 = #{sort},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_system_config
            <set>
                <if test="groupType       != null">
                    group_type      = #{groupType},
                </if>
                <if test="provider        != null">
                    provider        = #{provider},
                </if>
                <if test="configKey       != null">
                    config_key      = #{configKey},
                </if>
                <if test="valueType       != null">
                    value_type      = #{valueType},
                </if>
                <if test="configValue     != null">
                    config_value    = #{configValue},
                </if>
                <if test="ossFileId       != null">
                    oss_file_id     = #{ossFileId},
                </if>
                <if test="description     != null">
                    description     = #{description},
                </if>
                <if test="isSensitive     != null">
                    is_sensitive    = #{isSensitive},
                </if>
                <if test="sort            != null">
                    sort            = #{sort},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
