<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.user.mapper.AdminUserMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.user.entity.AdminUser">
        <result column="id"                        property="id" />
        <result column="createdTime"              property="createdTime" />
        <result column="createdBy"                property="createdBy" />
        <result column="modifiedTime"             property="modifiedTime" />
        <result column="modifiedBy"               property="modifiedBy" />
        <result column="deletedTime"              property="deletedTime" />
        <result column="deletedBy"                property="deletedBy" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="userBaseId"              property="userBaseId" />
        <result column="realName"                 property="realName" />
        <result column="avatar"                    property="avatar" />
        <result column="email"                     property="email" />
        <result column="phone"                     property="phone" />
        <result column="gender"                    property="gender" />
        <result column="deptId"                   property="deptId" />
        <result column="jobTitle"                 property="jobTitle" />
        <result column="loginIp"                  property="loginIp" />
        <result column="loginTime"                property="loginTime" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 修改人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除（0-否，1-是） -->

            ,t1.user_base_id              AS userBaseId                  <!-- 用户ID（关联tth_user_base） -->
            ,t1.real_name                 AS realName                    <!-- 真实姓名 -->
            ,t1.avatar                    AS avatar                      <!-- 头像URL -->
            ,t1.email                     AS email                       <!-- 电子邮箱 -->
            ,t1.phone                     AS phone                       <!-- 手机号码 -->
            ,t1.gender                    AS gender                      <!-- 性别（0-未知，1-男，2-女） -->
            ,t1.dept_id                   AS deptId                      <!-- 部门ID -->
            ,t1.job_title                 AS jobTitle                    <!-- 职位 -->
            ,t1.login_ip                  AS loginIp                     <!-- 最后登录IP -->
            ,t1.login_time                AS loginTime                   <!-- 最后登录时间 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_admin_user t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.AdminUser">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.user.entity.AdminUser">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.AdminUser">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.user.entity.AdminUser">
        DELETE FROM tth_admin_user
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.user.entity.AdminUser">
        UPDATE tth_admin_user
        <set>
            <if test="userBaseId               != null or (canNullFields != null and canNullFields.contains('userBaseId'))">
                user_base_id        = #{userBaseId},
            </if>
            <if test="realName                  != null or (canNullFields != null and canNullFields.contains('realName'))">
                real_name            = #{realName},
            </if>
            <if test="avatar                    != null or (canNullFields != null and canNullFields.contains('avatar'))">
                avatar               = #{avatar},
            </if>
            <if test="email                     != null or (canNullFields != null and canNullFields.contains('email'))">
                email                = #{email},
            </if>
            <if test="phone                     != null or (canNullFields != null and canNullFields.contains('phone'))">
                phone                = #{phone},
            </if>
            <if test="gender                    != null or (canNullFields != null and canNullFields.contains('gender'))">
                gender               = #{gender},
            </if>
            <if test="deptId                    != null or (canNullFields != null and canNullFields.contains('deptId'))">
                dept_id              = #{deptId},
            </if>
            <if test="jobTitle                  != null or (canNullFields != null and canNullFields.contains('jobTitle'))">
                job_title            = #{jobTitle},
            </if>
            <if test="loginIp                   != null or (canNullFields != null and canNullFields.contains('loginIp'))">
                login_ip             = #{loginIp},
            </if>
            <if test="loginTime                 != null or (canNullFields != null and canNullFields.contains('loginTime'))">
                login_time           = #{loginTime},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_admin_user
            <set>
                <if test="userBaseId      != null">
                    user_base_id    = #{userBaseId},
                </if>
                <if test="realName        != null">
                    real_name       = #{realName},
                </if>
                <if test="avatar          != null">
                    avatar          = #{avatar},
                </if>
                <if test="email           != null">
                    email           = #{email},
                </if>
                <if test="phone           != null">
                    phone           = #{phone},
                </if>
                <if test="gender          != null">
                    gender          = #{gender},
                </if>
                <if test="deptId          != null">
                    dept_id         = #{deptId},
                </if>
                <if test="jobTitle        != null">
                    job_title       = #{jobTitle},
                </if>
                <if test="loginIp         != null">
                    login_ip        = #{loginIp},
                </if>
                <if test="loginTime       != null">
                    login_time      = #{loginTime},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
