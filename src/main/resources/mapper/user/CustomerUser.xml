<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.user.mapper.CustomerUserMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.user.entity.CustomerUser">
        <result column="id"                        property="id" />
        <result column="createdTime"              property="createdTime" />
        <result column="createdBy"                property="createdBy" />
        <result column="modifiedTime"             property="modifiedTime" />
        <result column="modifiedBy"               property="modifiedBy" />
        <result column="deletedTime"              property="deletedTime" />
        <result column="deletedBy"                property="deletedBy" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="userBaseId"                property="userBaseId" />
        <result column="nickname"                  property="nickname" />
        <result column="avatar"                    property="avatar" />
        <result column="gender"                    property="gender" />
        <result column="birthday"                  property="birthday" />
        <result column="region"                    property="region" />
        <result column="phone"                     property="phone" />
        <result column="email"                     property="email" />
        <result column="level"                     property="level" />
        <result column="points"                    property="points" />
        <result column="registerSource"            property="registerSource" />
        <result column="registerIp"                property="registerIp" />
        <result column="lastLoginTime"             property="lastLoginTime" />
        <result column="lastLoginIp"               property="lastLoginIp" />
        <result column="realName"                  property="realName" />
        <result column="idCardNo"                  property="idCardNo" />
        <result column="idCardProfileFileId"       property="idCardProfileFileId" />
        <result column="idCardNationalFileId"      property="idCardNationalFileId" />
        <result column="verifyStatus"              property="verifyStatus" />
        <result column="verifyTime"                property="verifyTime" />
        <result column="verifyFailReason"          property="verifyFailReason" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 修改人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除（0-否，1-是） -->

            ,t1.user_base_id              AS userBaseId                  <!-- 用户ID（关联tth_user_base） -->
            ,t1.nickname                  AS nickname                    <!-- 昵称 -->
            ,t1.avatar                    AS avatar                      <!-- 头像URL -->
            ,t1.gender                    AS gender                      <!-- 性别（0-未知，1-男，2-女） -->
            ,t1.birthday                  AS birthday                    <!-- 生日 -->
            ,t1.region                    AS region                      <!-- 地区 -->
            ,t1.phone                     AS phone                       <!-- 手机号码 -->
            ,t1.email                     AS email                       <!-- 电子邮箱 -->
            ,t1.level                     AS level                       <!-- 用户等级 -->
            ,t1.points                    AS points                      <!-- 积分 -->
            ,t1.register_source           AS registerSource              <!-- 注册来源（1-PC，2-APP，3-小程序等） -->
            ,t1.register_ip               AS registerIp                  <!-- 注册IP -->
            ,t1.last_login_time           AS lastLoginTime               <!-- 最后登录时间 -->
            ,t1.last_login_ip             AS lastLoginIp                 <!-- 最后登录IP -->
            ,t1.real_name                 AS realName                    <!-- 真实姓名 -->
            ,t1.id_card_no                AS idCardNo                    <!-- 身份证号码 -->
            ,t1.id_card_profile_file_id   AS idCardProfileFileId         <!-- 身份证人像面图片ID -->
            ,t1.id_card_national_file_id  AS idCardNationalFileId        <!-- 身份证国徽面图片ID -->
            ,t1.verify_status             AS verifyStatus                <!-- 实名认证状态（0-未认证，1-认证中，2-已认证，3-认证失败） -->
            ,t1.verify_time               AS verifyTime                  <!-- 认证时间 -->
            ,t1.verify_fail_reason        AS verifyFailReason            <!-- 认证失败原因 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_customer_user t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.CustomerUser">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.user.entity.CustomerUser">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.CustomerUser">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.user.entity.CustomerUser">
        DELETE FROM tth_customer_user
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.user.entity.CustomerUser">
        UPDATE tth_customer_user
        <set>
            <if test="userBaseId                != null or (canNullFields != null and canNullFields.contains('userBaseId'))">
                user_base_id         = #{userBaseId},
            </if>
            <if test="nickname                  != null or (canNullFields != null and canNullFields.contains('nickname'))">
                nickname             = #{nickname},
            </if>
            <if test="avatar                    != null or (canNullFields != null and canNullFields.contains('avatar'))">
                avatar               = #{avatar},
            </if>
            <if test="gender                    != null or (canNullFields != null and canNullFields.contains('gender'))">
                gender               = #{gender},
            </if>
            <if test="birthday                  != null or (canNullFields != null and canNullFields.contains('birthday'))">
                birthday             = #{birthday},
            </if>
            <if test="region                    != null or (canNullFields != null and canNullFields.contains('region'))">
                region               = #{region},
            </if>
            <if test="phone                     != null or (canNullFields != null and canNullFields.contains('phone'))">
                phone                = #{phone},
            </if>
            <if test="email                     != null or (canNullFields != null and canNullFields.contains('email'))">
                email                = #{email},
            </if>
            <if test="level                     != null or (canNullFields != null and canNullFields.contains('level'))">
                level                = #{level},
            </if>
            <if test="points                    != null or (canNullFields != null and canNullFields.contains('points'))">
                points               = #{points},
            </if>
            <if test="registerSource            != null or (canNullFields != null and canNullFields.contains('registerSource'))">
                register_source      = #{registerSource},
            </if>
            <if test="registerIp                != null or (canNullFields != null and canNullFields.contains('registerIp'))">
                register_ip          = #{registerIp},
            </if>
            <if test="lastLoginTime             != null or (canNullFields != null and canNullFields.contains('lastLoginTime'))">
                last_login_time      = #{lastLoginTime},
            </if>
            <if test="lastLoginIp               != null or (canNullFields != null and canNullFields.contains('lastLoginIp'))">
                last_login_ip        = #{lastLoginIp},
            </if>
            <if test="realName                  != null or (canNullFields != null and canNullFields.contains('realName'))">
                real_name            = #{realName},
            </if>
            <if test="idCardNo                  != null or (canNullFields != null and canNullFields.contains('idCardNo'))">
                id_card_no           = #{idCardNo},
            </if>
            <if test="idCardProfileFileId       != null or (canNullFields != null and canNullFields.contains('idCardProfileFileId'))">
                id_card_profile_file_id = #{idCardProfileFileId},
            </if>
            <if test="idCardNationalFileId      != null or (canNullFields != null and canNullFields.contains('idCardNationalFileId'))">
                id_card_national_file_id = #{idCardNationalFileId},
            </if>
            <if test="verifyStatus              != null or (canNullFields != null and canNullFields.contains('verifyStatus'))">
                verify_status        = #{verifyStatus},
            </if>
            <if test="verifyTime                != null or (canNullFields != null and canNullFields.contains('verifyTime'))">
                verify_time          = #{verifyTime},
            </if>
            <if test="verifyFailReason          != null or (canNullFields != null and canNullFields.contains('verifyFailReason'))">
                verify_fail_reason   = #{verifyFailReason},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_customer_user
            <set>
                <if test="userBaseId      != null">
                    user_base_id    = #{userBaseId},
                </if>
                <if test="nickname        != null">
                    nickname        = #{nickname},
                </if>
                <if test="avatar          != null">
                    avatar          = #{avatar},
                </if>
                <if test="gender          != null">
                    gender          = #{gender},
                </if>
                <if test="birthday        != null">
                    birthday        = #{birthday},
                </if>
                <if test="region          != null">
                    region          = #{region},
                </if>
                <if test="phone           != null">
                    phone           = #{phone},
                </if>
                <if test="email           != null">
                    email           = #{email},
                </if>
                <if test="level           != null">
                    level           = #{level},
                </if>
                <if test="points          != null">
                    points          = #{points},
                </if>
                <if test="registerSource  != null">
                    register_source = #{registerSource},
                </if>
                <if test="registerIp      != null">
                    register_ip     = #{registerIp},
                </if>
                <if test="lastLoginTime   != null">
                    last_login_time = #{lastLoginTime},
                </if>
                <if test="lastLoginIp     != null">
                    last_login_ip   = #{lastLoginIp},
                </if>
                <if test="realName        != null">
                    real_name       = #{realName},
                </if>
                <if test="idCardNo        != null">
                    id_card_no      = #{idCardNo},
                </if>
                <if test="idCardProfileFileId != null">
                    id_card_profile_file_id = #{idCardProfileFileId},
                </if>
                <if test="idCardNationalFileId != null">
                    id_card_national_file_id = #{idCardNationalFileId},
                </if>
                <if test="verifyStatus    != null">
                    verify_status   = #{verifyStatus},
                </if>
                <if test="verifyTime      != null">
                    verify_time     = #{verifyTime},
                </if>
                <if test="verifyFailReason != null">
                    verify_fail_reason = #{verifyFailReason},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
