<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.user.mapper.PermissionMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.user.entity.Permission">
        <result column="id"                        property="id" />
        <result column="created_time"              property="createdTime" />
        <result column="created_by"                property="createdBy" />
        <result column="modified_time"             property="modifiedTime" />
        <result column="modified_by"               property="modifiedBy" />
        <result column="deleted_time"              property="deletedTime" />
        <result column="deleted_by"                property="deletedBy" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="parent_id"                 property="parentId" />
        <result column="permission_name"           property="permissionName" />
        <result column="permission_code"           property="permissionCode" />
        <result column="permission_type"           property="permissionType" />
        <result column="user_type"                 property="userType" />
        <result column="path"                      property="path" />
        <result column="component"                 property="component" />
        <result column="icon"                      property="icon" />
        <result column="sort"                      property="sort" />
        <result column="status"                    property="status" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 修改人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除（0-否，1-是） -->

            ,t1.parent_id                 AS parentId                    <!-- 父权限ID -->
            ,t1.permission_name           AS permissionName              <!-- 权限名称 -->
            ,t1.permission_code           AS permissionCode              <!-- 权限编码 -->
            ,t1.permission_type           AS permissionType              <!-- 权限类型（1-菜单，2-按钮，3-接口） -->
            ,t1.user_type                 AS userType                    <!-- 用户类型 -->
            ,t1.path                      AS path                        <!-- 路径 -->
            ,t1.component                 AS component                   <!-- 组件 -->
            ,t1.icon                      AS icon                        <!-- 图标 -->
            ,t1.sort                      AS sort                        <!-- 排序 -->
            ,t1.status                    AS status                      <!-- 状态（0-禁用，1-启用） -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_permission t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.Permission">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.user.entity.Permission">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.Permission">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.user.entity.Permission">
        DELETE FROM tth_permission
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.user.entity.Permission">
        UPDATE tth_permission
        <set>
            <if test="parentId                  != null or (canNullFields != null and canNullFields.contains('parentId'))">
                parent_id            = #{parentId},
            </if>
            <if test="permissionName            != null or (canNullFields != null and canNullFields.contains('permissionName'))">
                permission_name      = #{permissionName},
            </if>
            <if test="permissionCode            != null or (canNullFields != null and canNullFields.contains('permissionCode'))">
                permission_code      = #{permissionCode},
            </if>
            <if test="permissionType            != null or (canNullFields != null and canNullFields.contains('permissionType'))">
                permission_type      = #{permissionType},
            </if>
            <if test="userType                  != null or (canNullFields != null and canNullFields.contains('userType'))">
                user_type            = #{userType},
            </if>
            <if test="path                      != null or (canNullFields != null and canNullFields.contains('path'))">
                path                 = #{path},
            </if>
            <if test="component                 != null or (canNullFields != null and canNullFields.contains('component'))">
                component            = #{component},
            </if>
            <if test="icon                      != null or (canNullFields != null and canNullFields.contains('icon'))">
                icon                 = #{icon},
            </if>
            <if test="sort                      != null or (canNullFields != null and canNullFields.contains('sort'))">
                sort                 = #{sort},
            </if>
            <if test="status                    != null or (canNullFields != null and canNullFields.contains('status'))">
                status               = #{status},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_permission
            <set>
                <if test="parentId        != null">
                    parent_id       = #{parentId},
                </if>
                <if test="permissionName  != null">
                    permission_name = #{permissionName},
                </if>
                <if test="permissionCode  != null">
                    permission_code = #{permissionCode},
                </if>
                <if test="permissionType  != null">
                    permission_type = #{permissionType},
                </if>
                <if test="userType        != null">
                    user_type       = #{userType},
                </if>
                <if test="path            != null">
                    path            = #{path},
                </if>
                <if test="component       != null">
                    component       = #{component},
                </if>
                <if test="icon            != null">
                    icon            = #{icon},
                </if>
                <if test="sort            != null">
                    sort            = #{sort},
                </if>
                <if test="status          != null">
                    status          = #{status},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
