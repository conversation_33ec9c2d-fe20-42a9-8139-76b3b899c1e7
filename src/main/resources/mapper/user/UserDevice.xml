<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.user.mapper.UserDeviceMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.user.entity.UserDevice">
        <result column="id"                        property="id" />
        <result column="created_time"              property="createdTime" />
        <result column="created_by"                property="createdBy" />
        <result column="modified_time"             property="modifiedTime" />
        <result column="modified_by"               property="modifiedBy" />
        <result column="deleted_time"              property="deletedTime" />
        <result column="deleted_by"                property="deletedBy" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="user_id"                   property="userId" />
        <result column="device_id"                 property="deviceId" />
        <result column="device_type"               property="deviceType" />
        <result column="device_name"               property="deviceName" />
        <result column="device_model"              property="deviceModel" />
        <result column="os_version"                property="osVersion" />
        <result column="app_version"               property="appVersion" />
        <result column="last_login_time"           property="lastLoginTime" />
        <result column="last_login_ip"             property="lastLoginIp" />
        <result column="token"                     property="token" />
        <result column="token_expire_time"         property="tokenExpireTime" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 修改人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除（0-否，1-是） -->

            ,t1.user_id                   AS userId                      <!-- 用户ID（关联tth_user_base） -->
            ,t1.device_id                 AS deviceId                    <!-- 设备ID -->
            ,t1.device_type               AS deviceType                  <!-- 设备类型（1-PC，2-Android，3-iOS，4-Web等） -->
            ,t1.device_name               AS deviceName                  <!-- 设备名称 -->
            ,t1.device_model              AS deviceModel                 <!-- 设备型号 -->
            ,t1.os_version                AS osVersion                   <!-- 操作系统版本 -->
            ,t1.app_version               AS appVersion                  <!-- 应用版本 -->
            ,t1.last_login_time           AS lastLoginTime               <!-- 最后登录时间 -->
            ,t1.last_login_ip             AS lastLoginIp                 <!-- 最后登录IP -->
            ,t1.token                     AS token                       <!-- 登录令牌 -->
            ,t1.token_expire_time         AS tokenExpireTime             <!-- 令牌过期时间 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_user_device t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.UserDevice">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.user.entity.UserDevice">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.user.entity.UserDevice">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.user.entity.UserDevice">
        DELETE FROM tth_user_device
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.user.entity.UserDevice">
        UPDATE tth_user_device
        <set>
            <if test="userId                    != null or (canNullFields != null and canNullFields.contains('userId'))">
                user_id              = #{userId},
            </if>
            <if test="deviceId                  != null or (canNullFields != null and canNullFields.contains('deviceId'))">
                device_id            = #{deviceId},
            </if>
            <if test="deviceType                != null or (canNullFields != null and canNullFields.contains('deviceType'))">
                device_type          = #{deviceType},
            </if>
            <if test="deviceName                != null or (canNullFields != null and canNullFields.contains('deviceName'))">
                device_name          = #{deviceName},
            </if>
            <if test="deviceModel               != null or (canNullFields != null and canNullFields.contains('deviceModel'))">
                device_model         = #{deviceModel},
            </if>
            <if test="osVersion                 != null or (canNullFields != null and canNullFields.contains('osVersion'))">
                os_version           = #{osVersion},
            </if>
            <if test="appVersion                != null or (canNullFields != null and canNullFields.contains('appVersion'))">
                app_version          = #{appVersion},
            </if>
            <if test="lastLoginTime             != null or (canNullFields != null and canNullFields.contains('lastLoginTime'))">
                last_login_time      = #{lastLoginTime},
            </if>
            <if test="lastLoginIp               != null or (canNullFields != null and canNullFields.contains('lastLoginIp'))">
                last_login_ip        = #{lastLoginIp},
            </if>
            <if test="token                     != null or (canNullFields != null and canNullFields.contains('token'))">
                token                = #{token},
            </if>
            <if test="tokenExpireTime           != null or (canNullFields != null and canNullFields.contains('tokenExpireTime'))">
                token_expire_time    = #{tokenExpireTime},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_user_device
            <set>
                <if test="userId          != null">
                    user_id         = #{userId},
                </if>
                <if test="deviceId        != null">
                    device_id       = #{deviceId},
                </if>
                <if test="deviceType      != null">
                    device_type     = #{deviceType},
                </if>
                <if test="deviceName      != null">
                    device_name     = #{deviceName},
                </if>
                <if test="deviceModel     != null">
                    device_model    = #{deviceModel},
                </if>
                <if test="osVersion       != null">
                    os_version      = #{osVersion},
                </if>
                <if test="appVersion      != null">
                    app_version     = #{appVersion},
                </if>
                <if test="lastLoginTime   != null">
                    last_login_time = #{lastLoginTime},
                </if>
                <if test="lastLoginIp     != null">
                    last_login_ip   = #{lastLoginIp},
                </if>
                <if test="token           != null">
                    token           = #{token},
                </if>
                <if test="tokenExpireTime != null">
                    token_expire_time = #{tokenExpireTime},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
