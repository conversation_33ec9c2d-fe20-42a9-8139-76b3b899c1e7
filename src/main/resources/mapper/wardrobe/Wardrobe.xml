<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tth.modules.wardrobe.mapper.WardrobeMapper">

    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tth.modules.wardrobe.entity.Wardrobe">
        <result column="id"                        property="id" />
        <result column="createdBy"                 property="createdBy" />
        <result column="createdTime"               property="createdTime" />
        <result column="modifiedBy"                property="modifiedBy" />
        <result column="modifiedTime"              property="modifiedTime" />
        <result column="deletedBy"                 property="deletedBy" />
        <result column="deletedTime"               property="deletedTime" />
        <result column="version"                   property="version" />
        <result column="remark"                    property="remark" />
        <result column="deleted"                   property="deleted" />

        <result column="userId"                    property="userId" />
        <result column="wardrobeName"              property="wardrobeName" />
        <result column="closedImageId"             property="closedImageId" />
        <result column="openedImageId"             property="openedImageId" />
        <result column="sort"                      property="sort" />

        <!-- 关联查询衣柜区域 -->
        <collection property="areas" resultMap="com.tth.modules.wardrobe.mapper.WardrobeAreaMapper.BaseResultMap" columnPrefix="area_" />
    </resultMap>

    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
             t1.id                        AS id                          <!-- 主键ID -->
            ,t1.created_by                AS createdBy                   <!-- 创建人 -->
            ,t1.created_time              AS createdTime                 <!-- 创建时间 -->
            ,t1.modified_by               AS modifiedBy                  <!-- 更新人 -->
            ,t1.modified_time             AS modifiedTime                <!-- 修改时间 -->
            ,t1.deleted_by                AS deletedBy                   <!-- 删除人 -->
            ,t1.deleted_time              AS deletedTime                 <!-- 删除时间 -->
            ,t1.version                   AS version                     <!-- 版本号 -->
            ,t1.remark                    AS remark                      <!-- 备注 -->
            ,t1.deleted                   AS deleted                     <!-- 是否删除（0-否，1-是） -->

            ,t1.user_id                   AS userId                      <!-- 用户ID（关联tth_user_base） -->
            ,t1.wardrobe_name             AS wardrobeName                <!-- 衣柜名称 -->
            ,t1.closed_image_id           AS closedImageId               <!-- 衣柜关闭时的图片ID（关联tth_oss_file） -->
            ,t1.opened_image_id           AS openedImageId               <!-- 衣柜打开时的图片ID（关联tth_oss_file） -->
            ,t1.sort                      AS sort                        <!-- 排序 -->

            <!-- 衣柜区域字段 -->
            ,t2.id                        AS area_id                     <!-- 区域ID -->
            ,t2.created_by                AS area_createdBy              <!-- 区域创建人 -->
            ,t2.created_time              AS area_createdTime            <!-- 区域创建时间 -->
            ,t2.modified_by               AS area_modifiedBy             <!-- 区域更新人 -->
            ,t2.modified_time             AS area_modifiedTime           <!-- 区域修改时间 -->
            ,t2.deleted_by                AS area_deletedBy              <!-- 区域删除人 -->
            ,t2.deleted_time              AS area_deletedTime            <!-- 区域删除时间 -->
            ,t2.version                   AS area_version                <!-- 区域版本号 -->
            ,t2.remark                    AS area_remark                 <!-- 区域备注 -->
            ,t2.deleted                   AS area_deleted                <!-- 区域是否删除 -->
            ,t2.wardrobe_id               AS area_wardrobeId             <!-- 区域衣柜ID -->
            ,t2.area_name                 AS area_areaName               <!-- 区域名称 -->
            ,t2.relative_x                AS area_relativeX              <!-- 区域左上角X坐标（百分比） -->
            ,t2.relative_y                AS area_relativeY              <!-- 区域左上角Y坐标（百分比） -->
            ,t2.relative_width            AS area_relativeWidth          <!-- 区域宽度（百分比） -->
            ,t2.relative_height           AS area_relativeHeight         <!-- 区域高度（百分比） -->
            ,t2.pixel_x                   AS area_pixelX                 <!-- 区域原始X坐标（像素） -->
            ,t2.pixel_y                   AS area_pixelY                 <!-- 区域原始Y坐标（像素） -->
            ,t2.pixel_width               AS area_pixelWidth             <!-- 区域原始宽度（像素） -->
            ,t2.pixel_height              AS area_pixelHeight            <!-- 区域原始高度（像素） -->
            ,t2.color                     AS area_color                  <!-- 区域颜色 -->
            ,t2.sort                      AS area_sort                   <!-- 区域排序 -->
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM tth_wardrobe t1
        LEFT JOIN tth_wardrobe_area t2 ON t1.id = t2.wardrobe_id AND t2.deleted = 0

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT #{offset},#{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="com.tth.modules.wardrobe.entity.Wardrobe">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="com.tth.modules.wardrobe.entity.Wardrobe">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="com.tth.modules.wardrobe.entity.Wardrobe">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = #{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="com.tth.modules.wardrobe.entity.Wardrobe">
        DELETE FROM tth_wardrobe
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="com.tth.modules.wardrobe.entity.Wardrobe">
        UPDATE tth_wardrobe
        <set>
            <if test="userId                    != null or (canNullFields != null and canNullFields.contains('userId'))">
                user_id              = #{userId},
            </if>
            <if test="wardrobeName              != null or (canNullFields != null and canNullFields.contains('wardrobeName'))">
                wardrobe_name        = #{wardrobeName},
            </if>
            <if test="closedImageId             != null or (canNullFields != null and canNullFields.contains('closedImageId'))">
                closed_image_id      = #{closedImageId},
            </if>
            <if test="openedImageId             != null or (canNullFields != null and canNullFields.contains('openedImageId'))">
                opened_image_id      = #{openedImageId},
            </if>
            <if test="sort                      != null or (canNullFields != null and canNullFields.contains('sort'))">
                sort                 = #{sort},
            </if>
            remark = #{remark},
            modified_by = #{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = #{id} and deleted = 0 and version = #{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tth_wardrobe
            <set>
                <if test="userId          != null">
                    user_id         = #{userId},
                </if>
                <if test="wardrobeName    != null">
                    wardrobe_name   = #{wardrobeName},
                </if>
                <if test="closedImageId   != null">
                    closed_image_id = #{closedImageId},
                </if>
                <if test="openedImageId   != null">
                    opened_image_id = #{openedImageId},
                </if>
                <if test="sort            != null">
                    sort            = #{sort},
                </if>
                remark = #{remark},
                modified_by = #{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = #{id} and deleted = 0
        </foreach>
    </update>

</mapper>
