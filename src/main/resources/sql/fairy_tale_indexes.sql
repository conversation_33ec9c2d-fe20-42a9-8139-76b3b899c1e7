-- 童话故事表索引优化SQL
-- 解决列表接口全表扫描问题

-- 1. 基础查询索引
-- 发布状态索引（最常用的过滤条件）
CREATE INDEX idx_fairy_tale_publish_status ON tth_fairy_tale(publish_status);

-- 创建时间索引（用于排序）
CREATE INDEX idx_fairy_tale_created_time ON tth_fairy_tale(created_time);

-- 修改时间索引（用于排序）
CREATE INDEX idx_fairy_tale_modified_time ON tth_fairy_tale(modified_time);

-- 2. 复合索引（针对常见查询组合）
-- 发布状态 + 创建时间（最常用的列表查询）
CREATE INDEX idx_fairy_tale_status_created ON tth_fairy_tale(publish_status, created_time DESC);

-- 发布状态 + 英文版需求 + 创建时间
CREATE INDEX idx_fairy_tale_status_english_created ON tth_fairy_tale(
    publish_status, 
    need_english_version, 
    created_time DESC
);

-- 发布状态 + 双语版需求 + 创建时间
CREATE INDEX idx_fairy_tale_status_bilingual_created ON tth_fairy_tale(
    publish_status, 
    need_bilingual_version, 
    created_time DESC
);

-- 3. 收藏相关索引
-- 中文版收藏数索引（用于热门排序）
CREATE INDEX idx_fairy_tale_chinese_favorite ON tth_fairy_tale(chinese_favorite_count DESC);

-- 英文版收藏数索引
CREATE INDEX idx_fairy_tale_english_favorite ON tth_fairy_tale(english_favorite_count DESC);

-- 双语版收藏数索引
CREATE INDEX idx_fairy_tale_bilingual_favorite ON tth_fairy_tale(bilingual_favorite_count DESC);

-- 4. 搜索相关索引
-- 中文标题索引（用于标题搜索）
CREATE INDEX idx_fairy_tale_chinese_title ON tth_fairy_tale(chinese_title);

-- 英文标题索引
CREATE INDEX idx_fairy_tale_english_title ON tth_fairy_tale(english_title);

-- 故事编号索引（唯一查询）
CREATE UNIQUE INDEX idx_fairy_tale_story_number ON tth_fairy_tale(story_number);

-- 5. 状态相关复合索引
-- 翻译状态 + 发布状态
CREATE INDEX idx_fairy_tale_translation_publish ON tth_fairy_tale(
    translation_status, 
    publish_status, 
    created_time DESC
);

-- TTS合成状态索引（用于任务查询）
CREATE INDEX idx_fairy_tale_chinese_title_tts ON tth_fairy_tale(chinese_title_tts_status);
CREATE INDEX idx_fairy_tale_chinese_content_tts ON tth_fairy_tale(chinese_content_tts_status);
CREATE INDEX idx_fairy_tale_english_title_tts ON tth_fairy_tale(english_title_tts_status);
CREATE INDEX idx_fairy_tale_english_content_tts ON tth_fairy_tale(english_content_tts_status);

-- 6. 逻辑删除索引（如果使用逻辑删除）
-- 假设有deleted字段
-- CREATE INDEX idx_fairy_tale_deleted_status_created ON tth_fairy_tale(deleted, publish_status, created_time DESC);

-- 7. 分页优化索引
-- 针对大数据量分页的覆盖索引
CREATE INDEX idx_fairy_tale_page_cover ON tth_fairy_tale(
    publish_status,
    created_time DESC,
    id,
    story_number,
    chinese_title,
    english_title,
    chinese_favorite_count,
    english_favorite_count,
    bilingual_favorite_count
);

-- 8. 查看索引使用情况的SQL
-- 查看表的索引
-- SHOW INDEX FROM tth_fairy_tale;

-- 分析查询执行计划
-- EXPLAIN SELECT * FROM tth_fairy_tale WHERE publish_status = 1 ORDER BY created_time DESC LIMIT 20;

-- 查看索引使用统计
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     SEQ_IN_INDEX,
--     COLUMN_NAME,
--     CARDINALITY
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = 'your_database_name' 
--   AND TABLE_NAME = 'tth_fairy_tale'
-- ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
