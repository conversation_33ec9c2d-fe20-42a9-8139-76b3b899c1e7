package ${package.Controller};

import org.springframework.web.bind.annotation.RequestMapping;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.tags.Tag;
import ${package.Service}.${table.serviceName};
import ${package.Entity}.${entity};
import lombok.extern.slf4j.Slf4j;
<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
import ${superServiceClassPackage};
</#if>

/**
 * <p>
 * ${table.comment!} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
<#assign entityName = entity?replace("Entity", "")>
<#assign entityLower = entityName?uncap_first>
@Tag(name = "${table.comment}接口【${entityLower}】", description = "权限前缀：【${entityLower}】")
@RequestMapping("<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle>${controllerMappingHyphenStyle}<#else>${entityLower}</#if>")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass}<${entity}> {
<#else>
public class ${table.controllerName} {
</#if>

    @Resource
    private ${table.serviceName} ${entityLower}Service;

    @Override
    protected String getModuleName() {
        return "${entityLower}";
    }

    @Override
    public BaseService<${entity}> getBaseService() {
    return ${entityLower}Service;
    }
}
</#if>
