<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package.Mapper}.${table.mapperName}">

<#if enableCache>
    <!-- 开启二级缓存 -->
    <cache type="${cacheClassName}"/>

</#if>
<#if baseResultMap>
    <!-- 全表查询映射结果 -->
    <resultMap id="BaseResultMap" type="${package.Entity}.${entity}">
    <#list table.commonFields as field>
        <#assign padLength = 35 - field.name?length>
        <result column="${field.propertyName}"<#if padLength gt 0>${""?right_pad(padLength)}</#if> property="${field.propertyName}" />
    </#list>

    <#list table.fields as field>
        <#assign padLength = 35 - field.name?length>
        <result column="${field.propertyName}"<#if padLength gt 0>${""?right_pad(padLength)}</#if> property="${field.propertyName}" />
    </#list>
    </resultMap>
</#if>

<#if baseColumnList>
    <!-- 查询字段的select部分 -->
    <sql id="queryAllSql">
        SELECT
    <#list table.commonFields as field>
        <#if field.keyFlag>
             t1.${field.name?right_pad(25)} AS ${field.propertyName?right_pad(25)}   <!-- ${field.comment} -->
        </#if>
        <#if !field.keyFlag>
            ,t1.${field.name?right_pad(25)} AS ${field.propertyName?right_pad(25)}   <!-- ${field.comment} -->
        </#if>
    </#list>

    <#list table.fields as field>
        <#if !field.keyFlag>
            ,t1.${field.name?right_pad(25)} AS ${field.propertyName?right_pad(25)}   <!-- ${field.comment} -->
        </#if>
    </#list>
    </sql>

    <!-- 查询字段的from部分 -->
    <sql id="fromTableSql">
        FROM ${table.name} t1

        WHERE t1.deleted = 0
    </sql>

    <!-- 查询字段的where部分 若使用ew 则包含order条件 和 limit条件 group by 条件-->
    <sql id="whereAllSql">
        <!-- 特别注意 只允许使用Wrapper作为参数控制 注意 如果存在复杂的条件查询 会导致SQL语句异常 -->
        <if test="ew != null">
            ${"$"}{ew.customSqlSegment.replaceFirst('WHERE ', 'AND ')}
        </if>
    </sql>

    <!-- 分页部分 -->
    <sql id="limitSql">
        <!--   分页参数   -->
        <if test="offset != null and count != null">
            LIMIT ${"#"}{offset},${"#"}{count};
        </if>
    </sql>

    <!-- 根据内容查询且分页 -->
    <select id="queryByPage" resultMap="BaseResultMap" parameterType="${package.Entity}.${entity}">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
        <include refid="limitSql"/>
    </select>

    <!-- 统计总数 -->
    <select id="queryCount" resultType="java.lang.Long" parameterType="${package.Entity}.${entity}">
        SELECT COUNT(DISTINCT t1.id)
        <include refid="fromTableSql"/>
        <include refid="whereAllSql"/>
    </select>

    <!-- 根据ID进行查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="${package.Entity}.${entity}">
        <include refid="queryAllSql"/>
        <include refid="fromTableSql"/>
        AND t1.id = ${"#"}{id}
    </select>

    <!-- 自定义删除-物理删除(危险操作) -->
    <delete id="deleteCustom" parameterType="${package.Entity}.${entity}">
        DELETE FROM ${table.name}
        <where>
            <!-- 如果 ew 为 null，则默认不删除 1=2 永远为false，避免误删 -->
            <if test="ew == null or ew.sqlSegment == null or ew.sqlSegment == ''">
                1 = 2
            </if>
            <!-- 如果 ew 不为 null，执行删除操作 -->
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                <trim prefixOverrides="AND | OR">
                    ${"$"}{ew.sqlSegment}
                </trim>
            </if>
        </where>
    </delete>

    <!-- 根据ID更新记录，支持通过canNullFields指定需要更新的字段（包括null值） -->
    <update id="updateByIdSelective" parameterType="${package.Entity}.${entity}">
        UPDATE ${table.name}
        <set>
    <#list table.fields as field>
        <#if !field.keyFlag>
            <if test="${field.propertyName?right_pad(25)} != null or (canNullFields != null and canNullFields.contains('${field.propertyName}'))">
                ${field.name?right_pad(20)} = ${"#"}{${field.propertyName}},
            </if>
        </#if>
    </#list>
            remark = ${"#"}{remark},
            modified_by = ${"#"}{modifiedBy},
            modified_time = NOW(),
            version = version + 1
        </set>
        WHERE id = ${"#"}{id} and deleted = 0 and version = ${"#"}{version}
    </update>

    <!-- 根据ID批量更新记录 -->
    <update id="updateBatchByIdSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE ${table.name}
            <set>
        <#list table.fields as field>
            <#if !field.keyFlag>
                <if test="${field.propertyName?right_pad(15)} != null">
                    ${field.name?right_pad(15)} = ${"#"}{${field.propertyName}},
                </if>
            </#if>
        </#list>
                remark = ${"#"}{remark},
                modified_by = ${"#"}{modifiedBy},
                modified_time = NOW()
            </set>
            WHERE id = ${"#"}{id} and deleted = 0
        </foreach>
    </update>

</#if>
</mapper>
